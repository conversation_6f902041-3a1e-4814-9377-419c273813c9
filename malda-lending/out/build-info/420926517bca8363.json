{"id": "420926517bca8363", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/StdAssertions.sol", "2": "lib/forge-std/src/StdChains.sol", "3": "lib/forge-std/src/StdCheats.sol", "4": "lib/forge-std/src/StdConstants.sol", "5": "lib/forge-std/src/StdError.sol", "6": "lib/forge-std/src/StdInvariant.sol", "7": "lib/forge-std/src/StdJson.sol", "8": "lib/forge-std/src/StdMath.sol", "9": "lib/forge-std/src/StdStorage.sol", "10": "lib/forge-std/src/StdStyle.sol", "11": "lib/forge-std/src/StdToml.sol", "12": "lib/forge-std/src/StdUtils.sol", "13": "lib/forge-std/src/Test.sol", "14": "lib/forge-std/src/Vm.sol", "15": "lib/forge-std/src/console.sol", "16": "lib/forge-std/src/console2.sol", "17": "lib/forge-std/src/interfaces/IMulticall3.sol", "18": "lib/forge-std/src/safeconsole.sol", "19": "lib/openzeppelin-contracts-upgradeable/contracts/access/OwnableUpgradeable.sol", "20": "lib/openzeppelin-contracts-upgradeable/contracts/proxy/utils/Initializable.sol", "21": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ContextUpgradeable.sol", "22": "lib/openzeppelin-contracts-upgradeable/contracts/utils/ReentrancyGuardUpgradeable.sol", "23": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "24": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "25": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Proxy.sol", "26": "lib/openzeppelin-contracts/contracts/proxy/ERC1967/ERC1967Utils.sol", "27": "lib/openzeppelin-contracts/contracts/proxy/Proxy.sol", "28": "lib/openzeppelin-contracts/contracts/proxy/beacon/IBeacon.sol", "29": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "30": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "31": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "32": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "33": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "34": "lib/openzeppelin-contracts/contracts/utils/Address.sol", "35": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "36": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "37": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "38": "lib/risc0-ethereum/contracts/src/IRiscZeroVerifier.sol", "39": "lib/risc0-ethereum/contracts/src/Util.sol", "40": "lib/risc0-ethereum/contracts/src/steel/Steel.sol", "41": "src/Operator/Operator.sol", "42": "src/Operator/OperatorStorage.sol", "43": "src/Roles.sol", "44": "src/blacklister/Blacklister.sol", "45": "src/interest/JumpRateModelV4.sol", "46": "src/interfaces/IBlacklister.sol", "47": "src/interfaces/IGasFeesHelper.sol", "48": "src/interfaces/IInterestRateModel.sol", "49": "src/interfaces/IOperator.sol", "50": "src/interfaces/IOracleOperator.sol", "51": "src/interfaces/IRewardDistributor.sol", "52": "src/interfaces/IRoles.sol", "53": "src/interfaces/ImErc20.sol", "54": "src/interfaces/ImErc20Host.sol", "55": "src/interfaces/ImToken.sol", "56": "src/interfaces/ImTokenGateway.sol", "57": "src/interfaces/external/poh/IPohVerifier.sol", "58": "src/libraries/BytesLib.sol", "59": "src/libraries/CommonLib.sol", "60": "src/libraries/mTokenProofDecoderLib.sol", "61": "src/mToken/BatchSubmitter.sol", "62": "src/mToken/extension/mTokenGateway.sol", "63": "src/mToken/host/mErc20Host.sol", "64": "src/mToken/mErc20.sol", "65": "src/mToken/mErc20Immutable.sol", "66": "src/mToken/mErc20Upgradable.sol", "67": "src/mToken/mToken.sol", "68": "src/mToken/mTokenConfiguration.sol", "69": "src/mToken/mTokenStorage.sol", "70": "src/migration/IMigrator.sol", "71": "src/migration/Migrator.sol", "72": "src/rewards/RewardDistributor.sol", "73": "src/utils/ExponentialNoError.sol", "74": "src/verifier/ZkVerifier.sol", "75": "test/Base_Unit_Test.t.sol", "76": "test/mocks/ERC20Mock.sol", "77": "test/mocks/OracleMock.sol", "78": "test/mocks/Risc0VerifierMock.sol", "79": "test/unit/mErc20/mErc20_borrow.t.sol", "80": "test/unit/shared/mToken_Unit_Shared.t.sol", "81": "test/utils/Constants.sol", "82": "test/utils/Events.sol", "83": "test/utils/Helpers.sol", "84": "test/utils/Types.sol"}, "language": "Solidity"}