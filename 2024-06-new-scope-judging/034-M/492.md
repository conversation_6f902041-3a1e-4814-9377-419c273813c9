Careful Fleece Pike

Medium

# Setting a new configurator in `PoolFactory` will cause the function `setReserveConfiguration` of the pools unusable

### Summary

`setReserveConfiguration` checks the configurator from the `PoolFactory` will cause when setting a new configurator in `PoolFactory`, the function `setReserveConfiguration` will be unusable.

### Root Cause

`setReserveConfiguration` checks the configurator from the `PoolFactory`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L150-L158

```solidity
  function setReserveConfiguration(
    address asset,
    address rateStrategy,
    address source,
    DataTypes.ReserveConfigurationMap calldata config
  ) external virtual {
>>  require(msg.sender == address(_factory.configurator()), 'only configurator');
    PoolLogic.setReserveConfiguration(_reserves, asset, rateStrategy, source, config);
  }
```

When the `PoolFactory` set a new configurator, all the permissioned roles from the old configurator are not carried over the new configurator, which will cause the permissioned roles can not interact with the new configurator to call to `setReserveConfiguration`. Moreover, the roles for a pool is only set during the initialization of the pool, because of that, the permissioned roles can not be added to the new configurator.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Admin sets a new configurator.

### Impact

All the governance parameters of a pool can not be updated.

### PoC

_No response_

### Mitigation

The configurator of a pool should be a storage variable, which is set during the initialization. When the `setReserveConfiguration` function is called, `msg.sender` should be checked against that storage variable.