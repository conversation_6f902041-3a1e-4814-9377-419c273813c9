Curly Pineapple Armadillo

Medium

# Liquidation protocol fee is not considered when calculating interest rates after liquidations

### Summary

When a liquidation occurs and interest rates of the collateral reserve are updated, the calculation of the new interest rates does not consider that liquidation protocol fees will also be withdrawn. This causes the collateral reserve interest rates to be higher than intended.

### Root Cause

In [`_burnCollateralTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L216-L225) when `updateInterestRates` is called, the value for the `_liquidityTaken` parameter is set to `vars.actualCollateralToLiquidate`. This is an issue as it does not include the value of the liquidation protocol fee, transferred to the treasury.
Those fees also need to be a part of `_liquidityTaken` as `IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress).calculateInterestRates` will calculate the new interest rate based on the pool's balance of the asset. As the fee transfer is performed after the rates are updated, the asset balance of the pool will be incorrectly inflated by the value of the fees, causing `calculateInterestRates` to return a higher value and interest rates to be higher than intended.

### Internal pre-conditions

1. `liquidationProtocolFeePercentage` must be more than 0.

### External pre-conditions

_No response_

### Attack Path

1. A liquidation occurs with `actualCollateralToLiquidate` equal to 10 ETH and `liquidationProtocolFeeAmount` equal to 1 ETH.
2. In order to calculate what the collateral reserve interest rates should be, `updateInterestRates` is called in `_burnCollateralTokens`. There the pool's balance of the asset is inflated by 1 ETH of liquidation fees which are going to be transferred from the pool to the treasury after `_burnCollateralTokens` has been fully executed.
3. Interest rates are wrongly calculated and are more than intended as `_liquidityTaken` does not include the 1 ETH of liquidation fees.

### Impact

As a result, when a liquidation occurs interest rates will be higher than what the protocol intends.

### PoC

_No response_

### Mitigation

In [`_burnCollateralTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L216-L225) when `updateInterestRates` is called, the value for the `_liquidityTaken` parameter should be set to `vars.actualCollateralToLiquidate` + the number of liquidation fees.