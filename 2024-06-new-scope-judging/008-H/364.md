Chilly Cherry Deer

High

# Lack of fee retrieval &  not assigned to `vars.liquidationProtocolFeePercentage`, then `liquidationProtocolFeePercentage` is used to determine `vars.liquidationProtocolFee`,  this logic is executed with a default=0, leads to protocol getting zero amount of fee on liquidation.

### Summary

The lack of fee retrieval & not assigned to `vars.liquidationProtocolFeePercentage`  then execution of `liquidationProtocolFeePercentage` in `_calculateAvailableCollateralToLiquidate`  will cause a complete loss of funds for the protocol as the system will default to zero leads to incorrect fee application, resulting in the protocol not collecting the fees during liquidations.


### Root Cause

In  [LiquidationLogic.sol#L366-L373](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L366-L373) the `_calculateAvailableCollateralToLiquidate` function does not internally retrieve &  assigned to `vars.liquidationProtocolFeePercentage`.  Due to lack of internal fee retrieval &  not assigned to `vars.liquidationProtocolFeePercentage` , results the function could operate with a default=0 & incorrect fee percentage, leading to inaccurate fee calculations during liquidations.`

```solidity
   function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    AvailableCollateralToLiquidateLocalVars memory vars;

    vars.collateralPrice = collateralPrice; // oracle.getAssetPrice(collateralAsset);
    vars.debtAssetPrice = debtAssetPrice; // oracle.getAssetPrice(debtAsset);

    vars.collateralDecimals = collateralReserve.configuration.getDecimals();
    vars.debtAssetDecimals = debtReserveCache.reserveConfiguration.getDecimals();

    unchecked {
      vars.collateralAssetUnit = 10 ** vars.collateralDecimals;
      vars.debtAssetUnit = 10 ** vars.debtAssetDecimals;
    }
      
    // @audit  // lack of fee retrieval &  not assigned to `vars.liquidationProtocolFeePercentage` , results the function could operate with a default=0 & incorrect fee percentage

    // This is the base collateral to liquidate based on the given debt to cover
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
      vars.debtAmountNeeded = (
        (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }

     //  @audit-issue liquidationProtocolFeePercentage is used to determine vars.liquidationProtocolFee, hence this logic is executed with a default=0, leads to protocol getting zero amount of fee on liquidation.
    if (liquidationProtocolFeePercentage != 0) {
      vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
      vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);
      return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
    } else {
      return (vars.collateralAmount, vars.debtAmountNeeded, 0);
    }
  }
```
- Hence,  lack of fee retrieval &  not assigned to `vars.liquidationProtocolFeePercentage`, and then `liquidationProtocolFeePercentage` is used to determine `vars.liquidationProtocolFee`,  this logic is executed with a default=0, leads to protocol getting zero amount of fee on liquidation.

### Impact

Complete loss of funds - resulting in the protocol not collecting the fees during liquidations.


### Mitigation

-  retrieve the liquidation protocol fee percentage from `IPoolDataProvider.sol` which have logic `function getLiquidationProtocolFee(address pool, address asset) external view returns (uint256);` 
-  or add the logic  before execution of `liquidationProtocolFeePercentage` like this and modify reserve configuration  accordingly.
```solidity
vars.liquidationProtocolFeePercentage = collateralReserve.configuration .getLiquidationProtocolFee();
```