Acrobatic Banana Poodle

Medium

# Liquidated User will not be charged for the liquidation

### Summary

The incorrect handling of the fee in the `LiquidationLogic::executeLiquidationCall` will cause the following issues:

1. The liquidated user will not be charged for the fee
2. `Pool._totalSupplies[collateralAsset].underlyingBalance` will be incorrect.

### Root Cause

In the `LiquidationLogic::executeLiquidationCall` the fee was handled incorrectly.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L188

After the fee amount is calculated, the collateralAsset was simply transferred out from the pool to the treasury.
The balance of user, the underlyingBalance were not updated.
As the result, the user will not be charged for the fee and the underlying balance for the collateral asset is outdated.
The collateral asset will be sent out to the treasury, the user's collateral balance is not subtracted for the fee, so the user can withdraw.

Also, the userConfig.setUsingAsCollateral was updated considering the fee, so userConfig and the users balance may have discrepancy, which may lead to potential problem.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L156-L157

Note: the difference to the AaveV3

https://github.com/aave/aave-v3-core/blob/6d6fa53d360b43f492ff5b3c7033f95aee4f1335/contracts/protocol/libraries/logic/LiquidationLogic.sol#L213-L217

Aave uses `collateralAToken.transferOnLiquidation`, which will transfer the aToken from the user to the treasury, therefore the user's collateral a token value will be correctly reduced. Also the underlying balance will stay in the aToken contract, therefore no need to update the virtual balance.

### Internal pre-conditions

- When the `liquidationProtocolFeePercentage` is set to non-zero and would have result in non-zero fee for the liquidation
- No additional/malicious action from any actor is required

### External pre-conditions

- No additional/malicious action from any actor is required

### Attack Path

1. Anytime a liquidator initiates a liquidation call (no need for any malicious intention), a fee would not be charged to the user even if the protocol wanted to charge.

### impact

1. The liquidated user will not be charged for the fee
2. `Pool._totalSupplies[collateralAsset].underlyingBalance` will be incorrect.

### PoC

_No response_

### Mitigation

One possible mitigation is to update the user's collateral balance, totalSupply for the collateral in the fee block. Then probably the interest rate also should be updated, which will also update the underlying collateral.

Alternatively, the `_burnCollateralTokens` can be updated to withdraw the `actualCollateralToLiquidate` + fee. But then, the `vars.liquidationProtocolFeeAmount` should be updated before the `_burnCollateralTokens` is called.
