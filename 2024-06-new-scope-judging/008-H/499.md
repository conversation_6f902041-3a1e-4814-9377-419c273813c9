Massive Glass Crane

High

# Pool.sol:liquidate()::LiquidityTaken is not considering the liquidationProtocolFeeAmount while calling updateInterestRate()

### Summary

when a liquidator liquidates a position using a collateral , some amount of the collaterals is transferred to `treasury` as `liquidationProtocolFeeAmount`.

Even though `liquidationProtocolFeeAmount` is taken from the collateral assets reserve there is no attempt made to update the `interestRate` of the same reserve for the `liquidationProtocolFeeAmount` but instead only the `actualCollateralToLiquidate` is considered. Where the `actualCollateralToLiquidate` is 
the final amount that is sent to the liquidator after deducing `liquidationProtocolFeeAmount`,


### Root Cause

The liquidator invokes executeLiquidationCall() .

When the the execution reaches the [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L174) `_burnCollateralTokens()` , 
`vars.actualCollateralToLiquidate` contains the amount that is sent to the liquidator
`vars.liquidationProtocolFeeAmount` contains the amount that is sent to the treasury.

Now , we are burning the total collateralTokens that are being sent from the reserves using the function `_burnCollateralTokens()`. 

```solidity
  function _burnCollateralTokens(
....
    LiquidationCallLocalVars memory vars,
....
  ) internal {
...
    collateralReserve.updateInterestRates(
      totalSupplies,
      collateralReserveCache,
      params.collateralAsset,
      IPool(params.pool).getReserveFactor(),
      0,
=>    vars.actualCollateralToLiquidate,
      params.position,
      params.data.interestRateData
    );
....
    IERC20(params.collateralAsset).safeTransfer(msg.sender, vars.actualCollateralToLiquidate);
  }
  ```

  Here [LiquidationLogic.sol#L222](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L222) , we are passing  only `vars.actualCollateralToLiquidate` as the parameter `liquidityTaken`  instead of `vars.actualCollateralToLiquidate` + `vars.liquidationProtocolFeeAmount` while calling `updateInterestRate()`.

  Hence the new `Interest rate` is calculated [incorrectly](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L160-L171) and also `totalSuplly.underlyingBalance`  also  reflects the [incorrect](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L177) value.


  Protocol is also burning only the  shares for the amount `vars.actualCollateralToLiquidate` from the reserves `totalShare` instead of the entire amount taken from the reserve.[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L228)

### Internal pre-conditions
WHen a liquidator executes a liquidation


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

InterestRates are calculated inclduing the liquidationProtocolFees but in reality the amount is taken out from the reserve and sent to the treasury. Hence interest rate is incorrectly calculated affecting the liquidityIndex which inturn inflicts losses to the lenders.

### PoC


### Mitigation
```solidity

    collateralReserve.updateInterestRates(
      totalSupplies,
      collateralReserveCache,
      params.collateralAsset,
      IPool(params.pool).getReserveFactor(),
      0,
=>    vars.actualCollateralToLiquidate + vars.liquidationProtocolFeeAmount,
      params.position,
      params.data.interestRateData
    );
    balances.withdrawCollateral(totalSupplies, vars.actualCollateralToLiquidate+ vars.liquidationProtocolFeeAmount, collateralReserveCache.nextLiquidityIndex);

    ```