Curly Pineapple Armadillo

High

# The liquidation protocol fee is not reduced from the total supply shares, allowing liquidated borrowers to steal from other users

### Summary

In `executeLiquidationCall` when collateral is withdrawn from the liquidated borrower and transferred to the liquidator the liquidation fee shares are not removed from the liquidated borrower's share balance. As a result,  the liquidated borrower will have access to the fee shares, allowing them to withdraw assets that do not belong to them, essentially stealing from suppliers.

### Root Cause

- In [_burnCollateralTokens](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L228), when `withdrawCollateral` is called, the `amount` parameter, which needs to represent the total collateral withdrawn from the liquidated user, is only set to `vars.actualCollateralToLiquidate`. This is an issue as `vars.liquidationProtocolFeeAmount` is also taken from the liquidated user's collateral, but the shares that the fee amount is worth are not removed from the collateral balance of the liquidated user.

### Internal pre-conditions

1. `liquidationProtocolFeePercentage` must be more than 0.

### External pre-conditions

_No response_

### Attack Path

1. A liquidation occurs with `actualCollateralToLiquidate` equal to 10 ETH and `liquidationProtocolFeeAmount` equal to 1 ETH.
2. 10 ETH worth of shares are removed from the liquidated user's supply balance when `withdrawCollateral` is called.
3. However the 1 ETH of fees, which are also a part of the liquidatee's collateral, are transferred to the treasury, but 1 ETH worth of shares are not removed from the liquidated user's balance.
4. As a result the liquidated user has access to 1 ETH worth of shares that do not belong to them anymore, and if they were withdrawn, the amount would be taken out of other innocent suppliers.

### Impact

Funds will be stolen from innocent suppliers when a liquidation occurs.

### PoC

_No response_

### Mitigation

In [_burnCollateralTokens](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L228), when `withdrawCollateral` is called, the `amount` parameter must be set to `vars.actualCollateralToLiquidate` + the number of liquidation fees.