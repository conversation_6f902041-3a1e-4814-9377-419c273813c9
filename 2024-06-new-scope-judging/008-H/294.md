Smooth Carbon Narwhal

High

# The protocol fee is not being deducted from the liquidatee's balance during liquidation

## Summary
When a user’s `position` is `liquidated`, they are required to send a portion of their `collateral` to both the `liquidator` and the `protocol` as a `fee`. 
This should reduce the `liquidatee`'s `collateral balance`. 
However, the `protocol fee` is not being deducted from the `liquidatee`’s `balance`, allowing them to `withdraw` these `shares` again, even though they have already been transferred to the `protocol` as `fees`.
## Vulnerability Detail
The `liquidation` process occurs in the `executeLiquidationCall` function. 
On `line 138`, the `_calculateAvailableCollateralToLiquidate` function is called to determine how much `debt` should be liquidated and how much `collateral` both the `liquidator` and the `protocol` will receive.
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
138: (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
  _calculateAvailableCollateralToLiquidate(
    collateralReserve,
    vars.debtReserveCache,
    vars.actualDebtToLiquidate,
    vars.userCollateralBalance,
    vars.liquidationBonus,
    IPool(params.pool).getAssetPrice(params.collateralAsset),
    IPool(params.pool).getAssetPrice(params.debtAsset),
    IPool(params.pool).factory().liquidationProtocolFeePercentage()
  );
}
```
If the `liquidationProtocolFeePercentage` is greater than `0`, a portion of the `collateral` is assigned to the `protocol` as a `fee` on `line 368`. 
```solidity
function _calculateAvailableCollateralToLiquidate(
  DataTypes.ReserveData storage collateralReserve,
  DataTypes.ReserveCache memory debtReserveCache,
  uint256 debtToCover,
  uint256 userCollateralBalance,
  uint256 liquidationBonus,
  uint256 collateralPrice,
  uint256 debtAssetPrice,
  uint256 liquidationProtocolFeePercentage
) internal view returns (uint256, uint256, uint256) {
  if (liquidationProtocolFeePercentage != 0) {
    vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
    
368:    vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);

    return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
  } else {
    return (vars.collateralAmount, vars.debtAmountNeeded, 0);
  }
}
```
The `collateral` allocated to the `liquidator` is sent to them, and the `liquidatee`’s `collateral balance` is reduced accordingly in `line 228`. 
```solidity
function _burnCollateralTokens(
  DataTypes.ReserveData storage collateralReserve,
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  DataTypes.PositionBalance storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
) internal {
228:  balances.withdrawCollateral(totalSupplies, vars.actualCollateralToLiquidate, collateralReserveCache.nextLiquidityIndex);

  IERC20(params.collateralAsset).safeTransfer(msg.sender, vars.actualCollateralToLiquidate);
}
```
However, while the `protocol`'s `fee` is sent to the `treasury`, the `liquidatee`'s `collateral balance` is not reduced on `line 188`.
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
138: (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
  _calculateAvailableCollateralToLiquidate(
    collateralReserve,
    vars.debtReserveCache,
    vars.actualDebtToLiquidate,
    vars.userCollateralBalance,
    vars.liquidationBonus,
    IPool(params.pool).getAssetPrice(params.collateralAsset),
    IPool(params.pool).getAssetPrice(params.debtAsset),
    IPool(params.pool).factory().liquidationProtocolFeePercentage()
  );
  
  if (vars.liquidationProtocolFeeAmount != 0) {
188:   IERC20(params.collateralAsset).safeTransfer(
IPool(params.pool).factory().treasury(), vars.liquidationProtocolFeeAmount);  
  }
}
```
## Impact
This allows `liquidatees` to unfairly benefit, as their `collateral balance` does not reflect the `fee` paid to the `protocol`. 
This issue can lead to several problems.
For instance, users may be unable to `withdraw` their funds due to an insufficient `asset balance`, as the reduction in the `total pool assets` is not properly tracked. 
Additionally, because the `liquidatees`’ actual losses are less than intended, they may not take the `liquidation` process seriously, undermining the protocol's intended risk management.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L138-L148
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L368
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L228
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L188
## Tool used

Manual Review

## Recommendation
To resolve this issue, deduct the `protocol fee` from the `liquidatee`'s `balance` and the `total shares` by calling the `withdrawCollateral` function.