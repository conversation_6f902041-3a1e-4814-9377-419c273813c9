Clever Ebony Halibut

High

# userbalance not reduced by `liquidationProtocolFeeAmount`

## Summary
The protocol takes a `liquidationProtocolFeeAmount` from the bonus seized collateral in liquidation, but this amount is not deducted from the user's balance, and still sent to the treasury. This issue leads to breaking the internal accounting of the protocol and incorrect liquidation process. 

## Vulnerability Detail

The Protocol takes a `liquidationProtocolFeeAmount` from the bonus seized collateral in liquidation. the exact amount is calculated in `_calculateAvailableCollateralToLiquidate` function:
```solidity
  function _calculateAvailableCollateralToLiquidate(
  // some code . .
    if (liquidationProtocolFeePercentage != 0) {
      vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
      vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);
@>>      return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
    } else {
      return (vars.collateralAmount, vars.debtAmountNeeded, 0);
    }
  }
}
```
As we can see  the `actualCollateralToLiquidate` calculated using `_calculateAvailableCollateralToLiquidate()` doesn't include the `liquidationProtocolFeeAmount`


```solidity
   (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) = 
 _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance, 
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```
**The problem however arises from the fact the user balance and the total supply is only reduced by `actualCollateralToLiquidate` (not including the `liquidationProtocolFeeAmount`)**

**And as we can see later we will transfer the `liquidationProtocolFeeAmount` to the treasury, but this amount is never reduced from the user balance or the total supply in the protocol.**
```solidity
    //some code ..
 @>>   _burnCollateralTokens(collateralReserve, params, vars, balances[params.collateralAsset][params.position], 
 totalSupplies[params.collateralAsset]);

    // Transfer fee to treasury if it is non-zero
    if (vars.liquidationProtocolFeeAmount != 0) {
      uint256 liquidityIndex = collateralReserve.getNormalizedIncome();
      uint256 scaledDownLiquidationProtocolFee = vars.liquidationProtocolFeeAmount.rayDiv(liquidityIndex);
      uint256 scaledDownUserBalance = balances[params.collateralAsset][params.position].supplyShares;

      if (scaledDownLiquidationProtocolFee > scaledDownUserBalance) {
        vars.liquidationProtocolFeeAmount = scaledDownUserBalance.rayMul(liquidityIndex);
      }

@>>      IERC20(params.collateralAsset).safeTransfer(IPool(params.pool).factory().treasury(), vars.liquidationProtocolFeeAmount);
    }
```

just for reference this is how the balance is reduced in `_burnCollateralTokens` function:

```solidity
function _burnCollateralTokens(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    DataTypes.PositionBalance storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    DataTypes.ReserveCache memory collateralReserveCache = collateralReserve.cache(totalSupplies);
// ......
@audit-issue not reduced by the `liquidationProtocolFeeAmount` but with the `actualCollateralToLiquidate` that doesn't include the fee
 @>>   balances.withdrawCollateral(totalSupplies, vars.actualCollateralToLiquidate, collateralReserveCache.nextLiquidityIndex);
    IERC20(params.collateralAsset).safeTransfer(msg.sender, vars.actualCollateralToLiquidate);
  }
```
the balances and totalSupply as seen in the code are not reduced by the `liquidationProtocolFeeAmount` but with the `actualCollateralToLiquidate` that doesn't include the fee.
## Impact
This bug can lead to insolvency issues, allowing to spend the `liquidationProtocolFeeAmount` twice from the protocol reserves and breaking the accounting of the protocol.
The issue also leads to a loss of funds for the protocol as the violator will be able to withdraw the `liquidationProtocolFeeAmount` from the protocol reserves.(they don't get reduced from his balance)

## Code Snippet
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L138C1-L176C7
h- ttps://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L228

## Tool used

Manual Review

## Recommendation
To solve this issue we need to reduce the `liquidationProtocolFeeAmount` from the user balance and the total supply in the protocol.


```diff
   _burnCollateralTokens(collateralReserve, params, vars, balances[params.collateralAsset][params.position], totalSupplies[params.collateralAsset]);

    // Transfer fee to treasury if it is non-zero
    if (vars.liquidationProtocolFeeAmount != 0) {
      uint256 liquidityIndex = collateralReserve.getNormalizedIncome();
      uint256 scaledDownLiquidationProtocolFee = vars.liquidationProtocolFeeAmount.rayDiv(liquidityIndex);
      uint256 scaledDownUserBalance = balances[params.collateralAsset][params.position].supplyShares;

      if (scaledDownLiquidationProtocolFee > scaledDownUserBalance) {
        vars.liquidationProtocolFeeAmount = scaledDownUserBalance.rayMul(liquidityIndex);
      }
+    balances[params.collateralAsset][params.position].withdrawCollateral(totalSupplies[params.collateralAsset], vars.actualCollateralToLiquidate, collateralReserveCache.nextLiquidityIndex);
     IERC20(params.collateralAsset).safeTransfer(IPool(params.pool).factory().treasury(), vars.liquidationProtocolFeeAmount);
    }
```