Clever Ebony Halibut

Medium

# Inaccurate Interest Rate Calculation in Liquidation Process

## Summary

The liquidation process in the `LiquidationLogic.sol` contract incorrectly calculates the interest rate for the collateral asset by underestimating the amount of liquidity taken. The calculation only considers the amount seized by the liquidator and ignores the additional liquidation fee sent to the protocol treasury. This leads to an inaccurate utilization ratio and consequently affects the liquidity index and the yield for depositors.

## Vulnerability Detail

- The liquidation process in the `LiquidationLogic.sol` contract involves seizing collateral from the violator and and then update the interest rate since some collateral will be leaving the protocol. The seized amount consists of two parts: _the liquidation amount taken by the liquidator_ and _the liquidation fee sent to the protocol treasury_.

```js
 >>  (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) = _calculateAvailableCollateralToLiquidate(/*prams*/);
```

- However, the current implementation of the `_burnCollateralTokens` function, which is responsible for updating the interest rates, only considers the liquidation amount taken by the liquidator (`vars.actualCollateralToLiquidate`) when calculating the interest rate. It ignores the liquidation fee (`vars.liquidationProtocolFeeAmount`) that is also sent to the treasury.

Here's the relevant code snippet from `LiquidationLogic.sol`:

```js
  function _burnCollateralTokens(
  DataTypes.ReserveData storage collateralReserve,
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  DataTypes.PositionBalance storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
  // ...
  collateralReserve.updateInterestRates(
  totalSupplies,
  collateralReserveCache,
  params.collateralAsset,
  IPool(params.pool).getReserveFactor(),
  0,
  vars.actualCollateralToLiquidate, // @audit-issue: underestimated amount, should include liquidation fee
  params.position,
  params.data.interestRateData
  );
  // ...
  }
```

The vulnerability lies in the fact that the liquidation fee, which is sent to the treasury, is not accounted for in the interest rate calculation, leading to an underestimation of the liquidity taken and an incorrect intrestRate. (lost of yield for suppliers since utilisationRation will be less than it should , and all intrestRate models are a utilisationRAtion functions).

> Note: we know that the interest rate model is _out-of-scope_ for this audit. However, we don't need to know the specific model used by the protocol to identify this issue. The `_updateInterestRate` function is used correctly in other operations like supply and withdraw. This inconsistency in the liquidation process is sufficient to demonstrate the vulnerability, regardless of the exact interest rate model employed.

## Impact

- The inconsistent handling of liquidation fees leads to incorrect interest rate calculations, resulting in loss of yield for depositors and potential of funds locked in the protocol (which can happen with some interest rate models).

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/LiquidationLogic.sol#L123-L134

## Tool used

Manual Review

## Recommendation

- To address this issue, the `_burnCollateralTokens` function should be updated to include the liquidation fee (`vars.liquidationProtocolFeeAmount`) when calculating the interest rate.

```diff
function _burnCollateralTokens(
DataTypes.ReserveData storage collateralReserve,
DataTypes.ExecuteLiquidationCallParams memory params,
LiquidationCallLocalVars memory vars,
DataTypes.PositionBalance storage balances,
DataTypes.ReserveSupplies storage totalSupplies
) internal {
// ...

  collateralReserve.updateInterestRates(
  totalSupplies,
  collateralReserveCache,
  params.collateralAsset,
  IPool(params.pool).getReserveFactor(),
  0,
- vars.actualCollateralToLiquidate,
+ vars.actualCollateralToLiquidate + vars.liquidationProtocolFeeAmount;
  params.position,
  params.data.interestRateData
  );
  // ...
  }
```