Sneaky Hazelnut Lizard

High

# Contract fails to deduct the liquidation fee from the User but instead withdraws this directly from the pool.

### Summary

In Zeroland’s current liquidation implementation, the liquidation protocol fee is incorrectly deducted from the pool’s total value instead of being withdrawn from the liquidated user's collateral. This discrepancy causes the pool to bear the burden of paying the liquidation fee, which leads to unintended value leakage from other users’ collateral. In contrast, Aave's implementation correctly deducts the liquidation fee from the liquidated user's collateral before transferring the fee to the treasury.

### Root Cause

Zeroland transfers the liquidation fee directly from the pool's balance, instead of first withdrawing the appropriate amount from the user’s collateral before performing the transfer. This results in an incorrect allocation of funds, impacting all users in the pool.


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L179-L189

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L366-L369

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L227-L229

Aave's implementation

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/LiquidationLogic.sol#L203-L217

```solidity
 if (vars.liquidationProtocolFeeAmount != 0) {
      uint256 liquidityIndex = collateralReserve.getNormalizedIncome();
      uint256 scaledDownLiquidationProtocolFee = vars.liquidationProtocolFeeAmount.rayDiv(
        liquidityIndex
      );
      uint256 scaledDownUserBalance = vars.collateralAToken.scaledBalanceOf(params.user);
      // To avoid trying to send more aTokens than available on balance, due to 1 wei imprecision
      if (scaledDownLiquidationProtocolFee > scaledDownUserBalance) {
        vars.liquidationProtocolFeeAmount = scaledDownUserBalance.rayMul(liquidityIndex);
      }
      vars.collateralAToken.transferOnLiquidation(
        params.user,
        vars.collateralAToken.RESERVE_TREASURY_ADDRESS(),
        vars.liquidationProtocolFeeAmount
      );
```

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/AToken.sol#L116-L124

```solidity
 /// @inheritdoc IAToken
  function transferOnLiquidation(
    address from,
    address to,
    uint256 value
  ) external virtual override onlyPool {
    // Being a normal transfer, the Transfer() and BalanceTransfer() are emitted
    // so no need to emit a specific event here
    _transfer(from, to, value, false);
  }
```

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/AToken.sol#L196-L217

```solidity
  function _transfer(address from, address to, uint256 amount, bool validate) internal virtual {
    address underlyingAsset = _underlyingAsset;

    uint256 index = POOL.getReserveNormalizedIncome(underlyingAsset);

    uint256 fromBalanceBefore = super.balanceOf(from).rayMul(index);
    uint256 toBalanceBefore = super.balanceOf(to).rayMul(index);

    super._transfer(from, to, amount, index);

    if (validate) {
      POOL.finalizeTransfer(underlyingAsset, from, to, amount, fromBalanceBefore, toBalanceBefore);
    }

    emit BalanceTransfer(from, to, amount.rayDiv(index), index);
  }
```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

When a liquidation occurs, Zeroland fails to deduct the liquidation protocol fee from the liquidated user's collateral. Instead, the fee is transferred directly from the pool's total balance. This is a significant flaw as it causes the pool to use other users' funds to cover the liquidation protocol fee for another user's position.

This can be seen in the following Zeroland implementation:

```solidity
if (vars.liquidationProtocolFeeAmount != 0) {
    uint256 liquidityIndex = collateralReserve.getNormalizedIncome();
    uint256 scaledDownLiquidationProtocolFee = vars.liquidationProtocolFeeAmount.rayDiv(liquidityIndex);
    uint256 scaledDownUserBalance = balances[params.collateralAsset][params.position].supplyShares;

    if (scaledDownLiquidationProtocolFee > scaledDownUserBalance) {
        vars.liquidationProtocolFeeAmount = scaledDownUserBalance.rayMul(liquidityIndex);
    }

@audit>>>     IERC20(params.collateralAsset).safeTransfer(IPool(params.pool).factory().treasury(), vars.liquidationProtocolFeeAmount);
}
```

Here, the liquidation protocol fee is transferred directly from the pool to the treasury without withdrawing it from the liquidated user's collateral.

In comparison, Aave correctly deducts the liquidation fee from the user's collateral before transferring it to the treasury:

```solidity
vars.collateralAToken.transferOnLiquidation(
    params.user,
    vars.collateralAToken.RESERVE_TREASURY_ADDRESS(),
    vars.liquidationProtocolFeeAmount
);
```



### Impact

This issue leads to a significant leakage of funds from the pool, as users’ funds are unfairly used to cover liquidation fees for other positions. 

**Loss of User Funds**: Other users’ collateral is reduced to pay for liquidation fees that should be borne by the liquidated user.




### PoC

_No response_

### Mitigation

To mitigate this issue, the Zeroland contract must be modified to withdraw the liquidation protocol fee directly from the user's collateral before transferring the fee to the treasury, similar to how Aave handles liquidation fees:

```solidity

++           balances.withdrawCollateral(totalSupplies, vars.liquidationProtocolFeeAmount, collateralReserveCache.nextLiquidityIndex);

              IERC20(params.collateralAsset).safeTransfer(IPool(params.pool).factory().treasury(), vars.liquidationProtocolFeeAmount);
```

This ensures that the fee is paid from the liquidated user's collateral and not from the pool’s shared funds, protecting the value for other users.