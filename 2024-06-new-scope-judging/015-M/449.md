Special Macaroon Shetland

Medium

# Inflation/donation attack is possible in current Vault implementation

### Summary

In Curated Vault implementation, virtual offset is not applied and inflation/donation attack is possible. Attacker can steal the supplier assets with donation. For attack details OpenZeppelin's documentation: https://docs.openzeppelin.com/contracts/4.x/erc4626

### Root Cause

While calculation of the shares, vault simply converts assets to shares as follows [CuratedVaultGetters.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L163):

```solidity
  function _convertToSharesWithTotals(
    uint256 assets,
    uint256 newTotalSupply,
    uint256 newTotalAssets,
    MathUpgradeable.Rounding rounding
  ) internal view returns (uint256) {
    return assets.mulDiv(newTotalSupply + 10 ** _decimalsOffset(), newTotalAssets + 1, rounding);
```

`_decimalsOffset()` will be 0 if the assets decimal is equal to 18 or higher. We can know that from here:

```solidity
DECIMALS_OFFSET = uint8(uint256(18).zeroFloorSub(IERC20Metadata(_asset).decimals()));

--------------------------------------------------------
  /// @dev Returns max(0, x - y).
  function zeroFloorSub(uint256 x, uint256 y) internal pure returns (uint256 z) {
    assembly {
      z := mul(gt(x, y), sub(x, y))
    }
  }
```

In the end, our implementation will be like that:
```solidity
  function _convertToSharesWithTotals(
    uint256 assets,
    uint256 newTotalSupply,
    uint256 newTotalAssets,
    MathUpgradeable.Rounding rounding
  ) internal view returns (uint256) {
    return assets.mulDiv(newTotalSupply + 1, newTotalAssets + 1, rounding);
```
Due to, missing virtual offset or additional protection against inflation attack, it's open against inflation/donation attacks.






### Internal pre-conditions

1. It should be a new deployed vault

### External pre-conditions

N/A

### Attack Path

1. New Vault for WETH ( 18 decimal ) is deployed.
2. Attacker deposits 1 wei of WETH and he gets 1 share from the vault. 
3. Alice deposits 1000 WETH but it will wait in block queue.
4. Attacker sees Alice's transaction and donate 2000 WETH to vault using position id of Vault with frontrunning.
Note: Direct donation is not possible with ERC20. Attacker will donate tokens with `supply()` function in Pool.sol contract using Vault's position asset and index. It's approved in Pool implementation see: [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L78)
5. Now, let's make some math. In vault there are 1 shares total which is minted to attacker and there are 2000 WETH + 1 wei of WETH assets in the vault.

Our equation as follows:

$$\frac{AliceDeposit * (totalShares + 1)}{totalAssets + 1} = newAlicesShares$$

$$\frac{1000e18 * (1 + 1)}{2000e18 + 1 + 1} = 0$$

6. Alice will get 0 shares from this deposit and attacker earn 1000 WETH and make 50% profit from this attack. 

### Impact

1. Inflation/Donation attack is possible, attacker can steal first user's deposit by donating some tokens to Vault.

### Mitigation

In order to prevent inflation/donation attack, following OpenZeppelin's documentation is beneficial.

```solidity
  function _convertToSharesWithTotals(
    uint256 assets,
    uint256 newTotalSupply,
    uint256 newTotalAssets,
    MathUpgradeable.Rounding rounding
  ) internal view returns (uint256) {
    return assets.mulDiv(newTotalSupply + 10 ** _decimalsOffset() + _virtualOffset, newTotalAssets + 1, rounding); 
  }
```