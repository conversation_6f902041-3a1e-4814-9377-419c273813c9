Fresh Silver Owl

High

# `DECIMALS_OFFSET` is not set correctly

## Summary

Currently, `DECIMALS_OFFSET` is not set correctly, making it possible to perform a classic ERC4626-inflation attack

## Vulnerability Detail

```solidity
  function initialize(
    address[] memory _admins,
    address[] memory _curators,
    address[] memory _guardians,
    address[] memory _allocators,
    uint256 _initialTimelock,
    address _asset,
    string memory _name,
    string memory _symbol
  ) external initializer {
    __ERC20_init(_name, _symbol);
    __ERC20Permit_init(_name);
    __ERC4626_init(IERC20Upgradeable(_asset));
    __Multicall_init();
    __CuratedVaultRoles_init(_admins, _curators, _guardians, _allocators);

    DECIMALS_OFFSET = uint8(uint256(18).zeroFloorSub(IERC20Metadata(_asset).decimals()));
    _checkTimelockBounds(_initialTimelock);
    _setTimelock(_initialTimelock);

    positionId = keccak256(abi.encodePacked(address(this), 'index', uint256(0)));
  }
```
For all assets with 18 decimals, `DECIMALS_OFFSET` would be set to 0. That means a classic ERC4626 inflation is possible.
```solidity
  function deposit(uint256 assets, address receiver) public override returns (uint256 shares) {
    uint256 newTotalAssets = _accrueFee();

    // Update `lastTotalAssets` to avoid an inconsistent state in a re-entrant context.
    // It is updated again in `_deposit`.
    lastTotalAssets = newTotalAssets;
    shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Down);
    _deposit(_msgSender(), receiver, assets, shares);
  }
```
```solidity
  function _convertToSharesWithTotals(
    uint256 assets,
    uint256 newTotalSupply,
    uint256 newTotalAssets,
    MathUpgradeable.Rounding rounding
  ) internal view returns (uint256) {
    return assets.mulDiv(newTotalSupply + 10 ** _decimalsOffset(), newTotalAssets + 1, rounding);
  }
```
A malicious user can perform a donation to execute a classic first depositor/ERC4626 inflation Attack against the CuratedVault. The general process of this attack is well-known, and a detailed explanation of this attack can be found in many of the resources such as the following:

https://blog.openzeppelin.com/a-novel-defense-against-erc4626-inflation-attacks
https://mixbytes.io/blog/overview-of-the-inflation-attack

In short, to kick-start the attack, the malicious user will often usually mint the smallest possible amount of shares (e.g., 1 wei) and then donate significant assets to the vault to inflate the number of assets per share. Subsequently, it will cause a rounding error when other users deposit.

However, in Zerolend, we cannot deposit 1 wei to a pool because such check:

```solidity
  function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
    sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
  }
```
Thus, one would need to perform additional steps to workaround/bypass the existing controls.

Let's divide the setup of the attack into two main parts:

1. Malicious user mint 100 shares(we assume index < 100)
2. Inflate the supply share price by 100x through a sequence of `withdraw()` without receiving any assets in return. The reason is that in withdraw we compute the assets to be received as `shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Up);`. Note that assets can be zero and the withdraw essentially becomes a pure burn function.
```solidity
  function withdraw(uint256 assets, address receiver, address owner) public override returns (uint256 shares) {
    uint256 newTotalAssets = _accrueFee();

    // Do not call expensive `maxWithdraw` and optimistically withdraw assets.
    shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Up);

    // `newTotalAssets - assets` may be a little off from `totalAssets()`.
    _updateLastTotalAssets(newTotalAssets.zeroFloorSub(assets));
    _withdraw(_msgSender(), receiver, owner, assets, shares);
  }
```

## Impact

Malicous users could steal the assets of the victim.

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L163

## Tool used

Manual Review

## Recommendation

Mint some initial shares when deploying vaults.