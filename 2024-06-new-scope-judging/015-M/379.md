Brave Ruby Dinosaur

High

# Inflation attack on `CuratedVaults` using 18-decimal asset tokens

## Summary
In the `CuratedVault` contract, the `DECIMALS_OFFSET` value is set to `max(0, 18 - asset.decimals())`. This means that the CuratedVaults created on 18-decimal asset tokens have a `DECIMALS_OFFSET` of 0, resulting in no virtual shares being minted. This omission creates a vulnerability, leading to an inflation attack on that specific `CuratedVault`.

## Vulnerability Detail
`CuratedVault` follows the ERC4626 standard, which is known to be susceptible to inflation attacks. To address this, virtual shares are typically minted using the `DECIMALS_OFFSET` mechanism. However,In our case of `CuratedVault` when the asset token has 18 decimals, the `DECIMALS_OFFSET` is set to 0, preventing the minting of additional virtual shares. This lack of virtual shares exposes the vault to an inflation attack. 

A malicious actor can exploit this vulnerability by front-running the `deposit` function, allowing them to inflate the token supply and cause significant damage.

## Impact
Malicious actors can execute an inflation attack on `CuratedVault` contracts that use 18-decimal asset tokens, potentially causing substantial losses to unsuspecting users.
 
## Code Snippet
```solidity
  function initialize(
    address[] memory _admins,
    address[] memory _curators,
    address[] memory _guardians,
    address[] memory _allocators,
    uint256 _initialTimelock,
    address _asset,
    string memory _name,
    string memory _symbol
  ) external initializer {
    __ERC20_init(_name, _symbol);
    __ERC20Permit_init(_name);
    __ERC4626_init(IERC20Upgradeable(_asset));
    __Multicall_init();
    __CuratedVaultRoles_init(_admins, _curators, _guardians, _allocators);

@>   DECIMALS_OFFSET = uint8(uint256(18).zeroFloorSub(IERC20Metadata(_asset).decimals()));
    _checkTimelockBounds(_initialTimelock);
    _setTimelock(_initialTimelock);

    positionId = keccak256(abi.encodePacked(address(this), 'index', uint256(0)));
  }
```
```solidity
  function _convertToSharesWithTotals(
    uint256 assets,
    uint256 newTotalSupply,
    uint256 newTotalAssets,
    MathUpgradeable.Rounding rounding
  ) internal view returns (uint256) {
@>  return assets.mulDiv(newTotalSupply + 10 ** _decimalsOffset(), newTotalAssets + 1, rounding);
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L76
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L322
## Tool used
Manual Review
## Recommendation
- use a better mechanism to set the `DECIMALS_OFFSET` value. 
- add the below require check
```solidity
function deposit(uint256 assets, address receiver) public override returns (uint256 shares) {
    uint256 newTotalAssets = _accrueFee();

    // Update `lastTotalAssets` to avoid an inconsistent state in a re-entrant context.
    // It is updated again in `_deposit`.
    lastTotalAssets = newTotalAssets;
    shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Down);
@>  require(shares != 0, "Shares cannot be equal to 0"); 
    _deposit(_msgSender(), receiver, assets, shares);
  }
```