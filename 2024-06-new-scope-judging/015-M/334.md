Long Coffee Cat

Medium

# CuratedVault will work incorrectly with assets that have more than `18` decimals

### Summary

CuratedVault's `initialize()` will silently set `DECIMALS_OFFSET` to zero for assets with more than 18 decimals, so all the computations involving `DECIMALS_OFFSET` will be incorrect

### Root Cause

There is no check for asset decimals to not exceed `18`, while `zeroFloorSub` doesn't revert in this case, setting `DECIMALS_OFFSET = 0`:

[CuratedVault.sol#L76](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L76)

```solidity
    DECIMALS_OFFSET = uint8(uint256(18).zeroFloorSub(IERC20Metadata(_asset).decimals()));
```

Correct offset in this situation should be negative, i.e. it is not supported by the implementation: instead of `1` in `newTotalAssets + 1` it needs to be `newTotalAssets + 10 ** (-_decimalsOffset())`  in `_convertToSharesWithTotals()` and `_convertToAssetsWithTotals`, which is not the case

### Internal pre-conditions

None

### External pre-conditions

Vault's asset has to have decimals exceeding `18`. No other properties are assumed

### Attack Path

Since asset buffer in `_convertToSharesWithTotals()` and `_convertToAssetsWithTotals` will not be sufficient in this case, Vault becomes prone to shares inflation attacks

### Impact

New Vaults can be exposed to decimals difference based attacks, e.g. minting `1 wei` of share for `1 wei` of asset when shares have 18 decimals, while asset has, for example, 36 decimals. Net impact is attacker being able to steal from the subsequent depositors

### PoC

N/A as the surface is wide enough and specific attack depends on the way new Vaults be deployed in production

### Mitigation

Consider reverting in CuratedVault's `initialize()` when `IERC20Metadata(_asset).decimals() > 18`