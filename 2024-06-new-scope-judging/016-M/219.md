Fresh Sangria Mink

Medium

# chainlink integration issues

## Summary

## Vulnerability Detail
Calls to Oracles could potentially revert, which may result in a complete Denial-of-Service to smart contracts which depend upon them.
Chainlink price feeds have in-built minimum & maximum prices they will return. if during a flash crash, bridge compromise, or depegging event, an asset’s value can fall below the price feed’s minimum price.
`PoolGetters::getAssetPrice` fails to handle these scenarios.
## Impact
1. Calls to the `PoolGetters::getAssetPrice` can revert which will disrupt the protocol functionality,
2. Code will execute with prices that don’t reflect the current pricing resulting in a potential loss of funds for users.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163
## Tool used

Manual Review

## Recommendation
1. wrap calls to Oracles in try/catch blocks
2. Revert unless minAnswer < answer < maxAnswer