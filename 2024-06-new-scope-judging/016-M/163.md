Rural Eggshell Sheep

Medium

# Chainlink Oracle will return the wrong price for asset if underlying aggregator hits minAnswer

## Summary

> from ZeroLend One docs: Low Risk  | For blue-chip assets only |  ETH, USDC, USDT, DAI etc...
	
Zerolend supports USDC , ETH for a  pool. So we can expect there will be a pool for USDC/ETH.

Chainlink aggregators have a built in circuit breaker if the price of an asset goes outside of a predetermined price band. The result is that if an asset experiences a huge drop in value (i.e. LUNA crash) the price of the oracle will continue to return the minPrice instead of the actual price of the asset. This would allow user to continue borrowing with the asset but at the wrong price. This is exactly what happened to [Venus on BSC when LUNA imploded](https://rekt.news/venus-blizz-rekt) .
## Vulnerability Detail
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price, , uint256 updatedAt, ) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  } 
```

ChainlinkFeedRegistry#latestRoundData pulls the associated aggregator and requests round data from it. ChainlinkAggregators have minPrice and maxPrice circuit breakers built into them. This means that if the price of the asset drops below the minPrice, the protocol will continue to value the token at minPrice instead of it's actual value. This will allow users to take out huge amounts of bad debt and bankrupt the protocol.

## Impact
The wrong price may be returned in the event of a market crash. An adversary will then be able to borrow against the wrong price and incur bad debt to the protocol.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158
## Tool used

Manual Review

## Recommendation
get minprice and maxprice from aggregator and add check .

```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price, , uint256 updatedAt, ) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    if (price >= maxPrice or price <= minPrice) revert();
    return uint256(price);
  } 

```