Brave Ruby Dinosaur

Medium

# If a token's oracle goes down or price falls to zero, liquidations will be frozen


## Summary

In some extreme cases, oracles can be taken offline or token prices can fall to zero. In these cases, liquidations will be frozen (all calls will revert) for any debt holders holding this token, even though they may be some of the most important times to allow liquidations to retain the solvency of the protocol.

## Vulnerability Detail

Chainlink has taken oracles offline in extreme cases. For example, during the UST collapse, Chainlink paused the UST/ETH price oracle, to ensure that it wasn't providing inaccurate data to protocols.In such a situation (or one in which the token's value falls to zero), all liquidations for users holding the frozen asset would revert

If the oracle price lookup reverts, liquidations will be frozen, and the user will be immune to liquidations. Although there are ways this could be manually fixed with fake oracles, by definition this happening would represent a cataclysmic time where liquidations need to be happening promptly to avoid the protocol falling into insolvency.



## Impact

Liquidations may not be possible at a time when the protocol needs them most. As a result, the value of user's asset may fall below their debts, turning off any liquidation incentive and pushing the protocol into insolvency.



## Code Snippet

```solidity
    function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
https://github.com/zerolend/zerolend-one/blob/6b681f2a16be20cb2d43e544c164f913a8db1cb8/contracts/core/pool/PoolGetters.sol#L158

## Tool used

Manual Review

## Recommendation

Ensure there is a safeguard in place to protect against this possibility.
