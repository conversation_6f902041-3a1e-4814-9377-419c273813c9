Brave Ruby Dinosaur

Medium

# Risk of Incorrect Asset Pricing by `PoolGetters::getAssetPrice()` in Case of Underlying Aggregator Reaching minAnswer

## Summary
Chainlink aggregators have a built-in circuit breaker to prevent the price of an asset from deviating outside a predefined price range. This circuit breaker may cause the oracle to persistently return the minPrice instead of the actual asset price in the event of a significant price drop, as witnessed during the LUNA crash.This would allow user to continue borrowing with the asset but at the wrong price. This is exactly what happened to Venus on BSC when LUNA imploded.
## Vulnerability Detail
ChainlinkFeedRegistry#latestRoundData pulls the associated aggregator and requests round data from it. ChainlinkAggregators have minPrice and maxPrice circuit breakers built into them. This means that if the price of the asset drops below the minPrice, the protocol will continue to value the token at minPrice instead of it's actual value. This will allow users to take out huge amounts of bad debt and bankrupt the protocol.

Example:
TokenA has a minPrice of $1. The price of TokenA drops to $0.10. The aggregator still returns $1 allowing the user to borrow against TokenA as if it is $1 which is 10x it's actual value.
## Impact
In the event that an asset crashes (i.e. LUNA) the protocol can be manipulated and allows Borrowing at an inflated price of collateral. 
## Code Snippet
```solidity
function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158
## Tool used
Manual Review
## Recommendation
the price should be checked against min/max price as mentioned below and revert if the answer is outside of these bounds:
```solidity
(, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    if (price >= maxPrice or price <= minPrice) revert();
```
This ensures that a false price will not be returned if the underlying asset's value hits the minPrice.
