Sc<PERSON>y <PERSON>jon <PERSON>chilla

Medium

# `PoolGetters::getAssetPrice()` did not check `minAnswer` and `maxAnswer`, this may cause wrong price

### Summary

`PoolGetters::getAssetPrice()` did not check `minAnswer` and `maxAnswer`, this can cause wrong prices and impact asset conversions using those prices. For example, the asset used by `Zerolend` is `USDC`. `USDC` is a stablecoin that is maintained at $1 and in the chainlink oracle feed it is known that the `USDC / ETH` pair has a deviation threshold of 1%. So the price must be maintained in that range to get the correct price. This can be done by checking whether the price is in the `minAnswer` and `maxAnswer` range, but this is not implemented in the codebase.

### Root Cause

In [PoolGetters.sol:158 - 163](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163) there is missing check for `minAnswer` and `maxAnswer`

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The price used to convert the asset is incorrect and this affects the amount of assets received by the user

### PoC

```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```

### Mitigation

`PoolGetters:getAssetPrice()` should check the returned answer against the `minPrice` / `maxPrice` and revert if the answer is outside of the bounds:

```solidity
if (answer >= maxPrice or answer <= minPrice) revert();
```