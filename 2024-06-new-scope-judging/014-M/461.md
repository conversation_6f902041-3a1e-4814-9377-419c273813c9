Long Coffee Cat

High

# CuratedVault assets aren't updated on pool removal inflating its total assets

### Summary

Vault's assets aren't updated on pool removal, accumulating the inflated total assets reading over time, causing losses to the late depositors and eventually leading to insolvency

### Root Cause


When pool is deleted from CuratedVault (cap is set to zero, then withdraw queue is updated to a list not including the pool) its assets aren't cleared from the total. Whenever the assets are positive it can ruin the accounting after that point. Since the error accumulates the amounts can be small enough each time, still causing the Vault's mispricing and eventual withdraw unavailability, i.e. insolvency.

Supply is added to the total on pool introduction:

[CuratedVaultSetters.sol#L85-L102](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L85-L102)

```solidity
  function _setCap(IPool pool, uint184 supplyCap) internal {
    MarketConfig storage marketConfig = config[pool];

    if (supplyCap > 0) {
      if (!marketConfig.enabled) {
        withdrawQueue.push(pool);

        if (withdrawQueue.length > MAX_QUEUE_LENGTH) revert CuratedErrorsLib.MaxQueueLengthExceeded();

        marketConfig.enabled = true;

        // Take into account assets of the new market without applying a fee.
        pool.forceUpdateReserve(asset());
        uint256 supplyAssets = pool.supplyAssets(asset(), positionId);
>>      _updateLastTotalAssets(lastTotalAssets + supplyAssets);

        emit CuratedEventsLib.SetWithdrawQueue(msg.sender, withdrawQueue);
      }
```

But never removed, i.e. it's directly checked that `pool.supplyAssets(asset(), positionId) > 0`, but Vault's total is not reduced:

[CuratedVault.sol#L210-L225](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L210-L225)

```solidity
    for (uint256 i; i < currLength; ++i) {
      if (!seen[i]) {
        IPool pool = withdrawQueue[i];

        if (config[pool].cap != 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroCap(pool);
        if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.PendingCap(pool);
>>      if (pool.supplyShares(asset(), positionId) != 0) {
          if (config[pool].removableAt == 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroSupply(pool);
          if (block.timestamp < config[pool].removableAt) {
            revert CuratedErrorsLib.InvalidMarketRemovalTimelockNotElapsed(pool);
          }
        }

>>      delete config[pool];
      }
    }
```

### Internal pre-conditions

Positive cap is set for a new pool that CuratedVault already have some existing balance with. After some time this pool's cap is set to zero, and then pool is removed via `updateWithdrawQueue()`, while still having `pool.supplyShares(asset(), id) > 0`

### External pre-conditions

Pool and Vault balances need to be positive. There is no requirement of any substantial value as accounting mismatch can accumulate over time, i.e. it can be small/medium amounts gathered over extended period

### Attack Path

It can be exploitted directly by colliding `Curator` and `Allocator`: `Curator` can set non-zero cap, then zero cap, and `Allocator` can simultaneously update the withdraw queue first to include the pool after non-zero cap is set, then to exclude it after it was reset back to zero. Since pool is not compromised by itself and addition/removal are the usual operations this can be carried out unnoticed for a substantial time. This way these actors can perform a early deposit, then inflate the total assets reading, at exit, stealing from the remaining depositors, the last of whom will not be able to exit due to insolvency.

Attacker can fund Vault's balance with the pool once using pool's supply operation that allows to fund any position supplying arbitrary `to.getPositionId(index) = vault.getPositionId(0)`, then use this invested value multiple times, achieving substantially bigger impact compared to these funds, making the investment ROE attractive

### Impact

Minting/depositing and withdrawal/redeem operations to value shares using overstated total assets reading, bloating shares value, causing losses to late depositors by redirecting some value from them to early withdrawers. This ends up with Vault insolvency since the last depositors will not be able to withdraw a part of their funds

### PoC

Direct attack, all the steps are parts of usual workflow, is described above. This is low probability / high impact way of using the surface.

There is also high probability / low impact one: error accumulation without any actors colliding and happening along with the usual operations. Vault removal from the queue can happen due to various reasons, say pool can have steadily high utilization for a period, which is usually coupled with high APY, so it can be reasonable to keep it not available for withdrawals for a while to sustain APY and lower user gas costs (i.e. there is no point to request withdrawal as it won't produce much and will reduce the usage of the attractive opportunity while it lasts). However, being repeated over time as needed this operation will substantially bloat Vault assets reading and eventually lead to its insolvency

### Mitigation

In order to keep accounting correct when timelock is satisfied and the market can be removed, when its supply is not zero it needs to be cleared from the total assets, e.g. via `_updateLastTotalAssets(lastTotalAssets - supplyAssets)`:

```diff
+       pool.forceUpdateReserve(asset());
+       uint256 supplyAssets = pool.supplyAssets(asset(), positionId);
+       _updateLastTotalAssets(lastTotalAssets - supplyAssets);
```