Massive Glass Crane

Medium

# CuratedVault :: Fees shares are not minted to the FeeRecipients prior MarketRemoval.

### Summary
Before removing a market from `withdrawQueue` , `fees` are not collected from the interest accumulated.

Protocol says , fees are not collected from the accumulated interest of the market while the disabled market with existing shares is getting enabled.
https://vscode.dev/github/zerolend/zerolend-one/blob/dhank/contracts/core/vaults/CuratedVaultSetters.sol#L96-L97

But that doesnt mean fees should be avoided from the interest that the market is accumulating prior the market removal.


### Root Cause
`Fees` are not collected and given to `feeRecipient` prior the removal of Market from `withdrawQueue`.

https://vscode.dev/github/zerolend/zerolend-one/blob/master/contracts/core/vaults/CuratedVault.sol#L210-L227


### Internal pre-conditions

A market `X` is gathering interest even after the `supplyCap` is made `0`.(Users can nomore deposit in the X ) 

Operation such as deposit/mint/redeem/withdraw/setFee is getting executed and the `fees` are calculated from the interest gathered by all `markets` including the market `X`.

Now , the Market `X` is removed from the `withdrawQueue` by the `allocator`.(Note that `lastTotalAssets` is not updated) 

And next, when the `accrueFee()` call is made , the `totalInterest` is calculated from ( `newTotalAssets` - `lastTotalAsset` ) and the `lastTotalAsset` > `newTotalAssets`. 
[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L188)

`totalInterest` becomes 0.Hence the actual `fees` generated by the removed Market prior removal are not collected and given to `feeRecipient` .
### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

`Fees` shares are not minted to the `feeRecipient` prior removing the market from `withdrawQueue` affecting the design of the protocol.

### PoC
_No response_

### Mitigation

```solidity
....
for (uint256 i; i < currLength; ++i) {
      if (!seen[i]) {
        IPool pool = withdrawQueue[i];

        if (config[pool].cap != 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroCap(pool);
        if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.PendingCap(pool);
        if (pool.supplyShares(asset(), positionId) != 0) {
            
          _updateLastTotalAssets(_accrueFee()); //update total Assets and accrue Fee.

          if (config[pool].removableAt == 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroSupply(pool);
          if (block.timestamp < config[pool].removableAt) {
            revert CuratedErrorsLib.InvalidMarketRemovalTimelockNotElapsed(pool);
          }
        }

        delete config[pool];
      }
    }
    ....
```