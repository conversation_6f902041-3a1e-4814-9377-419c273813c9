Polished Iris Antelope

Medium

# Loss of interest fee shares for ```CuratedVault``` admins due to not calling ```_accrueFee()``` before the removal of a ```Pool``` from ```withdrawQueue```.

## Summary
Updating the ```withdrawQueue``` of a ```CuratedVault``` through ```updateWithdrawQueue``` function, lets you remove a ```Pool``` from it but it doesn't call ```_accrueFee``` before it so the interest fee will be lost.

## Vulnerability Detail
```CuratedVault``` admins take a portion of the interest generated from the LPing in ```Pools```. This procedure happens when ```_accrueFee``` function is called and we can see its implementation here :
```solidity
  function _accrueFee() internal returns (uint256 newTotalAssets) {
    uint256 feeShares;
    (feeShares, newTotalAssets) = _accruedFeeShares();
    if (feeShares != 0) _mint(feeRecipient, feeShares);
    emit CuratedEventsLib.AccrueInterest(newTotalAssets, feeShares);
  }

  function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
    newTotalAssets = totalAssets();

    uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets);
    if (totalInterest != 0 && fee != 0) {
      // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
      uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
      // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
      // that total assets is already increased by the total interest (including the fee assets).
      feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
    }
  }

   function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
@>      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368C1-L372C4)

As you can see, ```withdrawQueue``` is being looped to determine how many assets do the Vault has and on them to perform the charge of fees. If a Pool is removed from the ```withdrawQueue``` and the ```_accrueFee``` hasn't been called before it, then the interest generated from this ```Pool``` won't be charged with fee. This happens in the ```updateWithdrawQueue``` function of ```CuratedVault```. We can see it here :
```solidity
  function updateWithdrawQueue(uint256[] calldata indexes) external onlyAllocator {
    // ...

    for (uint256 i; i < currLength; ++i) {
      if (!seen[i]) {
        IPool pool = withdrawQueue[i];

        if (config[pool].cap != 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroCap(pool);
        if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.PendingCap(pool);
        if (pool.supplyShares(asset(), positionId) != 0) {
          if (config[pool].removableAt == 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroSupply(pool);
          if (block.timestamp < config[pool].removableAt) {
            revert CuratedErrorsLib.InvalidMarketRemovalTimelockNotElapsed(pool);
          }
        }

        delete config[pool];
      }
    }

    // ...
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L192)

We can see that the removal of ```Pool``` proceeds as usual but the ```accrueFee()``` is not called and as a result the admins lose value since the ```Pool``` just disappears from the ```withdrawQueue```.

## Impact
The failure to call ```_accrueFee()``` before removing a ```Pool``` from the ```withdrawQueue``` results in the loss of interest fee shares that would otherwise be accrued to ```CuratedVault``` admins. This means that any interest generated by the removed ```Pool``` will not contribute to the admins' fees, leading to a financial loss. Over time, repeated this vulnerability could significantly impact the protocol's revenue model.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L192

## Tool used
Manual Review

## Recommendation
Consider making this change so to collect the right fees from the Pools before the updates of the ```withdrawQueue``` :
```diff
  function updateWithdrawQueue(uint256[] calldata indexes) external onlyAllocator {

+    uint256 newTotalAssets = _accrueFee();
+    lastTotalAssets = newTotalAssets;

    uint256 newLength = indexes.length;
    uint256 currLength = withdrawQueue.length;

    bool[] memory seen = new bool[](currLength);
    IPool[] memory newWithdrawQueue = new IPool[](newLength);

    for (uint256 i; i < newLength; ++i) {
      uint256 prevIndex = indexes[i];

      // If prevIndex >= currLength, it will revert CuratedErrorsLib.with native "Index out of bounds".
      IPool pool = withdrawQueue[prevIndex];
      if (seen[prevIndex]) revert CuratedErrorsLib.DuplicateMarket(pool);
      seen[prevIndex] = true;

      newWithdrawQueue[i] = pool;
    }

    for (uint256 i; i < currLength; ++i) {
      if (!seen[i]) {
        IPool pool = withdrawQueue[i];

        if (config[pool].cap != 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroCap(pool);
        if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.PendingCap(pool);
        if (pool.supplyShares(asset(), positionId) != 0) {
          if (config[pool].removableAt == 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroSupply(pool);
          if (block.timestamp < config[pool].removableAt) {
            revert CuratedErrorsLib.InvalidMarketRemovalTimelockNotElapsed(pool);
          }
        }

        delete config[pool];
      }
    }

    withdrawQueue = newWithdrawQueue;
    emit CuratedEventsLib.SetWithdrawQueue(_msgSender(), newWithdrawQueue);
  }
```