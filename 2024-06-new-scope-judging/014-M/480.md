Massive Glass Crane

Medium

# CuratedVault. ::`lastTotalAsset` will store a value larger than the actual value/assets owned by the `Vault` resulting in the incorrect calculation of fees

### Summary
`lastTotalAsset` will store a value larger than the actual value/assets owned by the `Vault`.

This happens when a `market` is added to the `withdrawQueue` by increasing the `supplyCap` after removing it from `withdrawQueue` considering there are no calls to `accrueFees()` happened in between.

Later when a user deposits/withdraw wrong `lastTotalAssets` is used  to calculate fees.


### Root Cause

Lets say the `Vault` has some shares in Market `X` but due to some problems like consistent revert the allocator decided to remove the market `X` from the `withdrawQueue` for a while.

Hence the curator called `SubmitMarketRemoval()` for the Market `X` after setting its `supplyCap` to `0`.

Allocator then called `updateWithdrawQueue` to remove Market `X` , here it is [evident](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L216-L221) that protocol is not updating the `lastTotalAssets` even though a market with `supplyAssets` is removed from the `withDrawQueue`. The reason is , when user executes any other function (deposit/mint/withdraw/setFee) `accrueFee()` is called where the `lastTotalAssets` is updated to reflect the new actual assets.

But now the allocator decided to add the the same Market `X` to the `withdrawQueue`( considering no `updateLastTotalAssets()` has been called in between) and the curator  calls the `submitCap()`.

Now after the locktime period is over and when the `_setCap()` is called , it will check whether the `supplyCap >0` and `market.enbled == false` which is true for our case for the market `X`.

```solidity
if (supplyCap > 0) {     
      if (!marketConfig.enabled) {
        withdrawQueue.push(pool);

        if (withdrawQueue.length > MAX_QUEUE_LENGTH) revert CuratedErrorsLib.MaxQueueLengthExceeded();

        marketConfig.enabled = true;

        // Take into account assets of the new market without applying a fee.
        pool.forceUpdateReserve(asset());
        uint256 supplyAssets = pool.supplyAssets(asset(), positionId);
@=>       _updateLastTotalAssets(lastTotalAssets + supplyAssets); //@audit

        emit CuratedEventsLib.SetWithdrawQueue(msg.sender, withdrawQueue);
      }

      marketConfig.removableAt = 0;
    }
```

Inside the [condition](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L99) , we are updating the `lastTotalAssets` by adding the `supplyAssets` of the market `X` to it.

Since we havent removed the `supplyAssets` from the `lastTotalAssets` while removing the `market` from `withdrawQueue` and so later when the `_setCap()` is executed the same `supplyAssets`(but with some gained interest) is added again.

THis results the `lastTotalAssets` to store values larger than the actual value.

Now if a user deposits/redeem , the incorrect `lastTotalAssets` is used to calculate the `fees` for `feeRecipient`. 

### Internal pre-conditions

The vault has shares on the market X before it is removed from the withdrawQueue.

Curator decided to enable the Market X again by setting the supplyCap for the same.

No calls to accrueFees has been called until the _setCap() for market X is executed.

User calls deposit/redeem/withdraw/mint and accueFee() gets executed which denies the feeRecipient his shares.


### External pre-conditions

_No response_

### Attack Path

Users who noticed this vulnerability will come forward and call `redeem()`.

Since the `lastTotalAssets` now contains the amount larger than the actual amount , the new feeShare minted to feeRecipient can be reduced upto 0.


### Impact

`FeeRecipients` can be retained from receiving the feeShares when the `supplyCap` is again set for the `market` after it is disabled.


### PoC
-

### Mitigation
Call accrueFees before adding the supplyAssets to lastTotalAssets.
```solidity
 if (supplyCap > 0) {
      if (!marketConfig.enabled) {
        withdrawQueue.push(pool);

        if (withdrawQueue.length > MAX_QUEUE_LENGTH) revert CuratedErrorsLib.MaxQueueLengthExceeded();

        marketConfig.enabled = true;

        // Take into account assets of the new market without applying a fee.
        pool.forceUpdateReserve(asset());
=>+     _updateLastTotalAssets(accrueFee());
        uint256 supplyAssets = pool.supplyAssets(asset(), positionId);
        _updateLastTotalAssets(lastTotalAssets + supplyAssets);

        emit CuratedEventsLib.SetWithdrawQueue(msg.sender, withdrawQueue);
      }

      marketConfig.removableAt = 0;
    }
```

