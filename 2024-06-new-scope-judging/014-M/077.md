Great Jade Shetland

High

# `lastTotalAsset` is not updated after removing pools using `CuratedVault::updateWithdrawQueue`, forcing subsequent interest calculation to not work properly

### Summary

When users deposit funds into curated vaults, these funds get deposited into different pools, earning interest when borrowers borrow and repay their debts. Vaults have their part in this interest, and at the same time, part of the vault's profit is gone to the vault's creator/fee recipient.

Minting shares to the fee recipient is done in `CuratedVaultSetters::_accrueFee`, which calls `CuratedVaultGetters::_accruedFeeShares`:
```solidity
function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
  newTotalAssets = totalAssets();

  uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets);
  if (totalInterest != 0 && fee != 0) {
    // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
    uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
    // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
    // that total assets is already increased by the total interest (including the fee assets).
    feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
  }
}
```
`totalInterest` depends on the new total assets - the last total assets, which is considered the vault's profit where part of it should go to the fee recipient. When removing a pool from the withdrawal queue, and the vault still had some assets in that pool, the `lastTotalAssets` is not updated to reflect this removal.

After removal, `lastTotalAssets` will still reflect the assets deposited in the removed pool. So when `CuratedVaultSetters::_accrueFee` is called through a deposit/withdrawal, `lastTotalAssets` (which reflects a wrong exaggerated value) will be much greater than `newTotalAssets`, `totalInterest` will be 0 so no interest given or fees accrued.

This forces the vault depositors to lose their profit, and vault owners to lose their accrued fees.

### Root Cause

`lastTotalAssets` is not being updated when removing pools from the withdrawal queues, in [`updateWithdrawQueue`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L192-L229).

### Impact

Fees accrued just after calling `updateWithdrawQueue`, will be broken, i.e. no fee shares will be minted.

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract Contest_Vault is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  PoolConfigurator public configurator;
  IPool internal pool_1;
  IPool internal pool_2;
  ICuratedVault internal vault;
  ICuratedVaultFactory internal vaultFactory;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public borrower = makeAddr('borrower');
  address public feeRecipient = makeAddr('feeRecipient');
  address public owner = makeAddr('owner');
  address internal allocator = makeAddr('allocator');
  address internal curator = makeAddr('curator');
  address internal guardian = makeAddr('guardian');

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    configurator = new PoolConfigurator(address(poolFactory));
    poolFactory.setConfigurator(address(configurator));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _setupPool1() internal {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      ltv: 0,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    DataTypes.InitPoolParams memory p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
    poolFactory.createPool(p);
    pool_1 = poolFactory.pools(0);
  }

  function _setupPool2() internal {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      ltv: 8000,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    DataTypes.InitPoolParams memory p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
    poolFactory.createPool(p);
    pool_2 = poolFactory.pools(1);
  }

  function _setUpCuratedVault() internal {
    CuratedVault instance = new CuratedVault();
    vaultFactory = ICuratedVaultFactory(new CuratedVaultFactory(address(instance)));

    address[] memory admins = new address[](1);
    address[] memory curators = new address[](1);
    address[] memory guardians = new address[](1);
    address[] memory allocators = new address[](1);
    admins[0] = owner;
    curators[0] = curator;
    guardians[0] = guardian;
    allocators[0] = allocator;

    vault = vaultFactory.createVault(
      ICuratedVaultFactory.InitVaultParams({
        revokeProxy: true,
        proxyAdmin: owner,
        admins: admins,
        curators: curators,
        guardians: guardians,
        allocators: allocators,
        timelock: 1 weeks,
        asset: address(USDC),
        name: 'Vault',
        symbol: 'VLT',
        salt: keccak256('salty')
      })
    );

    vm.startPrank(owner);
    vault.setFeeRecipient(feeRecipient);
    vault.setFee(0.05e18);
    vm.stopPrank();
  }

  function _setCap(IPool pool_, uint256 newCap) internal {
    vm.prank(curator);
    vault.submitCap(pool_, newCap);

    vm.warp(block.timestamp + vault.timelock());

    vault.acceptCap(pool_);

    IPool[] memory newSupplyQueue = new IPool[](vault.supplyQueueLength() + 1);
    for (uint256 k; k < vault.supplyQueueLength(); k++) newSupplyQueue[k] = vault.supplyQueue(k);
    newSupplyQueue[vault.supplyQueueLength()] = pool_;

    vm.prank(allocator);
    vault.setSupplyQueue(newSupplyQueue);
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setupPool1();
    _setupPool2();
    _setUpCuratedVault();
    _setCap(pool_1, 500e6);
    _setCap(pool_2, 10_000e6);
    _syncOracles();
  }

  function testNotUpdatingLastTotalAssets() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    USDC.mint(bob, USDCamount * 2);
    USDC.mint(borrower, USDCamount);
    WETH.mint(borrower, WETHamount);

    // Bob deposits 1k USDC into the vault, 500 to pool 1 and 500 to pool 2
    vm.startPrank(bob);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, bob);
    vm.stopPrank();

    // Borrower supplies 1 WETH to pool 1 and borrows 500 USDC from pool 2
    vm.startPrank(borrower);
    WETH.approve(address(pool_2), type(uint256).max);
    pool_2.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool_2.borrowSimple(address(USDC), borrower, USDCamount / 2, 0);
    vm.stopPrank();

    // Curator sets the cap of pool 1 to 0
    vm.startPrank(curator);
    vault.submitCap(pool_1, 0);
    vault.submitMarketRemoval(pool_1);
    vm.stopPrank();

    // Some time passes
    // Some interest is accumulated on pool 2
    vm.warp(block.timestamp + vault.timelock());
    pool_2.forceUpdateReserves();

    // Borrower repays his debt
    vm.startPrank(borrower);
    USDC.approve(address(pool_2), type(uint256).max);
    pool_2.repaySimple(address(USDC), pool_2.getDebt(address(USDC), borrower, 0), 0);
    vm.stopPrank();

    // Funds aren't withdrawn from pool 1

    uint256 lastTotalAssetsBefore = vault.lastTotalAssets();
    uint256[] memory indices = new uint256[](1);
    indices[0] = 1;

    // Allocator removes the pool from the vault
    vm.prank(allocator);
    vault.updateWithdrawQueue(indices);

    // Last total assets wasn't updated
    assertEq(vault.lastTotalAssets(), lastTotalAssetsBefore);
    // Vault still has some assets in pool 1
    assertGt(pool_1.supplyAssets(address(USDC), vault.positionId()), 0);

    // Some action is performed, i.e. `_accrueFee` was called
    vm.prank(bob);
    vault.deposit(USDCamount, bob);

    // No fees were accrued by the vault
    assertEq(vault.balanceOf(feeRecipient), 0);
  }
}
```


</details>

### Mitigation

Subtract the `lastTotalAssets` when removing pools from withdrawal queues, by updating `CuratedVault::updateWithdrawQueue` to something like:
```diff
function updateWithdrawQueue(uint256[] calldata indexes) external onlyAllocator {
  uint256 newLength = indexes.length;
  uint256 currLength = withdrawQueue.length;

  bool[] memory seen = new bool[](currLength);
  IPool[] memory newWithdrawQueue = new IPool[](newLength);

  for (uint256 i; i < newLength; ++i) {
    uint256 prevIndex = indexes[i];

    // If prevIndex >= currLength, it will revert CuratedErrorsLib.with native "Index out of bounds".
    IPool pool = withdrawQueue[prevIndex];
    if (seen[prevIndex]) revert CuratedErrorsLib.DuplicateMarket(pool);
    seen[prevIndex] = true;

    newWithdrawQueue[i] = pool;
  }

+ uint256 assetsRemoved;

  for (uint256 i; i < currLength; ++i) {
    if (!seen[i]) {
      IPool pool = withdrawQueue[i];

      if (config[pool].cap != 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroCap(pool);
      if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.PendingCap(pool);
      if (pool.supplyShares(asset(), positionId) != 0) {
        if (config[pool].removableAt == 0) revert CuratedErrorsLib.InvalidMarketRemovalNonZeroSupply(pool);
        if (block.timestamp < config[pool].removableAt) {
          revert CuratedErrorsLib.InvalidMarketRemovalTimelockNotElapsed(pool);
        }
      }

+     assetsRemoved += pool.supplyAssets(asset(), positionId);
      delete config[pool];
    }
  }

  withdrawQueue = newWithdrawQueue;
+ _updateLastTotalAssets(lastTotalAssets.zeroFloorSub(assetsRemoved));
  emit CuratedEventsLib.SetWithdrawQueue(_msgSender(), newWithdrawQueue);
}
```