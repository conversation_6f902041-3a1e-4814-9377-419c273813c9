Special Velvet Toad

High

# Repayment will revert due to wrong balance mismatch check

### Summary

Repayment will revert due to wrong balance mismatch check: the debt balance is converted into shares using `rayDiv()`, and repayed asset amount is not.

### Root Cause

[This](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L121) part of the code checks if the repayed amount is equal to the difference between the previous and the current debt balances:
```solidity
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```
The problem is that repayed amount is [stored](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L129-L137) as actual repayed asset amount:
```solidity
// If paybackAmount is more than what the user wants to payback, the set it to the
// user input (ie params.amount)
if (params.amount < payback.assets) payback.assets = params.amount;
//...
IERC20(params.asset).safeTransferFrom(msg.sender, address(this), payback.assets);
```
While the debt balance is [converted](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L68-L73) into shares using `rayDiv()` (it applies arithmetic with large numbers, adjusting for higher precision):
```solidity
sharesBurnt = amount.rayDiv(index);
//...
self.debtShares -= sharesBurnt;  //scaled usind RAY = 1e27
```
Thus value returned from `getDebt()` will be much bigger than repayed asset amount:
```solidity
uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
```
```solidity
function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex);
  }

function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```

### Internal pre-conditions

None

### External pre-conditions

None

### Attack Path

The user opens a position and borrows 100 USDC (100e6). Now user's `self.debtShares` = 100e33 (suppose index = 1):
```solidity
sharesMinted = amount.rayDiv(index) = (a * RAY + b / 2) / b = 100e6 * 1e27 / 1 = 100e33
self.debtShares += sharesMinted;
```
User wants to repay 20 USDC (20e6) and calls `repay()`:
```solidity
previousDebtBalance = pool.getDebt() = 100e33

repaid.assets = params.amount = 20e6

//After repayment debt balance decreases by `sharesBurnt` amount:
sharesBurnt = amount.rayDiv(index) = (a * RAY + b / 2) = 20e6 * 1e27 / 1 = 20e33
self.debtShares -= sharesBurnt;

currentDebtBalance = pool.getDebt() = self.debtShares = 100e33 - 20e33 = 80e33

previousDebtBalance - currentDebtBalance = 100e33 - 80e33 = 20e33
```
This check will revert:
```solidity
if (previousDebtBalance - currentDebtBalance != repaid.assets) {  
      revert NFTErrorsLib.BalanceMisMatch();
    }

20e33 != 20e6
```


### Impact

Users will not be able to repay the debt, as the `repay()` function will always revert.

### PoC

See Attack Path section.

### Mitigation

Convert value returned from `getDebtBalance()` into assets:
```diff
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-   return self.debtShares + increase;
+   return self.debtShares.rayMul(index) + increase;
}
```
Or use `repaid.shares` instead:
```diff
- if (previousDebtBalance - currentDebtBalance != repaid.assets) {  
+ if (previousDebtBalance - currentDebtBalance != repaid.shares) {  
      revert NFTErrorsLib.BalanceMisMatch();
  }
```