Joyous Cedar Tortoise

High

# Repaying loans via NFT position manager will always revert. User collateral will be stuck forever.

## Summary

## Vulnerability Detail
```solidity
uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
```
When pool.getDebt() is called the first time, it calls getDebtBalance using the stored borrowIndex. However this may not be up to date.
```solidity
function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex);
}
``` 
Then when the assets are repaid, it calls `updateState()` on the reserve, which updates the `borrowIndex` to be up to date

Then when calculating the `currentDebtBalance`, the borrow index used is up to date.

This means that `currentDebtBalance - previousDebtBalance` will not be equal to `repaid.assets`.

This causes a revert in the next check [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125):
```solidity
if (previousDebtBalance - currentDebtBalance != repaid.assets) {
  revert NFTErrorsLib.BalanceMisMatch();
}
```
## Impact
Loan repayments cannot be made, so user’s collateral is stuck permanently.
## Code Snippet

## Tool used

Manual Review

## Recommendation
call pool.forceUpdateReserve(asset) at the start of _repay() to ensure that the liquidity index used during the repayment is up to date