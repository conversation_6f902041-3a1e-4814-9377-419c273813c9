Clever Ebony Halibut

Medium

# Repay Function often Fails Due to Incorrect Debt Balance Calculation in Position Manager

## Summary

The `repay` function in the `NFTPositionManagerSetters` contract is incorrectly calculating the debt balance, causing the function to revert. The issue arises when the function performs a check to compare the difference between the previous and current debt balances with the repaid amount. This check fails due to the accrual of interest, which leads to a mismatch in debt balance calculation.

## Vulnerability Detail

The vulnerability lies in the `repay` function of the `NFTPositionManagerSetters` contract. The function retrieves the previous debt balance using the `getDebt` function from the `PoolGetters` contract before updating the indexes. It then calls the `repay` function to update the debt and retrieve the repaid amount. Finally, it fetches the current debt balance using `getDebt` again.

The issue occurs when the function compares the difference between the previous and current debt balances with the repaid amount. If there is any interest accrued during this process, which is often the case, the check will fail. This is because the `previousDebtBalance` is obtained using the outdated borrowIndex, while the actual repayment updates the index. As a result, `previousDebtBalance - currentDebtBalance` will be less than the `repaid.assets`, causing the function to revert.

In some cases, if there is a significant amount of interest accrued and only a small repayment is made, the subtraction `previousDebtBalance - currentDebtBalance` may even underflow.

Here's the problematic code snippet from `NFTPositionManagerSetters.sol`:

```js
function repay(DataTypes.RepayParams memory params) external override {
// ... (previous code)

    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
        revert NFTErrorsLib.BalanceMisMatch();
    }

    // ... (remaining code)

}
```

For context, here's the relevant part of the `getDebt` function from `PoolGetters.sol`:

```js
function getDebt(address asset, address user, uint256 tokenId) public view override returns (uint256) {
DataTypes.ReserveData storage reserve = \_reserves[asset];
DataTypes.UserConfigurationMap storage userConfig = \_usersConfig[user][tokenId];

    (uint256 debtShares, ) = userConfig.getDebtShares(asset);
    return debtShares.rayMul(reserve.getNormalizedDebt());

}
```

## Impact

This vulnerability leads to a Denial of Service (DoS) condition for the repay functionality. Users will be unable to successfully repay their debts using the `NFTPositionManagerSetters` contract. As a result, users may be stuck with their debt positions, potentially leading to financial losses and the inability to manage their positions effectively.

## Code Snippet

- https://github.com/example/path/to/NFTPositionManagerSetters.sol#L100-L120

## Tool used

Manual Review

## Recommendation

To address this issue, consider to Force an index update by calling the `forceUpdateReserve` function on the pool contract before calculating the previous debt balance. This ensures that the previous debt balance is calculated using the latest index.

```diff
function repay(DataTypes.RepayParams memory params) external override {
// ... (previous code)

+    pool. pool.forceUpdateReserve(params.asset); // Force update the index
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    // ... (remaining code)

}

```