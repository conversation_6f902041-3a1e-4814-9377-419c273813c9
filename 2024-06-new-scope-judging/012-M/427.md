Chilly Cherry Deer

Medium

# Balance mismatch in `_repay` logic will cause transaction reverts for users

### Summary

The `_repay` function in the `NFTPositionManagerSetters` contract assumes that the difference between `previousDebtBalance` and `currentDebtBalance` should exactly match `repaid.assets`. This logic will not hold true due to potential fees, interest accrual.


### Root Cause

- In [NFTPositionManagerSetters.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125) `previousDebtBalance - currentDebtBalance` should exactly match `repaid.assets` does not account for potential fees, interest accrual in pool. , leading to possible transaction reverts.
```solidity
 if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```
- Interest accrue between the time `previousDebtBalance` is recorded and `currentDebtBalance` is calculated.

### Impact

the users cannot successfully execute the repayment of their debt due to the strict balance check, which does not account for fees, interest accrual. This lead to transaction reverts, preventing users from managing their debt positions which leads to liquidation risks.

#### Example
Lets say:
- Initial Debt Balance: 1000 units
- Intended Repayment: 100 units
- Accrued Interest/Fees: 5 units (assumed for this example)
1. Expected new debt balance(without fee) : 1000 - 100 = 900 units
2. Actual new debt balance(with fee) : 1000 - (100 - 5) =  1000 - 95 = 905
3. Code logic 
```solidity
 if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```
- `(previousDebtBalance - currentDebtBalance != repaid.assets)`
- `(1000 - 905 != 100)`
- Result: Since `95 != 100`, the condition evaluates to `true`, meaning there is a mismatch between the expected and actual reduction in the debt. The system will therefore revert the transaction with error.



### Mitigation

- Modify the balance check to account for potential fees and interest, consider according to interest rate strategy and protocol fee from `IPool`.