Rapid Onyx Meerkat

High

# user can't repay assets due to BalanceMisMatch error

### Summary

When repaying borrowed assets, the protocol compares the repaid assets with the actual decrease in assets. However, it mistakenly compares the amount with the shares, leading to a `BalanceMisMatch` error.

### Root Cause

In `NFTPositionManagerSetters.sol#L123-L125`  mistakenly compares the amount with the shares

```solidity
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);  <@
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);  <@

    if (previousDebtBalance - currentDebtBalance != repaid.shares) { <@
      revert NFTErrorsLib.BalanceMisMatch(); //@audit assets --> shares.
    }
```

pool.getDebt return share instead to amount
```solidity
  function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex);
  }
```
`repaid.assets` is the amount of repay assets
```solidity
  function repay(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    IERC20Upgradeable(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);  <@
    _repay(params);
  }
```
```solidity
    // Allows a user to max repay without leaving dust from interest.
    if (params.amount == type(uint256).max) {
      params.amount = payback.assets;
    }

    ValidationLogic.validateRepay(params.amount, payback.assets);

    // If paybackAmount is more than what the user wants to payback, the set it to the
    // user input (ie params.amount)
    if (params.amount < payback.assets) payback.assets = params.amount;  <@
```


### Internal pre-conditions

1. alice supply assets to pool
2. alice borrow assets from pool
3. alice repay assets to pool

### External pre-conditions

none

### Attack Path

1. alice call nftPositionManager.supply()
2. alice call nftPositionManager.borrow()
3. alice call nftPositionManager.repay()

### Impact

user can't repay assets due to BalanceMisMatch error

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L124

### PoC

add test function to file `NFTPositionManagerTest.t.sol`
```solidity
  function testBalanceMisMatch() public {
    //alice supply. 1
    uint256 mintAmount = 100 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, 3e18, 1, data);

    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);
    vm.stopPrank();

    //alice borrow assets
    params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, 1e18, 1, data);

    vm.startPrank(alice);
    nftPositionManager.borrow(params);
    vm.stopPrank();

    //365 days pass by
    skip(365 days);

    //alice repay assets.
    params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, 60e18, 1, data);

    vm.startPrank(alice);
    nftPositionManager.repay(params);
    vm.stopPrank();
  }
```

out
```shell
Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 4.83ms (1.57ms CPU time)

Ran 1 test suite in 142.74ms (4.83ms CPU time): 0 tests passed, 1 failed, 0 skipped (1 total tests)

Failing tests:
Encountered 1 failing test in test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[FAIL. Reason: BalanceMisMatch()] testBalanceMisMatch() (gas: 924877)
```

### Mitigation

```diff
@@ -120,12 +120,12 @@ abstract contract NFTPositionManagerSetters is NFTRewardsDistributor {
     DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
     uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
 
-    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
-      revert NFTErrorsLib.BalanceMisMatch();
+    if (previousDebtBalance - currentDebtBalance != repaid.shares) {
+      revert NFTErrorsLib.BalanceMisMatch(); //@audit assets --> shares.
     }
```