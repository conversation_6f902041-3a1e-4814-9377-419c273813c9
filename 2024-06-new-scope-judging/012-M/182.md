Innocent Chartreuse Parrot

Medium

# Repayments via the `NFTPositionManager` will be blocked for reserves that have pending interest

## Summary
Repayments via the `NFTPositionManager` will be blocked for reserves that have pending interest

## Vulnerability Detail
When repaying debt via the `NFTPositionManager`, the decrease in the user's debt balance is required to equal exactly the amount of assets repaid:

[NFTPositionManagerSetters::_repay](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L114-L125)
```solidity
    IPool pool = IPool(userPosition.pool);
    IERC20 asset = IERC20(params.asset);


    asset.forceApprove(userPosition.pool, params.amount);


    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);


    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```

However, the pool reserve is not explicitly updated prior to querying the user's debt, and the `getDebt` getter function does not update the pool reserve. Therefore, the `previousDebtBalance` can hold a lower debt value if the pool has pending interest:

[PoolGetters::getDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L94-L97)
```solidity
  function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex); 
  }
```

A pool reserve with pending interest can cause this function call to fail with a `BalanceMisMatch` error. Here is a simple example:

```python
pendingInterest = 1
previousDebtBalance = 5
assetsRepaid = 2

currentDebtBalance = previousDebtBalance + pendingInterest - assetsRepaid = 5 + 1 - 2 = 4

differenceObserved = previousDebtBalance - currentDebtBalance = 5 - 4 = 1

# differenceObserved != assetsRepaid
```

## Impact
All repayments via the `NFTPositionManager` will fail for reserves that have pending interest. Note that the obvious work around is to call `pool.forceUpdateReserve` before repayment, but this flow is not obvious to users. Additionally, repayment can be considered a time-sensitive function as failed repayments can lead to unnecessary liquidations that could have been avoided. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L114-L125

## Tool used

Manual Review

## Recommendation
Pool reserve should be explicitly updated during repayments in `NFTPositionManagerSetters::_repay`.