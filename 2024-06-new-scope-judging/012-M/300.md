Polished Iris Antelope

High

# Repayments will revert on ```NFTPositionManager``` due to outdated debt measurement on ```_repay()```.

## Summary
The check for the repaid amount in ```_repay()``` of ```NFTPositionManager``` is wrongly implemented using outdated borrow index leading to repayments to revert.

## Vulnerability Detail
When someone wants to repay, he is supposed to call ```repay``` function of ```NFTPositionManager``` and pass the ```tokenId``` of his position and the ```amount``` he wants to repay. We can see the implementation here :
```solidity
function repay(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    IERC20Upgradeable(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);
    _repay(params);
}
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L116C1-L120C4)

This function will call ```_repay()``` which executes an extra safety check which is supposed to assure that the debt of the position before the repayment minus the debt of the position after the repayment is equal to the repayment amount. We can the implementation here :
```solidity
function _repay(AssetOperationParams memory params) internal nonReentrant {
    // ...

    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

@>    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }

    // ...
}
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L105C3-L135C4)

However, this check is impossible to be right and pass since the ```borrowIndex``` used in ```previousDebtBalance``` calculation is outdated and as a result it does not represent the actual debt balance of the position at the time of the repayment. In the other hand, after the ```pool.repay()``` call the ```borrowIndex``` will be updated and the ```currentDebtBalance``` will be calculated with the new ```borrowIndex```. We can see the implementation of ```getDebt``` here :
```solidity
function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
@>    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex);
}
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L94C1-L97C4)

Both ```debtBalance``` variables must have been calculated with the current up-to-date ```borrowIndex``` so the difference of them to be the actual repayment amount, otherwise the ```previousDebtBalance``` is not the debt balance just before the repayment but a debt balance of the past time and the difference of it from ```currentDebtBalance``` will not be onlt the repayment amount but also some interest accrued.

## Impact
Due to the outdated debt measurement in ```_repay()```, all repayment attempts on the ```NFTPositionManager``` will fail, making it impossible for users to repay their loans. This results in the protocol becoming practically unusable for all borrowers, potentially leading to liquidations if users cannot manage their positions.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L105C3-L135C4
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L94C1-L97C4
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L116C1-L120C4

## Tool used
Manual Review

## Recommendation
Consider calling ```forceUpdateReserve()``` inside ```_repay()``` so the debt measurement to be up to date.
```diff
function _repay(AssetOperationParams memory params) internal nonReentrant {
    // ...

+   pool.forceUpdateReserve(params.asset); 

    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
    // ...
  }
```