Acrobatic Rainbow Grasshopper

High

# Repayments using the NFT position manager will revert in a lot of cases

## Summary
Repayments using the NFT position manager will revert in a lot of cases
## Vulnerability Detail
Users can interact with the NFT position manager instead of directly interacting with the pool, that way they can earn some extra rewards using a staking mechanism. Let's take at a part of the code responsible for repaying:
```solidity
uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```
First, we calculate the previous debt, then we repay and then we calculate the new debt balance. Then, we have a type of invariant check to confirm that the difference between the previous and new debt is equal to what we repaid. Let's take a look at the code responsible for calculating the debt:
```solidity
  function getDebt(address asset, address who, uint256 index) external view returns (uint256 debt) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getDebtBalance(_reserves[asset].borrowIndex);
  }
```
As seen, we get the position and we call another function to calculate the debt using the current borrow index cached in the reserves mapping. The issue is that upon calculating the `previousDebtBalance`, we use an outdated borrow index. Here is the a part of code called upon the actual repayment:
```solidity
reserve.updateState(params.reserveFactor, cache);
payback.assets = balances.getDebtBalance(cache.nextBorrowIndex);
```
`ReserveLogic::updateState()` updates the next borrow index in the `cache` struct and in the reserves, then we calculate the debt of the position. The `currentDebtBalance` is also updated based on the new borrow index. Thus, the invariant check is not actually correct and can cause a similar scenario:
1. `previousDebtBalance` is 90 as it uses an outdated borrow index
2. User tries to repay all of his debt, which is calculated to be 100 based on the new borrow index
3. `currentDebtBalance` is 0 as he repaid all of his debt
4. `90 - 0  != 100`, thus the function will revert

Paste the following POC into `NFTPositionManagerTest.t.sol`:
```solidity
  function testShouldRevertPOC() public {
    address supplier = makeAddr('supplier');
    address borrower = makeAddr('borrower');
    _mintAndApprove(supplier, tokenA, 1e18, address(nftPositionManager));
    _mintAndApprove(borrower, tokenA, 2e18, address(nftPositionManager));
    _mintAndApprove(borrower, tokenB, 2e18, address(nftPositionManager));
    vm.startPrank(supplier);
    uint256 tokenId = nftPositionManager.mint(address(pool));
    INFTPositionManager.AssetOperationParams memory params = INFTPositionManager.AssetOperationParams(address(tokenA), supplier, 1e18, tokenId, DataTypes.ExtraData(bytes(''), bytes('')));
    nftPositionManager.supply(params);
    vm.stopPrank();

    vm.startPrank(borrower);
    uint256 tokenIdBorrower = nftPositionManager.mint(address(pool));
    INFTPositionManager.AssetOperationParams memory paramsCollateral = INFTPositionManager.AssetOperationParams(address(tokenB), borrower, 2e18, tokenIdBorrower, DataTypes.ExtraData(bytes(''), bytes(''))); // Craft collateral supply params
    nftPositionManager.supply(paramsCollateral); // Supply collateral
    INFTPositionManager.AssetOperationParams memory paramsBorrow = INFTPositionManager.AssetOperationParams(address(tokenA), borrower, 1e18, tokenIdBorrower, DataTypes.ExtraData(bytes(''), bytes(''))); // Craft borrow params
    nftPositionManager.borrow(paramsBorrow); // Borrow

    vm.warp(block.timestamp + 365 days); // Commenting this out will make the function not revert as index wouldnt have changed
    INFTPositionManager.AssetOperationParams memory paramsRepay = INFTPositionManager.AssetOperationParams(address(tokenA), borrower, 2e18, tokenIdBorrower, DataTypes.ExtraData(bytes(''), bytes(''))); // Craft repay params
    vm.expectRevert(NFTErrorsLib.BalanceMisMatch.selector);
    nftPositionManager.repay(paramsRepay); // Repay
    vm.stopPrank();
  }
```
## Impact
Repayments using the NFT position manager will revert in a lot of cases
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L125
## Tool used

Manual Review

## Recommendation
Force update the reserve before calculating the previous debt