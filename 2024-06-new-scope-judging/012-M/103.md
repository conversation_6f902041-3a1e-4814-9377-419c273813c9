Big Admiral Dove

High

# Repayment through the `NFTPositionManager` will be DOSed due to incorrect post-validation.

## Summary

In the `NFTPositionManagerSetters:_repay()` function, a post-validation exists that checks if the correct amount of assets were repaid into the pool.

This incorrect validation will cause DOS of the whole repayment process of `NFTPositionManager`.

## Vulnerability Detail

The post-validation [positions/NFTPositionManagerSetters.sol:L123-L125](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125) checks if the repaid amount strictly equals with the difference between balances before and after the repayment. But such strict equality check often cause denial-of-service for many cases, and this one is no exception.
```solidity
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
```

If some time goes by after borrowing, the repaid amount cannot be exactly same with the difference between before and after balances because amount of debt assets is generaly calculated by multiplying `debtShares` and `borrowIndex`(because at this moment, `borrowIndex` became a complex value(> 1) that the interest rate strategy was applied).

### Proof-Of-Concept

Some console logs are added to `_repay()` function for test purpose:

```solidity
  function _repay(AssetOperationParams memory params) internal nonReentrant {
    ... ...

    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    console.log('previousDebtBalance', previousDebtBalance);
    console.log('currentDebtBalance', currentDebtBalance);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }

    ... ...
  }
```

Here are the test case to demonstrate the vulnerability:

```solidity
// Add this test case  to NFTPositionManagerTest.t.sol
  function testShouldRepayAlicePoc() external {
    testShouldSupplyAlice();

    uint256 repayAmount = 10 ether;
    uint256 borrowAmount = 20 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 1, data);

    vm.startPrank(alice);
    nftPositionManager.borrow(params);
    assertEq(tokenA.balanceOf(address(pool)), 30 ether, 'Pool Revert');
    assertEq(tokenA.balanceOf(alice), 70 ether, 'Alice Revert');

    vm.warp(block.timestamp + 1 hours); // <-- Here, passes 1 hour
    pool.forceUpdateReserve(address(tokenA));

    params.amount = repayAmount;
    vm.expectRevert(NFTErrorsLib.BalanceMisMatch.selector);
    nftPositionManager.repay(params);
    vm.stopPrank();
  }
```

And the logs:
```bash
$ forge test --match-test testShouldRepayAlicePoc -vvv
[⠒] Compiling...
[⠰] Compiling 5 files with Solc 0.8.19
[⠔] Solc 0.8.19 finished in 6.27s
Compiler run successful!

Ran 1 test for test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[PASS] testShouldRepayAlicePoc() (gas: 857082)
Logs:
  previousDebtBalance 20000136015229690478
  currentDebtBalance 10000068007152344817

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 3.42ms (937.80µs CPU time)

Ran 1 test suite in 9.74ms (3.42ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

As can be seen from the logs, 10e18 assets were repaid to the pool, but the actual difference is 10000068008077345661.

## Impact

The repayment operation of `NFTPositionManager` will be entirely DOSed, because almost all repayments are performed after some time passes from borrowings.

This means that NFT Position owners will never repay their debts.

## Code Snippet

[positions/NFTPositionManagerSetters.sol:L123-L125](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125)

## Tool used

Manual Review

## Recommendation

The issued post-validation is actually unnecessary, so I'd suggest removing those lines.

