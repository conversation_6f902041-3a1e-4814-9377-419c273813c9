Special Macaroon Shetland

High

# `debtBalance` is calculated wrongly and it can cause users can't repay their whole debt amount

### Summary

In `PositionBalanceConfiguration.sol`, a users's debt balance is calculated using `getDebtBalance()` function. Due to wrong calculation logic, it will cause users can't repay their whole debt amount.

### Root Cause

In [PositionBalanceConfiguration::getDebtBalance()](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L139), convert users debt shares to debt assets amount. Because of wrong logic, it will lead debt repayment problems. It directly adds debtShares with increase amount which is wrong implementation. `debtShares` is calculated in following way:

```solidity
  function borrowDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
&>  sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.debtShares == 0;
    self.lastDebtLiquidtyIndex = index;
    self.debtShares += sharesMinted;
    totalSupply.debtShares += sharesMinted;
  }
```
But debt balance calculated wrongly with following logic:
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase; // @audit addition operation between shares and assets
  }
```

While calculation of the assets from `debtShares`, it have to multiply `debtShares` with `nextBorrowingIndex`. 

### Internal pre-conditions

1. `nextBorrowIndex` should be different than 1.0

Because in 1.0 point it will be exactly same even the wrong logic.

### Attack Path

1. Alice borrows 110 WETH while borrowing index is 1.10 
2. Protocol will calculate 100 debt shares for her.
3. She wants to close her debt while borrowing index is 1.15.
4. Protocol will calculate her debt as 105 WETH ( increase will be 5 because of index change ) 
5. She couldn't close her debt, her real debt is equal to 115 WETH after interests.

### Impact

High - Correct calculation for debt assets amount is really important in ZeroLend protocol. It will cause wrong logic in repayments of the debts. In current system, there is no way to repay whole debt.

In conclusion, if users can't repay their whole debt.

### Mitigation

Following way it will calculate the correct debt assets:

```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    return self.debtShares.rayMul(index);
  }
```

In Aave-v3, the same implementation made with following way:
`IERC20(reserveCache.variableDebtTokenAddress).balanceOf(user)`

VariableDebtToken contract always rescale shares to assets by default in `balanceOf` calculation. 

Please see [VariableDebtToken.sol](https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/VariableDebtToken.sol#L80)
