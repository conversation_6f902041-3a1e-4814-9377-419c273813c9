Powerful Licorice Elephant

High

# self.supplyShares is not converted into amounts when withdrawing supply amounts.

## Summary
self.supplyShares is not converted into amounts in the  function getSupplyBalance.As a result, user’s full withdrawal amount calculation will be less than the actual full withdrawal amount and Users will get incorrect withdrawal amount than actual withdrawal amounts when withdrawing the full withdrawal amounts.

## root cause
self.supplyShares is not converted into amounts in the  function getSupplyBalance.


## Vulnerability Detail
when function executeWithdraw is called to withdraw the full withdrawals amounts of a position’s collateral asset, it’s calls PositionBalanceConfiguration’s function getSupplyBalance to calculate the full withdrawal amounts i.e balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);

See function getSupplyBalance, here increase i.e interest is calculated( converted into amount from share). But self.supplyShares is not converted into amounts. Without converting  self.supplyShares into amounts, self.supplyShares is added with interest and full supply balance is returned which is incorrect. As a result, user’s full withdrawal amount calculation will be incorrect than the actual full withdrawal amount.

## Impact
 Users will get an incorrect withdrawal amount than actual withdrawal amounts when withdrawing the full withdrawal amounts.


## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126
## Tool used

Manual Review

## Recommendation
convert self.supplyShares into amounts in the  function getSupplyBalance.
