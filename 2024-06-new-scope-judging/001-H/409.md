Shiny Seaweed Mongoose

High

# Lenders who deposits after a loan has been repaid will lose some of their deposits.

### Summary

The share allocation system is broken, lenders who deposit their funds into the pool after some borrowing and repaying is done will lose some of their deposits due to an error in the share allocation system.


### Root Cause

The root cause is how shares are minted based on the new liquidity index. As the liquidity index increases the subsequent lenders earn fewer shares for the same amount.

Liquidity index increases when interest is accrued.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L44

```js
  function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
@ ->  sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
  }
```

### Internal pre-conditions

Lending, borrowing and repaying of loans have occurred in the pool.

### External pre-conditions

_No response_

### Attack Path

1. Alice deposits 1000 USD
2. After a year Bob borrows 700 USD and repays after 800 USD total 1.e 100 USD interest.
3. Mark deposits 1000 USD
4. Mark checks his balance it's less than 1000 USD


### Impact

Subsequent lenders will lose some part of their deposits.

### PoC


Add the following test to the `forge/core/pool` folder and run the test.

This is the output.

Supply 1 Balance:  2018566015738851646750
Supply 2 Balance:  1981604747534545244524


```js
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import './PoolSetup.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';

import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

contract LiquidationFSLPOC is PoolSetup {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;

  address borrower  = makeAddr('borrower');

  address supplier1 = makeAddr("supplier 1");
  address supplier2 = makeAddr("supplier 2");

  address liquidator = makeAddr("liquidator");

  uint256 mintAmountA = 2000 ether;
  uint256 mintAmountB = 2000 ether;
  uint256 supplyAmountA = 1000 ether;
  uint256 supplyAmountB = 2000 ether;
  uint256 borrowAmountB = 500 ether;

  function setUp() public {
    _setUpPool();

    pos = keccak256(abi.encodePacked(borrower, 'index', uint256(0)));
  }

  function testLiquidationSimple() external {

    oracleA.updateAnswer(5e10);
    oracleB.updateAnswer(5e10);

    _mintAndApprove(borrower, tokenA, mintAmountA, address(pool)); // borrower 500 tokenA
    _mintAndApprove(borrower, tokenB, mintAmountB, address(pool)); // borrower 500 tokenA

    _mintAndApprove(supplier1, tokenB, mintAmountB, address(pool)); // supplier1 2000 tokenA
    _mintAndApprove(supplier2, tokenB, mintAmountB, address(pool)); // supplier2 2000 tokenB
    _mintAndApprove(liquidator, tokenB, mintAmountB, address(pool)); // max 2000 tokenB

    vm.prank(supplier1);
    pool.supplySimple(address(tokenB), supplier1, supplyAmountB, 0); // 750 tokenB supplier1 supply

    vm.startPrank(borrower);
    pool.supplySimple(address(tokenA), borrower, supplyAmountA, 0); // 500 tokenA borrower supply
    pool.borrowSimple(address(tokenB), borrower, borrowAmountB, 0); // 700 tokenB borrower borrow
    vm.stopPrank();

    vm.warp(block.timestamp + 52 weeks);

    oracleA.updateAnswer(5e10);
    oracleB.updateAnswer(5e10);

    vm.prank(borrower);
    pool.repaySimple(address(tokenB), type(uint).max, 0);

    vm.prank(supplier2);
    pool.supplySimple(address(tokenB), supplier2, supplyAmountB, 0); // 750 tokenB supplier2 supply

    console.log("Supply 1 Balance: ", pool.getBalance(address(tokenB), address(supplier1), 0));

    console.log("Supply 2 Balance: ", pool.getBalance(address(tokenB), address(supplier2), 0));

  }

}
```


### Mitigation

_No response_