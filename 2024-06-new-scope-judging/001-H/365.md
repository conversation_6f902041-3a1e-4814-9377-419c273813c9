Curly <PERSON>

High

# Incorrect balance calculation will cause user loss their supply or borrower steal tokens from pool

### Summary

The two calculation `getSupplyBalance()` and `getDebtBalance()` implemented in `contracts/core/pool/configuration/PositionBalanceConfiguration.sol` is incorrect which return less than the actual correct value. The amount returned by `getSupplyBalance()` will cause users not able to withdraw completely. Likewise, `getDebtBalance()` will allow borrower to repay less than they should and attackers could exploit this to steal tokens from the pool.

### Root Cause

`getSupplyBalance()` and `getDebtBalance()` should return normalized amount, not amount in shares which is divided by index in ray.
- In [`PositionBalanceConfiguration.sol:128`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128) added two value in different unit
- In [`PositionBalanceConfiguration.sol:139` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L139)added two value in different unit

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

_No response_

### PoC

_No response_

### Mitigation

_No response_