Smooth Carbon Narwhal

High

# The balance calculation is incorrect

## Summary
The current `balance` should be the product of `shares` and the current `index`. 
All protocol functions rely on these `balances`, but the calculations are currently incorrect.
## Vulnerability Detail
The current `supply balance` should be calculated by multiplying the `supply shares` by the current `liquidity index`, while the `debt balance` should be the product of `debt shares` and the current `borrow index`. 
This is because the `index` reflects accumulated `interest`.

However, in `line 128`, the `getSupplyBalance` function returns an incorrect `supply balance`.
```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
  uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);

128:  return self.supplyShares + increase;
}
```
Similarly, the `getDebtBalance` function returns an incorrect `debt balance` in `line 139`. 
```solidity
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
  uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);

139:  return self.debtShares + increase;
}
```
In both cases, the correct approach is to simply multiply the current `shares` by the current `index`, with the `increase` reflecting the net gain due to `interest`.
## Impact
These incorrect calculations disrupt the core functionality of the protocol, as many operations depend on accurate balance figures.
One example is the `withdrawal process`. 
When users attempt to `withdraw`, they can only `withdraw` up to their `supply balance`. 
However, the calculated `supply balance` is always less than the actual correct `balance`.
```solidity
function executeWithdraw(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  DataTypes.UserConfigurationMap storage userConfig,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies,
  DataTypes.ExecuteWithdrawParams memory params
) external returns (DataTypes.SharesType memory burnt) {
  DataTypes.ReserveData storage reserve = reservesData[params.asset];
  DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);
  reserve.updateState(params.reserveFactor, cache);

  uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex); // @audit, here

  if (params.amount == type(uint256).max) params.amount = balance;

  ValidationLogic.validateWithdraw(params.amount, balance);
}
```
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L139
## Tool used

Manual Review

## Recommendation
```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-  uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);

-  return self.supplyShares + increase;
+  return self.supplyShares.rayMul(index);
}

function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-  uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);

-  return self.debtShares + increase;
+  return self.debtShares.rayMul(index);
}
```