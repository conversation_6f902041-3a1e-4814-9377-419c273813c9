Magnificent Scarlet Dove

High

# getDebtBalance returns wrong amount of assets

## Summary
getDebtBalance returns wrong amount of assets because it doesn't converts previous shares to assets.
## Vulnerability Detail
Following is getDebtBalance function 
```solidity
/**
   * @notice Get the rebased assets worth of debt the position has
   * @dev Converts `shares` into `amount` and returns the rebased value
   * @param self The position to fetch the value for
   * @param index The current liquidity index
   */
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```
It is clearly mentioned that the function should return rebased assets but as we can see that it returns self.debtShares + increase
we can see that the increase represents the increased amount of rebased assets which is correct but self.debtShares are not converted to assets.
For example lets assume last debt liquidity index was 20 and 10 assets were supplied so shares would be 10/20. After some time the  debt liquidity index(index in above function) becomes 40 so the above function will return the balance as 10/20 +10 assets whereas 10/20 shares are worth 20 assets. So incorrect value of accrued debt is returned.
## Impact
As incorrect amount of debt is returned the position holder can repay the loan even by paying less assets. 
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L139
## Tool used

Manual Review

## Recommendation
Change the code as follows 
```solidity
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares.rayMul(self.lastDebtLiquidityIndex) + increase;
  }
```