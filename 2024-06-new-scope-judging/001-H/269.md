Magnificent Scarlet Dove

High

# getSupplyBalance returns wrong amount of assets

## Summary
getSupplyBalance returns wrong amount of assets because it doesn't converts previous shares to assets.
## Vulnerability Detail
Following is getSupplyBalance function 
```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```
Following are comments of this function
```solidity
/**
   * @notice Get the rebased assets worth of collateral the position has
   * @dev Converts `shares` into `amount` and returns the rebased value
   * @param self The position to fetch the value for
   * @param index The current liquidity index
   */
```
It is clearly mentioned that the function should return rebased assets but as we can see that it returns self.supplyShares + increase 
we can see that the increase represents the increased amount of rebased assets which is correct but self.supplyShares are not converted to assets.
For example lets assume last supply index was 20 and 10 assets were supplied so shares would be 10/20. After some time the liquidity index(index in above function) becomes 40 so the above function will return the balance as 10/20 +10 assets  whereas 10/20 shares are worth 20 assets. So incorrect value returned.
## Impact
This is a critical function used for calculating the collateral added so as to borrow some other assets. Not only this if the user calls withdraw then its supply balance is checked which will represent incorrect balance thus causing loss to the position holder.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128
## Tool used

Manual Review

## Recommendation
The code should be as follows
```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex)+ increase;
  }
```