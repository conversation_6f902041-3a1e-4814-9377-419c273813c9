Special Macaroon Shetland

High

# Wrong conversion from liquidity shares to assets causing loss of funds

### Summary

The incorrect conversion from liquidity shares to assets in `PositionBalanceConfiguration.sol` is causing a loss of funds for users. The ZeroLend protocol, which is heavily inspired by the Aave-v3 codebase, uses indexes to keep share prices up-to-date. However, there is an error in the conversion process between shares and assets in the position balance. In the current codebase, shares are directly added to assets, which is incorrect.

### Root Cause

In [PositionBalanceConfiguration::getSupplyBalance()](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128) function follows wrong method while conversion of users' shares to users' assets. 


The line directly adds shares to assets, which will lead to incorrect results and potentially cause a loss of funds. The `supplyShares` variable is calculated by dividing the amount of assets by the `liquidityIndex`. 

```solidity
  function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
&>  sharesMinted = amount.rayDiv(index); 
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT); // @audit-info Potential DoS, if index is too high for the amount
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index; // @audit-info interesting variable, can have problems
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
  }
```
However, the `liquidityIndex` can change over time and between blocks. To obtain the correct amount of assets from shares, you need to multiply `supplyShares` by the `nextLiquidityIndex`, which is the most up-to-date index for liquidity. The current implementation follows following way:

```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase; // @audit addition operation between shares and assets
}
```

### Internal pre-conditions

1) Index should be different than 1.0

If the index variable is 1.0, it doesn't convert it wrongly.

### Attack Path

1. Let say current liquidityIndex is 1.05 ( in ray ) and Alice supply 105 WETH to protocol.
2. Protocol will calculate her shares by dividing the WETH amounts to 1.05, now she has 100 shares.
3. Then she wants to withdraw back her supply token in the same block.
4. Protocol will calculate first increase amount but we're in the same block it will be 0, and her shares is equal to 100.
5. Protocol will give 100WETH back to Alice instead of 105WETH and Alice can't reach this anymore.

### Impact

High - This shares conversion is used many contract including the vaults. Assets conversion is crucial and it should be correctly calculated, if not it will lead significant loss of funds.

The calculation will be wrong based on the circumstances, if index is really high, than loss will be massive. For instance, if index is equal to 2.0, loss will be 50% in this calculation.

Even if the withdraw action is not made in same block with supply, ìncrease` variable doesn't save the situation. 
1. Let say index is equal to 1.05 again, Alice supply 105 WETH, she will get 100 shares.
2. Then she called withdraw while index is equal to 1.10.
3. The output will be 105 WETH but it should be 110 WETH after counting the interest rate.

### Mitigation

Following implemention will correct for conversion:

```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    return self.supplyShares.rayMul(index);
}
```

We can also compare it with Aave's way, `scaledBalanceOf` is exactly same thing with `supplyShares` in our codebase:

```solidity
uint256 userBalance = IAToken(reserveCache.aTokenAddress).scaledBalanceOf(msg.sender).rayMul(
      reserveCache.nextLiquidityIndex
);
```