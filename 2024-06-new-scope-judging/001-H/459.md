Puny Orchid Buffalo

High

# getDebtBalance Function's Incorrect Formula for User's Debt Calculation

## Summary
The `getDebtBalance` function's formula for calculating a user's debt is incorrect, resulting in the borrower needing to repay less debt than they actually owe.

## Vulnerability Detail
The code for the `getDebtBalance` function in the `PositionBalanceConfiguration` library is as follows:
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```
The error occurs in the line `return self.debtShares + increase;`. The `self.debtShares` should be multiplied by `self.lastDebtLiquidtyIndex` before adding `increase`. Otherwise, the calculated debt value will be less than the actual debt.

## Impact
The borrower ends up repaying less debt than they actually owe.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

## Tool used
Manual Review

## Recommendation
We recommend the following improvement:
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
     uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-    return self.debtShares + increase;
+    return self.debtShares.rayMul(self.lastDebtLiquidtyIndex) + increase;
  }
```