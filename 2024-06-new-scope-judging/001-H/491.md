Massive Glass Crane

High

# Pool.sol:: Wrongly Implemented getSupplyBalance() , not returning the correct supplied asseets.

### Summary

`getSupplyBalance() `is returning the incorrect value for totalSupply of a position.



### Root Cause


The function getSupplyBalance is expected to return the total assets held by the position using the shares owned by that position.

The `total assets` held by the position is calculated using the equation ,
`sharesOwned` * `currentLiquidityIndex`. 

But the function is calculating something else.
[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129)
```solidity
 function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
=>    return self.supplyShares + increase;//@audit  adding shares with the assets increase.
  }
``` 
By adding the `supplyShares` to the `increase` in assets since the last update , will not give the supplyBalance because the `supplyShares` will never equal to the supplyAssets (unless there are no interest being accumulated in he totalAssets.)

(same issue can be observed in `getDebtBalance()` also, where protocoal assumes the shares = assets)

### Internal pre-conditions
_No response_


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact
The supplyBalance of the user is incorrectly calculated.Hence the user cant withdraw the entire deposited asets evn though the position would be healthy afterwards.


### PoC


### Mitigation
```solidity
 function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    return  self.supplyShares.rayMul(index) 
  } 
```