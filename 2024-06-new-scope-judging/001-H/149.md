Powerful Licorice Elephant

Medium

# users(depositors)  may not get full interest as expected.

## Summary
when users supply an asset 2nd time(or afterwards) after supplying the asset 1st time, users interest is not increased to their supplyshare from lastsupplyindex to currentsupplyindex and the user lastsupplyindex is updated to currentsupplyindex. As a result, when the user withdraw the asset after sometime, so user will get interest from next supplyindex to currentsupplyindex and previous interest will be not be added.
## root cause
when users supply an asset 2nd time(or afterwards) after supplying the asset 1st time, users interest is not increased to their supplyshare.

## internal pre condition
before withdrawal users have to supply the asset 2nd time(afterwards) after supplying the asset 1st time.


## Vulnerability Detail
1st time supply:   current supplyindex = 1.2e18, supply amount = 500e18. So after supplying the user’s   self.supplyShares = 416.6e18,  self.lastSupplyLiquidtyIndex = 1.2e18.

2nd time supply to this reserve:   this time current supplyindex = 1.25e18,supply amounts = 100e18(share of 100e18 amount is 83.3e18), now 83.3e18 is added to self.supplyShares, so updated   self.supplyShares = 416.6+83.3 = 499.9e18 and  self.lastSupplyLiquidtyIndex is updated from 1.2e18 to 1.25e18. But here problem is when the share of 100e18 amount i.e 83.3e18 is calculated, then previous interest i.e interest from index 1.2e18 to 1.2e18 is not added to the self.supplyShares. As a result, users will lose the previous interest.

Withdraw immediately after 2nd time supply:   during 2nd time supply, supplyindex  = 1.25e18,self.lastSupplyLiquidtyIndex = 1.25e18(as updated), now when one user withdraw full amounts,  executeWithdraw function calls getSupplyBalance function to calculate the full withdrawal amounts with interest , see getSupplyBalance function, as now the user’s self.lastSupplyLiquidtyIndex is equal to current supplyindex. So the user’s accrued interest is 0 and the user will only get supplyshare.


## Impact
some users will not get interest fairly for supplying assets in pool.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L48
## Tool used

Manual Review

## Recommendation

add the interest to supplyshare,  when users supply an asset 2nd time(or afterwards) after supplying the asset 1st time.

