Acrobatic Banana Poodle

Medium

# Incorrect supply balance and debt balance calculation will cause multiple issues

### Summary

The `PositionBalanceConfiguration`'s `getSupplyBalance` and `getDebtBalance` functions are supposed to convert `shares` into `amount`. However, their calculation is incorrect and the amount is likely to be underestimated, which may lead to following issues:

1, the userConfig can be incorrectly tagged/untagged for using as collateral or borrowing
2. the user would not be able to repay the real debt balance but only be able to repay up to the underestimated debtBalance
3. withdrawing the entire balance using `type(uint256).max` would not function properly

### Root Cause

The `PositionBalanceConfiguration`'s `getSupplyBalance` and `getDebtBalance` calculation is incorrect, in that they are mixing up the shares and amount and adding them.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

### Internal pre-conditions

- The index for debt or collateral is bigger than one, which is likely.

### External pre-conditions

_No response_

### Attack Path

1. When users withdraw or repay, their functionality might not work properly and possibly cause problems.

### impact

The incorrect result of `getSupplyBalance` and `getDebtBalance` was used in the SupplyLogic and BorrowLogic:

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L139-L142

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L155

The possible impacts are:

1, the userConfig can be incorrectly tagged/untagged for using as collateral or borrowing
2. the user would not be able to repay the real debt balance but only be able to repay up to the underestimated debtBalance
3. withdrawing the entire balance using `type(uint256).max` would not function properly

### PoC

_No response_

### Mitigation

```solidity
// PositionBalanceConfiguration.sol

  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index);
  }

  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-     uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-     return self.debtShares + increase;
+     return self.debtShares.rayMul(index);
  }
```


