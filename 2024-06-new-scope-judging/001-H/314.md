Clever Ebony Halibut

High

# Incorrect Debt Calculation in getDebtBalance() Function Leads to Underestimation of Borrower Debt

## Summary

The `getDebtBalance()` function in the PositionBalanceConfiguration contract incorrectly calculates borrower debt. It returns an underestimated debt balance, effectively allowing borrowers to borrow more than they should and leading to protocol insolvency and bad debts.

## Vulnerability Detail

The vulnerability in the PositionBalanceConfiguration contract stems from an incorrect calculation in the `getDebtBalance()` function, leading to an underestimation of borrower debt and potential risk to the protocol's solvency.

When a user borrows tokens from the protocol, they accumulate debt shares. These shares represent their portion of the total debt in the pool. The value of these shares increases over time as interest accrues.

The issue arises during debt calculation. The `getDebtBalance()` function is responsible for calculating the current value of a user's debt. However, its mathematical formula is flawed.

The current formula attempts to calculate the increase in debt by subtracting the initial value of debt shares from their current value. This approach fails to account for the full appreciation of the debt over time.
```js
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```
Mathematically, the current formula can be expressed as:
$shares + (shares * currentIndex - shares * initialIndex)$

For example, if a user borrows 100 tokens when the debt index is 2, they would receive 50 debt shares. If the debt index later increases to 4 due to accrued interest, their debt should be 200 tokens. However, the current formula would calculate:

$(50 * 4 - 50 * 2) + 50 = 150$

- This result incorrectly suggests that the user's debt is `150` tokens, which is less than their actual debt. The issue becomes even more pronounced when we consider multiple borrowing events  or high borrowIndex.

## Impact
- Borrowers can borrow more than their collateral should allow.
- user can never payback full debt
- insolvency of the protocol due to undercollateralized positions
- Broken protocol accounting, leading to discrepancies between actual and reported debt

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

## Tool used

Manual Review

## Recommendation

- The correct calculation should simply multiply the number of shares by the current index:
  $DebtShares * currentDebtIndex$

```diff
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-    return self.debtShares + increase;
+    return self.debtShares.rayMul(index);

  }
```