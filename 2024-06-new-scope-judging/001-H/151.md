Mythical Alabaster Snake

High

# User supply balance is not tracked properly

when a user supplies or withdraw, lastSupplyLiquidtyIndex is updated without consideration for the users previous deposits(supply) and if the user did not withdraw entire amount(withdraw)
this value is used in determining a position increase but it is always incremented to the latest index without any means of tracking the interest gained by the user before it was incremented 


a very simple example would be bob deposits 10 eth at 1.01 index, bob would be minted 10/1.01 = 9.90099009901e18
assuming bob wants to withdraw immediately, bob shouldn't have gained any interest but he also shouldn't have lost any cash, however following the balance function 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L38-L50

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L85-L95


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129
note: this function is also implemented wrongly and is covered in another finding

  since index == lastSupplyLiquidtyIndex
  bob would only get 9.90099009901e18 tokens back 

however even if it was, as long as lastSupplyLiquidtyIndex is used to calculate balance, the function would always calculate from the last completely ignoring profit earned before it was last updated 

a proper solution would be to modify the position shares 
```solidity
if(lastSupplyLiquidtyIndex) != 0
lastSupplyLiquidtyIndex = (old shares + increase in shares) * index / ((old shares * index / lastSupplyLiquidtyIndex ) + new amount)
```
using this a positions interest is added to their supply 