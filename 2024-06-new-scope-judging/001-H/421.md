Joyous Rainbow Shark

High

# `PositionBalanceConfiguration::getDebtBalance()` adds interest in assets to a shares balance which prevents full repay of debt and completely DOSes `nftPositionManager::repay()`

### Summary

Users are unable to fully repay their debt through the `pool::repay()` flow and the `nftPositionManager::repay()` is completely DOSed due to `PositionBalanceConfiguration::getDebtBalance()` returning a lower than expected debt balance. `getDebtBalance()` should convert the 'base' debt to a value denominated in assets before returning.

### Root Cause

[`PositionBalanceConfiguration::getDebtBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140) adds an `increase` denominated in 'asset amount' to `self.debtShares` denominated in shares:

```javascript
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
@>  return self.debtShares + increase;
  }
```

This results in the function returning lower than expected debt balances. 

I will focus on explaining how this issue impacts the repayment flows in the protocol. 

1. The `NFTPositionManager::repay()` call [invokes `NFTPositionManagerSetters::_repay()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L119) which [calls `getDebt()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L121) twice. Each call then [invokes `getDebtBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L96). This logic supposedly tracks the debt balance pre and post repayment, and [their difference is compared to `repaid.assets`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125) to ensure the actual debt repaid matches the expected debt repaid.
The issue here is that `repaid.assets` is in 'assets' and the return value of `getDebt()` is primarily in shares, thus these values won't match for any repaid amount which completely bricks `NFTPositionManager::repay()`. 

2. A user also cannot repay through the `Pool::repay()` flow due to the same issue in `getDebtBalance()`. `Pool::repay()` [invokes `PoolSetters::_repay()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L121) which [calls `BorrowLogic::executeRepay()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L129-L143). This function sets `payback.assets` to the [return value of `getDebtBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126) which contains the bug. Finally, this [check can only scale down the value of `payback.assets`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L137). The value of `payback.assets` is then used to [repay the debt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L158).    


### Internal pre-conditions

1. Interest needs to have accrued on a borrowed asset, with borrowIndex updated

### External pre-conditions

_No response_

### Attack Path

1. User mints an NFT position, supplying collateral, and taking debt
2. Interest accrues on debt
3. User is unable to repay the debt through the `NFTPositionManager::repay()` function

Or
1. User has debt in a pool
2. User attempts to repay the full debt
3. The repayment is capped at the return value of `getDebtBalance()`, so all debt is not repaid.

### Impact

1. The `nftPositionManager::repay()` flow is DOSed for all repay values due to the described bug and a strict equality in `NFTPositionManagerSetters::_repay()`
2. The user is unable to repay their full debt through the `Pool::repay()` flow
3. `NFTPositionManagerSetters::_borrow()` calculates debt amount incorrectly for the puposes of reward calculations. Debt is understated and rewards will therefore be earned at a lower rate due to lower 'total balance' 

### PoC

Create a new file in `/test/forge/core/positions/` and run `test_POC_NFTPositionManagerRepayDos()`. It shows a simple supply/borrow/repay flow through an NFT position where a repayment of any amount will revert. 

```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {NFTPostionManagerTest} from './NFTPositionManagerTest.t.sol';
import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {NFTErrorsLib} from 'contracts/interfaces/errors/NFTErrorsLib.sol';
import {console2} from 'forge-std/src/Test.sol';

contract NFTPostionManagerRepayDOS is NFTPostionManagerTest {

  function test_POC_NFTPositionManagerRepayDos() external {
    testShouldSupplyAlice();
    uint256 repayAmount = 10 ether;
    uint256 borrowAmount = 20 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 1, data);

    vm.startPrank(alice);
    nftPositionManager.borrow(params);
    assertEq(tokenA.balanceOf(address(pool)), 30 ether, 'Pool Revert');
    assertEq(tokenA.balanceOf(alice), 70 ether, 'Alice Revert');

    vm.warp(block.timestamp + 1 days); // fast forward some time to accrue interest
    pool.forceUpdateReserves();
    assert(pool.getReserveData(address(tokenA)).borrowIndex > 1e27); // Prove some interest has accrued on borrows, ie borrowAssets : borrowShares is not 1:1

    params.amount = repayAmount;

    vm.expectRevert(NFTErrorsLib.BalanceMisMatch.selector); // will revert due to the bug in getDebt
    nftPositionManager.repay(params);

    vm.stopPrank();
  }
}
```

### Mitigation


```diff
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-   uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-   return self.debtShares + increase;
+   return self.debtShares.rayMul(index);
  }
```