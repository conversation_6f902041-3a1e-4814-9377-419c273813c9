Great Jade Shetland

High

# `PositionBalanceConfiguration::getSupplyBalance` returns shares instead of assets, forcing curated vault depositors to lose funds

### Summary

When users deposit funds into a curated vault, these funds are automatically deposited into different supply pools. A vault's assets are always being tracked by both `lastTotalAssets` and `totalAssets()`, where the first is a cached value of `totalAssets()` that gets the assets from every supply pool. It uses `PoolGetters::getBalanceByPosition`, which also uses `PositionBalanceConfiguration::getSupplyBalance` to get the underlying assets of the vault's position. However, `getSupplyBalance` returns the position's shares instead of the assets.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

At first, this won't be an issue, as assets to shares will be 1:1, however, when loan repayments occur, pools will profit interest, and then the value of a share will be > 1 asset, breaking the 1:1 rule. Which in return will return wrong/lowered values in `CuratedVault::totalAssets()`, this affects future assets to shares and shares to assets calculations.

This forces users to withdraw way less funds than what they originally deposited, knowing that the corresponding pools would've gained some profit.

### Root Cause

`PositionBalanceConfiguration::getSupplyBalance` is returning shares + assets, where `self.supplyShares` represents the user's shares, while `increase` represents the gained interest in assets. This forces `CuratedVault::totalAssets` to always return a wrong, lowered value, especially after the corresponding pool gains some interest profit from loan repayments.

### Impact

Users who deposit funds into Curated Vaults will lose funds, forcing them to withdraw less of what they originally deposited.

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract ContestTest is Test {
  // Core
  PoolFactory public poolFactory;
  Pool public poolImplementation;
  PoolConfigurator public configurator;
  DefaultReserveInterestRateStrategy public irStrategy;
  ICuratedVault internal vault;
  ICuratedVaultFactory internal vaultFactory;
  IPool internal pool;

  // Tokens
  WETH9Mocked public WETH;
  USDCMocked public USDC;

  // Oracles
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public owner = makeAddr('owner');
  address internal allocator = makeAddr('allocator');
  address internal curator = makeAddr('curator');
  address internal guardian = makeAddr('guardian');
  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');
  address public borrower = makeAddr('borrower');

  function _setUpCore() internal {
    poolImplementation = new Pool();
    poolFactory = new PoolFactory(address(poolImplementation));
    configurator = new PoolConfigurator(address(poolFactory));
    poolFactory.setConfigurator(address(configurator));

    WETH = new WETH9Mocked();
    USDC = new USDCMocked();

    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);

    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);

    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);

    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);

    DataTypes.InitReserveConfig memory config = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _setUpCuratedVault() internal {
    CuratedVault instance = new CuratedVault();
    vaultFactory = ICuratedVaultFactory(new CuratedVaultFactory(address(instance)));

    address[] memory admins = new address[](1);
    address[] memory curators = new address[](1);
    address[] memory guardians = new address[](1);
    address[] memory allocators = new address[](1);
    admins[0] = owner;
    curators[0] = curator;
    guardians[0] = guardian;
    allocators[0] = allocator;

    vault = vaultFactory.createVault(
      ICuratedVaultFactory.InitVaultParams({
        revokeProxy: true,
        proxyAdmin: owner,
        admins: admins,
        curators: curators,
        guardians: guardians,
        allocators: allocators,
        timelock: 1 weeks,
        asset: address(USDC),
        name: 'Vault',
        symbol: 'VLT',
        salt: keccak256('salty')
      })
    );
  }

  function _setCap(IPool pool_, uint256 newCap) internal {
    uint256 cap = vault.config(pool_).cap;
    bool isEnabled = vault.config(pool_).enabled;
    if (newCap == cap) return;

    PendingUint192 memory pendingCap = vault.pendingCap(pool_);
    if (pendingCap.validAt == 0 || newCap != pendingCap.value) {
      vm.prank(curator);
      vault.submitCap(pool_, newCap);
    }

    if (newCap < cap) return;

    vm.warp(block.timestamp + vault.timelock());

    vault.acceptCap(pool_);

    assertEq(vault.config(pool).cap, newCap, '_setCap');

    if (newCap > 0) {
      if (!isEnabled) {
        IPool[] memory newSupplyQueue = new IPool[](vault.supplyQueueLength() + 1);
        for (uint256 k; k < vault.supplyQueueLength(); k++) {
          newSupplyQueue[k] = vault.supplyQueue(k);
        }
        newSupplyQueue[vault.supplyQueueLength()] = pool_;
        vm.prank(allocator);
        vault.setSupplyQueue(newSupplyQueue);
      }
    }
  }

  function _setSupplyPool() internal {
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = pool;

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _setUpCuratedVault();

    _setCap(pool, type(uint184).max);
    _setSupplyPool();

    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function testVaultsDontProfit() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    USDC.mint(bob, USDCamount);
    USDC.mint(alice, USDCamount);
    USDC.mint(borrower, USDCamount);
    WETH.mint(borrower, WETHamount);

    // Bob deposits 1k USDC
    vm.startPrank(bob);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, bob);
    vm.stopPrank();

    (uint256 poolUSDCBeforeBorrow, , , ) = pool.marketBalances(address(USDC));

    // Borrower supplies 1 WETH and borrows 500 USDC
    vm.startPrank(borrower);
    WETH.approve(address(pool), type(uint256).max);
    pool.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool.borrowSimple(address(USDC), borrower, USDCamount / 2, 0);
    vm.stopPrank();

    vm.warp(block.timestamp + 30 days);

    pool.forceUpdateReserve(address(USDC));

    // Alice deposits 1k USDC
    vm.startPrank(alice);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, alice);
    vm.stopPrank();

    // Borrower repays 500 USDC, pools makes some profit
    vm.startPrank(borrower);
    USDC.approve(address(pool), type(uint256).max);
    pool.repaySimple(address(USDC), pool.getDebt(address(USDC), borrower, 0), 0);
    vm.stopPrank();

    (uint256 poolUSDCAfterBorrow, , , ) = pool.marketBalances(address(USDC));

    // Pool gained some profit
    assertGt(poolUSDCAfterBorrow, poolUSDCBeforeBorrow + USDCamount);

    // Vault's total assets don't match
    // Both Bob and Alice lost money and can withdraw < 1k USDC
    assertNotEq(vault.totalAssets(), vault.lastTotalAssets());
    assertLt(vault.maxWithdraw(bob), USDCamount);
    assertLt(vault.maxWithdraw(alice), USDCamount);
  }
}
```
</details>

### Mitigation

In `PositionBalanceConfiguration::getSupplyBalance`, return the assets + profit instead of shares.

```diff
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
- uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
- return self.supplyShares + increase;
+ return self.supplyShares.rayMul(index);
}
```
