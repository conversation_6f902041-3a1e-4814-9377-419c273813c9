Sneaky Hazelnut Lizard

Medium

# Withdrawing Max colllateral makes the asset Collaterals temporarily unusable because Get Supply balance should return the Total asset but instead it returns Supplyshares + asset value.

### Summary

In the Zerolend contract, there's a critical issue where the `getSupplyBalance` function calculates the user's supply balance by adding **shares value** to **asset value** instead of properly converting the shares into their corresponding asset value. This leads to significant miscalculations in operations such as collateral withdrawals .

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L120-L129

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L117-L121

Correct implementation can be seen in another  library 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveSuppliesConfiguration.sol#L27-L35

impact

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L136-L141

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L85-L89

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L46


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

#### Problem with `getSupplyBalance`
The current implementation of `getSupplyBalance` improperly handles the rebased value by directly adding the supply shares to the interest or asset increase, which results in an incorrect total. Here's the problematic section:

```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
}
```

**Problem:**  
- **Shares value + Asset value:** The function first calculates the value of the increase in liquidity based on the difference between the current and last liquidity index, but instead of returning the correct amount, it incorrectly adds the raw `supplyShares` to the increase, which causes a miscalculation. This creates a compound issue because **shares represent an abstract unit**, not the actual asset value. Therefore, adding them directly to the asset increase causes a skewed result.

#### Correct Approach
In contrast, the correct method used in the Reserve Library, or a similar model, should only return the **rebased asset value** derived from the shares and the liquidity index, without directly adding shares and asset amounts together. Here’s an example: for total Shares

```solidity
function getSupplyBalance(DataTypes.ReserveSupplies storage self, uint256 index) internal view returns (uint256 supply) {
    supply = self.supplyShares.rayMul(index);
}
```

In this example, the shares are properly multiplied by the current liquidity index to derive the actual asset amount. There is no erroneous addition of shares, ensuring accurate rebasing.

### Impacts on Other Functions
This miscalculation propagates through several critical functions that rely on the `getSupplyBalance` result, causing a ripple effect in various parts of the protocol, especially in:

1. **`executeUseReserveAsCollateral`**:
    - In this function, the **incorrect balance** returned by `getSupplyBalance` is used when a user attempts to withdraw collateral.
    - When the `balance` is retrieved for the withdrawal, it includes **both shares and asset values**, causing underestimation of the user’s supply balance.
   
    ```solidity
    uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);
    ```

2. **Withdrawing Maximum Amount**:
    - When a user tries to withdraw with the maximum amount (`type(uint256).max`), the incorrect balance calculation leads to a reversal . 
   
    ```solidity
    if (params.amount == type(uint256).max) params.amount = balance;
    ```

the value passed is shares + assets thus this wrong value is sent to the library to process withdrawal

3. **Collateral Withdrawals**:
    - In the `withdrawCollateral` function, the shares are burnt based on the rebased amount. However, the incorrect initial balance in `getSupplyBalance` leads to incorrect calculations of the shares to burn, and could potentially cause a **reversion** due to invalid amounts.

    ```solidity
    sharesBurnt = amount.rayDiv(index);
    ```


### Impact

1. Inability to withdraw all Supplied assets with type(uint256).max.
2. Users still have dust amount in the pool yet their collateral is set to false causing this asset to be temporarily unable to be used as collateral until msg.sender sets it manually with the executeUseReserveAsCollateral function.

 ```solidity
 // if the user is withdrawing everything then disable usage as collateral
    if (isCollateral && params.amount == balance) {
      userConfig.setUsingAsCollateral(reserve.id, false);
      emit PoolEventsLib.ReserveUsedAsCollateralDisabled(params.asset, params.position);
    }
 ```

Since supply shares cannot be zero because shares + profit passed will still be divided by the liquidity index.

  ```solidity
sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares -= sharesBurnt;
    supply.supplyShares -= sharesBurnt;
 ```

supplyshares still holds somes shares yet we set collateral to false. 

  ```solidity
isFirst = self.supplyShares == 0;
  ```
  ```solidity
  // if this is the user's first deposit, enable the reserve as collateral
    if (isFirst && ValidationLogic.validateUseAsCollateral(cache.reserveConfiguration)) {
      userConfig.setUsingAsCollateral(reserve.id, true);
      emit PoolEventsLib.ReserveUsedAsCollateralEnabled(params.asset, params.position);
    }
  ```
we can only manually set collateral to true to be used again as shares will not be zero and we have made collateral false based on the incorrect calculation.

### PoC

_No response_

### Mitigation

To resolve the issue, the `getSupplyBalance` function should be corrected to properly convert shares into asset values and then return only the rebased value. Here’s how it should be done:

#### Updated `getSupplyBalance`
```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex) + increase;
}
```