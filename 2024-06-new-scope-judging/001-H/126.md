Powerful Licorice Elephant

High

# self.debtShares is not converted into amounts when repaying the  debt amount .

## Summary
self.debtShares is not converted into amounts in the function getDebtBalance. As a result, user’s full debt amount calculation will be incorrect than the actual full debt amount and Users will repay incorrect debt amounts than actual debt when repaying the full debt amount.


## root cause
self.debtShares is not converted into amounts in the function getDebtBalance.

## Vulnerability Detail
when  function executeRepay is called to repay the full debt amounts of a position’s debt asset , it’s calls  PositionBalanceConfiguration’s function getDebtBalance to calculate the full debt amounts i.e payback.assets = balances.getDebtBalance(cache.nextBorrowIndex);


See function getDebtBalance, here increase i.e interest is calculated(converted into amount from share).  But self.debtShares is not converted into amounts. Without converting self.debtShares into amounts, self.debtShares is added with interest and full debt balance is returned which is incorrect. As a result, user’s full debt amount calculation will be incorrect  than the actual full debt amount.


## Impact
Users will repay incorrect debt than actual debt when repaying the full debt amount.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137
## Tool used

Manual Review

## Recommendation
convert the self.debtShares into amounts in the function getDebtBalance.