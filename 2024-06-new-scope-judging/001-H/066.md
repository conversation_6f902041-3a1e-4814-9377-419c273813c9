Careful Fleece Pike

High

# Wrong calculation in `PositionBalanceConfiguration#getSupplyBalance` when `liquidityIndex > ray` will cause broken accounting

### Summary

Wrong calculation in `PositionBalanceConfiguration#getSupplyBalance` when `liquidityIndex > ray` will cause broken accounting.

### Root Cause

Wrong calculation in `PositionBalanceConfiguration#getSupplyBalance`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```

`increase` is the increase in supply **asset** of a position comparing to the `lastSupplyLiquidtyIndex`, but `self.supplyShares` is the amount of supply **shares** of the position. The sum `self.supplyShares + increase` is not equal to amount of supply assets. 

Using the returned value of this function will lead to broken accounting.

### Internal pre-conditions

`liquidityIndex` of a reserve is greater than `ray`.

### External pre-conditions

_No response_

### Attack Path

Accounting will be broken where the function `getSupplyBalance` is used. Here we will demonstrate a vulnerability path for the `Pool#withdraw` function, because `getSupplyBalance` is used at

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118

1. When `liquidityIndex` of `tokenA` is equal to `ray`, Bob supplies `1e18 tokenA`, withdraws with `amount = type(uint256).max`, and can withdraw `1e18 tokenA` back. This is a situation where the accounting is not broken.
2. We simulate increasing `liquidityIndex` of `tokenA` to `1.2e27` by:
   - Adding `MockInterestRateStrategy` that will return a fixed `liquidityRate = 0.2e27`. Meaning the interest rate is `20%` a year.
   - Alice supplies `tokenB` as collateral, and borrows `1e18 tokenA` from Bob.
   - Skipping a year, now `liquidityIndex = 1.2e27`, Alice repays Bob `1.2e18 tokenA`
3. Now `liquidityIndex = 1.2e27`, Bob supplies `1.2e18 tokenA` and withdraws with `amount = type(uint256).max` but can only withdraw `1e18 tokenA` back. There still `1.666e17` shares of Bob left in the pool. This is a situation where the accounting is broken.

### Impact

Accounting will be broken where the function `getSupplyBalance` is used. For example:
- In case of the vulnerability path above, Bob can not withdraw all the supply, he has to repeatedly call to withdraw, but still left some assets behind
- `CuratedVault#totalAssets` will not return the correct amount of assets of the vault
- ...

### PoC

Run command: `forge test --match-path test/PoC/PoC.t.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {CorePoolTests} from 'test/forge/core/pool/CorePoolTests.sol';
import {IPool} from 'contracts/interfaces/pool/IPool.sol';

import {MintableERC20} from 'contracts/mocks/MintableERC20.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract MockInterestRateStrategy {
  function calculateInterestRates(
    bytes32,
    bytes memory,
    DataTypes.CalculateInterestRatesParams memory
  ) external returns (uint256 a, uint256 b) {
    a = 0.2e27;
    b = 0.2e27;
  }
}

contract PoolBorrowTests is CorePoolTests {
  IPool pool;
  MockInterestRateStrategy mockIRStrategy;

  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  uint256 mintAmount = 10 ether;
  uint256 borrowAmount = 1 ether;

  function setUp() public {
    _setUpCorePool();

    mockIRStrategy = new MockInterestRateStrategy();

    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));

    // setup users' balance and approval
    tokenA.mint(alice, mintAmount);
    tokenB.mint(alice, mintAmount);

    tokenA.mint(bob, borrowAmount);

    vm.startPrank(alice);
    tokenA.approve(address(pool), type(uint256).max);
    tokenB.approve(address(pool), type(uint256).max);
    vm.stopPrank();

    vm.prank(bob);
    tokenA.approve(address(pool), type(uint256).max);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(tokenA);
    // tokenB acts as collateral to back the loan of tokenA
    assets[1] = address(tokenB);

    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(mockIRStrategy);
    rateStrategyAddresses[1] = address(irStrategy);

    address[] memory sources = new address[](2);
    sources[0] = address(oracleA);
    sources[1] = address(oracleB);

    DataTypes.InitReserveConfig memory config = _basicConfig();

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _basicConfig() internal pure returns (DataTypes.InitReserveConfig memory c) {
    c = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
  }

  function testPoC() external {
    bytes32 pos = keccak256(abi.encodePacked(bob, 'index', uint256(0)));

    // Bob supplies and withdraws when `liquidityIndex` = ray
    vm.startPrank(bob);
    console.log("liquidityIndex: %e", pool.getReserveData(address(tokenA)).liquidityIndex);
    console.log("Bob's balance before: %e", tokenA.balanceOf(bob));
    pool.supplySimple(address(tokenA), bob, tokenA.balanceOf(bob), 0);
    console.log("Bob's balance from `getSupplyBalance`: %e", pool.getBalanceByPosition(address(tokenA), pos));

    pool.withdrawSimple(address(tokenA), bob, type(uint256).max, 0);

    console.log("Bob's balance after: %e", tokenA.balanceOf(bob));
    vm.stopPrank();

    // Simulate increasing `liquidityIndex`
    vm.prank(bob);
    pool.supplySimple(address(tokenA), bob, borrowAmount, 0);

    vm.startPrank(alice);
    pool.supplySimple(address(tokenB), alice, mintAmount, 0);
    pool.borrowSimple(address(tokenA), alice, borrowAmount, 0);
    vm.stopPrank();

    skip(365 days);

    vm.startPrank(alice);
    pool.repaySimple(address(tokenA), type(uint256).max, 0);
    vm.stopPrank();

    console.log("---------------------------------");

    // Bob supplies and withdraws when `liquidityIndex` > ray
    vm.startPrank(bob);
    console.log("liquidityIndex: %e", pool.getReserveData(address(tokenA)).liquidityIndex);
    pool.withdrawSimple(address(tokenA), bob, type(uint256).max, 0);
    console.log("Bob's balance before: %e", tokenA.balanceOf(bob));

    pool.supplySimple(address(tokenA), bob, tokenA.balanceOf(bob), 0);
    console.log("Bob's balance from `getSupplyBalance`: %e", pool.getBalanceByPosition(address(tokenA), pos));

    pool.withdrawSimple(address(tokenA), bob, type(uint256).max, 0);
    console.log("Bob's balance after: %e", tokenA.balanceOf(bob));
    vm.stopPrank();

    console.log("Bob's shares: %e", pool.supplyShares(address(tokenA), pos));
  }
}
```

Logs:
```bash
  liquidityIndex: 1e27
  Bob's balance before: 1e18
  Bob's balance from `getSupplyBalance`: 1e18
  Bob's balance after: 1e18
  ---------------------------------
  liquidityIndex: 1.2e27
  Bob's balance before: 1.2e18
  Bob's balance from `getSupplyBalance`: 1e18
  Bob's balance after: 1e18
  Bob's shares: 1.66666666666666667e17
```
When `liquidityIndex = 1.2e27`, Bob supplies `1.2e18 tokenA`, but `getSupplyBalance` return Bob's balance is only `1e18`.

### Mitigation

Fix the calculation in `PositionBalanceConfiguration#getSupplyBalance`

```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    return self.supplyShares.rayMul(index);
  }
```