Low Lemon Dove

High

# in PositionBalanceConfiguration. getSupplyBalance and getDebtBalance return a wrong amount

### Summary

the function **getDebtBalance** and   **getSupplyBalance** both function returns a wrong calculation   
```solidity

function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
```
as we can see the increase variable is converted from the share into the asset amount but  when returning self.supplyShares  is not converted  from the share into the desired amount the same apply   for the **getDebtBalance** function 

### Root Cause

PositionBalanceConfiguration:128 when returning the self.supplyShares should also be converted  
PositionBalanceConfiguration:139 when returning the self.supplyShares should also be converted  

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

the  function executeWithdraw in SupplyLogic uses the function **getSupplyBalance** to get the balance which will lead to wrong value so that  executeWithdraw  will withdraw a wrong amount 

### PoC

[_No response_
](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128)
### Mitigation

convert both the returnd amount