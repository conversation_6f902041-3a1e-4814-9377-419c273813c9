Acrobatic Rainbow Grasshopper

High

# `getSupplyBalance()` and `getDebtBalance()` return wrong values

## Summary
`getSupplyBalance()` and `getDebtBalance()` return wrong values
## Vulnerability Detail
`getSupplyBalance()` and `getDebtBalance()` are important functions used throughout the protocol to determine the amount of assets a user has:
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```
It first calculates the increase based on the new and last index, that is essentially the interest the user has received and then we add it to `self.supplyShares`. The issue is that `self.supplyShares` is a shares amount while the `increase` variable is in assets (as we multiply by index). Furthermore, that value is used as an amount of assets everywhere in the protocol. For example, upon supplying, here is the value `self.debtShares` gets its value (`PositionBalanceConfiguration::depositCollateral()`:
```solidity
sharesMinted = amount.rayDiv(index);
...
self.supplyShares += sharesMinted;
...
```
As seen, it first calculates the shares to mint based on the assets amount and the index and then we increment `self.supplyShares`. Let's imagine that we have not accrued any interest and `getSupplyBalance()` is called immediately afterwards. Then, the `increase` variable would be 0 so we would just return `self.supplyShares`. Based on this line: `sharesMinted = amount.rayDiv(index);`, we know that the value of `sharesMinted` is different than the `amount` parameter (unless `index` is 1e27). Thus, upon calling `getSupplyBalance()`, we would return that different amount which is not equal to the assets provided. Obviously, the returned amount should be equal to the assets provided as that's what we supplied and we have not accrued any interest.
## Impact
`getSupplyBalance()` and `getDebtBalance()` return wrong values
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129
## Tool used

Manual Review

## Recommendation
Return the amount in assets, not in shares