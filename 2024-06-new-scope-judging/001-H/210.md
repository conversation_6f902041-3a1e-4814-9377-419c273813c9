Joyous Cedar Tortoise

High

# getSupplyBalance does not convert supply shares to assets

## Summary

`PositionBalanceConfiguration.getSupplyBalance()` is supposed to return the rebased assets worth of collateral held by a position.

This means that it should return `supplyShares * currentLiquidityIndex`

However, the calculation in `PositionBalanceConfiguration.getSupplyBalance()` is incorrect (See ‘Root Cause’)

This leads to a large number of issues throughout the protocol, one of them is shown in the PoC section.

## Vulnerability Detail

[The calculation](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129):

```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
		uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
		return self.supplyShares + increase;
}
```

The mistake is that the liquidity index has not been applied to `self.supplyShares` 

The correct calculation would be:

`return self.supplyShares.rayMul(index)`


## Impact

As stated in the summary, this incorrect calculation leads to a large number of issues, wherever the supply balance of a position is obtained. This causes issues in vaults and pools. 

One of the many impacts:

When a user calls `Pool.withdraw()`  with `amount=type(uint256).max`, not all of the supplied collateral will be withdrawn. However, `isUsingAsCollateral`  will be set to `false` for that specific collateral asset, even though the supply balance is still not zero. This can cause unfair liquidations of users.

## PoC

Add the following test to `test/forge/core/pool/PoolBorrowTests.t.sol` and run it. 

The PoC demonstrates how after withdrawing maximally, the supply balance of the position remains greater than zero, but `isUsingAsCollateral` is set to false. This lowers the health factor to be lower than what it should be.
   
<details>
<summary> Foundry test </summary>
 
    ```solidity
    function testJ_cantWithdrawFully() external {
        _mintAndApprove(alice, tokenA, 4000 ether, address(pool));
        _mintAndApprove(bob, tokenA, 4000 ether, address(pool));
        _mintAndApprove(bob, tokenB, 4000 ether, address(pool));
        DataTypes.ReserveData memory reserveData = pool.getReserveData(address(tokenA));
    
        uint256 assetPrice = pool.getAssetPrice(address(tokenA));
        // Set the reserve factor to 1000 bp (10%)
        poolFactory.setReserveFactor(0.1e4);
    
        // Alice supplies
        vm.prank(alice);
        pool.supplySimple(address(tokenA), alice, 1000 ether, 0);
    
        DataTypes.UserConfigurationMap memory aliceData = pool.getUserConfiguration(alice, 0);
        assertTrue(aliceData.isUsingAsCollateral(reserveData.id));
    
        // Bob borrows
        vm.startPrank(bob);
        pool.supplySimple(address(tokenA), bob, 2000 ether, 0);
        pool.borrowSimple(address(tokenA), bob, 500 ether, 0);
        vm.stopPrank();
    
        uint256 daysToWait = 19 days;
        vm.warp(block.timestamp + daysToWait);
    
        vm.prank(alice);
        pool.supplySimple(address(tokenA), alice, 10 ether, 0);
    
        vm.warp(block.timestamp + 20 days - daysToWait);
    
        // bob repays after 20 days
        vm.startPrank(bob);
        tokenA.approve(address(pool), UINT256_MAX);
        DataTypes.SharesType memory repaid = pool.repaySimple(address(tokenA), UINT256_MAX, 0);
        vm.stopPrank();
    
        console.log('bob repaid %e assets', repaid.assets);
    
        // Alice withdraws max
        vm.startPrank(alice);
        pool.withdrawSimple(address(tokenA), alice, UINT256_MAX, 0);
        vm.stopPrank();
    
        // Data collection
        DataTypes.UserConfigurationMap memory aliceDataAfter = pool.getUserConfiguration(alice, 0);
    
        assertFalse(aliceDataAfter.isUsingAsCollateral(reserveData.id));
    
        uint256 supplyBalanceAfter = pool.getBalance(address(tokenA), alice, 0);
        console.log('supplyBalanceAfter is %e', supplyBalanceAfter);
        assertGt(supplyBalanceAfter, 0);
    }
    ```


</details>
    

## Recommendation

```diff
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-    return self.supplyShares + increase;
+    return self.supplyShares.rayMul(index);
}
```