Puny Orchid Buffalo

High

# getSupplyBalance Function's Incorrect Formula May Lead to Loss of Users' Funds or Earnings

## Summary
The formula in the `getSupplyBalance` function within the `PositionBalanceConfiguration` library has a vulnerability, and referencing this function may cause users to incur a loss of funds. The `getSupplyBalance` function is as follows:

```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```
The error occurs in the line `return self.supplyShares + increase;`. The `self.supplyShares` should be multiplied by `self.lastSupplyLiquidtyIndex` before adding increase. Otherwise, the calculated balance will be less than the actual amount.

## Vulnerability Detail
Here’s a simple example to explain this. For the sake of clarity, assume there is a Reserve where the liquidityIndex is equal to 1.5.

1. <PERSON> deposits 30 tokens, and at this point, <PERSON>’s supplyShares equals 20 (30/1.5).
2. <PERSON> intends to withdraw all of her funds, so she immediately calls the withdrawSimple function of the pool, setting the amount to type(uint256).max. Please note that the executeWithdraw function in SupplyLogic contains the following code:
```solidity
    uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);

    // repay with max amount should clear off all debt
    if (params.amount == type(uint256).max) params.amount = balance;
```

According to the calculation of the getSupplyBalance function: `balance = self.supplyShares + increase = 20 + 0 = 20`. At this point, Alice can only withdraw 20 tokens.

In the above example, for simplicity, it’s assumed that lastUpdateTimestamp equals block.timestamp, so the increase value equals 0. In reality, even if the increase is not 0, this issue still exists.

Let’s continue with an actual POC code to demonstrate this. In the test/forge/core/pool/ folder, create a test file `AuditTests.t.sol` with the following test code:

```solidity
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {IPool, PoolEventsLib, PoolSetup} from './PoolSetup.sol';
import {Test, console} from '../../../../lib/forge-std/src/Test.sol';

contract AuditTests is PoolSetup {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');
  address clark = makeAddr('clark');
  uint256 index = 1;

  function setUp() public {
    _setUpPool();
    _mintAndApprove(alice, tokenA, 100 ether, address(pool));
    _mintAndApprove(bob, tokenA, 100 ether, address(pool));
    _mintAndApprove(clark, tokenA, 1000 ether, address(pool));
  }

  // Vulnerabilities
  function testVulerability() external {
    // ---------- Mock normal supply and borrow logic --------
    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 1 wei, index);
    _forward(10);
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenA), bob, 50 ether, index);
    _forward(10);
    pool.borrowSimple(address(tokenA), bob, 35 ether, index);
    vm.stopPrank();

    _forward(86400 * 30 * 12); //To increase the value of liquidityIndex bigger

    vm.startPrank(clark);
    console.log('clark tokenA balance before', tokenA.balanceOf(clark), "\n");
    pool.supplySimple(address(tokenA), clark, 1000 ether, index);
    _forward(86400); //1 day
    doWithdrawAll("1st doWithdrawAll");
    // doWithdrawAll("2nd doWithdrawAll");
    // doWithdrawAll("3rd doWithdrawAll");
    vm.stopPrank();
  }

  function doWithdrawAll(string memory title) private{
    console.log(title,"----------------------------------");
    pool.withdrawSimple(address(tokenA), clark, type(uint256).max, index);
    console.log('clark tokenA balance after', tokenA.balanceOf(clark));
    console.log('addition logs ----------------------------------');
    console.log('pool.getBalance', pool.getBalance(address(tokenA), clark, index));
    console.log('clark supplyShares', pool.getBalanceRaw(address(tokenA), clark, index).supplyShares);
    console.log("\n");
  }
}
```
Run the test:
```shell
forge test --match-test testVulerability -vvv
```
Test result:
```shell
Ran 1 test for test/forge/core/pool/AuditTests.t.sol:AuditTests
[PASS] testVulerability() (gas: 604627)
Logs:
  clark tokenA balance before 1000000000000000000000

  1st doWithdrawAll ----------------------------------
  clark tokenA balance after 878571261271056173337
  addition logs ----------------------------------
  pool.getBalance 106684231498300176235
  clark supplyShares 106684231498300176235



Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 3.22ms (764.17µs CPU time)
```

From the results, we can see that after Clark withdraws all, only `878571261271056173337` is withdrawn to his account. Note that although Clark can continue to withdraw (because his `supplyShares` haven’t been fully burnt), the total withdrawal amount can only approach the principal, from any perspective this is causing a loss for Clark.

## Impact
Users may suffer a loss in funds or earnings.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

## Tool used
Manual Review
Foundry

## Recommendation
We recommend the following improvement:
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
     uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-    return self.supplyShares + increase;
+    return self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex) + increase;
  }
```
