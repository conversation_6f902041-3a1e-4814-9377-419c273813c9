Decent Walnut Squirrel

High

# Wrong calculation of supply balance and debt balance when withdraw and repay.

## Summary
The protocol calculates supply balance from supply share wrongly when withdraw.   
As same way, it calculates wrongly debt balance from debt share wrongly when repay.

## Vulnerability Detail
`SupplyLogic.sol#executeWithdraw()` function which is called when withdraw is as follows.
```solidity
  function executeWithdraw(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteWithdrawParams memory params
  ) external returns (DataTypes.SharesType memory burnt) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);
    reserve.updateState(params.reserveFactor, cache);

118 uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);

    // repay with max amount should clear off all debt
    if (params.amount == type(uint256).max) params.amount = balance;

    ValidationLogic.validateWithdraw(params.amount, balance);

    ...
  }
```
Here, `balances[params.asset][params.position]` is the amount of supply share.
`PositionBalanceConfiguration.sol#getSupplyBalance()` function called on L118 is as follows.
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
127    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
128    return self.supplyShares + increase;
  }
```
As we can see above, it adds share and amount.    
As same way, it calculates debt amount wrongly from debt share in `PositionBalanceConfiguration.sol#getDebtBalance()`.
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```

## Impact
A user cannot withdraw full balance. And it calculates debt balance of user wrongly, so it can cause unexpected error.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L128
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L139

## Tool used

Manual Review

## Recommendation
`PositionBalanceConfiguration.sol#getSupplyBalance(), getDebtBalance()` functions have to be modified as follows.
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index);
  }
  
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-   uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-   return self.debtShares + increase;
+   return self.debtShares.rayMul(index);
  }
```