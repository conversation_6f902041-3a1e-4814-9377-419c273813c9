Calm Graphite Ram

High

# PositionBalanceConfiguration::getSupplyBalance() incorrectly calculates shares value

## Summary

`PositionBalanceConfiguration::getSupplyBalance()` calculates the supply balance by adding two different units together, leading to an incorrect result.

## Vulnerability Detail
[PositionBalanceConfiguration::getSupplyBalance()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129)
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) { 
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase; 
  }
```
When calculating the supply balance the current calculation calculates the increase of the underlying tokens for the owned shares. This means that `increase` is in terms of `underlying` tokens, as the `supplyShares` are multiplied by the index, turning the shares back into `underlying`.

The issue arises with the fact that `self.supplyShares + increase` is adding two different units together, `shares` and `underlying` leading to an incorrect result.

## POC

Lets compare two implementation, the current one and just multiplying the shares by the current index to get the underlying amount:

### Current Implementation

1. Supply 100 underlying at index 1.1:
100 / 1.1 = 90.09 shares
2. Time passes and index increases to 1.3
3. getSupplyBalance():
increase = (90.09 * 1.3) - (90.09 * 1.1)
               = 118.18 - 100
               = 18.18
return = 90.09 + 18.18
           = 108.27 
Meaning that using this method the user's shares are worth 108.27 underlying tokens, which is incorrect as will be demonstrated in the usual method of utilising the index

### Standard implementation
1. Supply 100 underlying at index 1.1:
100 / 1.1 = 90.09 shares
2. Time passes and index increases to 1.3
3. getSupplyBalance():
return = shares * currentIndex
           = 90.09 * 1.3
           = 117.11

## Impact

The current logic within `PositionBalanceConfiguration::getSupplyBalance()` will lead to the interest on lending positions to be incorrectly calculated, causing them to lose out on rewards when they withdraw their underlying tokens by redeeming their shares.

`getSupplyBalance()` is utilised within
`CuratedVaultGetters::_convertToShares()` and `CuratedVaultGetters::_convertToAssets()` when calcualting the vault's total assets meaning it will lead to incorrect `shares <-> underlying` conversions, leading to under/over paying during deposits and withdrawals. This can be classified as a loss of funds impact.

## Code Snippet

[PositionBalanceConfiguration::getSupplyBalance()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129)

## Tool used

Manual Review

## Recommendation

Replace the logic in the function this the following:

```diff
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) { 
-    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-    return self.supplyShares + increase; 
+    return self.supplyShares.rayMul(index); 
  }
```

As the shares are minted with the current `index`, by simply multiplying the shares with the current index you will calculate the `shares` value in the `underlying` tokens.