Clever Ebony Halibut

High

# Incorrect Balance Calculation in getSupplyBalance() Function Leads to User Fund Loss

## Summary

- `getSupplyBalance()` function calculate the balance of the user incorrectly. it returns underestimated balance of the user. effectly causing loss of funds for them .


## Vulnerability Detail

- The vulnerability in the Pool contract stems from an incorrect calculation in the `getSupplyBalance()` function, leading to an underestimation of user balances and potential loss of assets.

- When a user deposits tokens into the protocol, they receive shares in return. These shares are calculated by dividing the deposited amount by the current liquidity index. For example, if a user deposits `100` tokens when the liquidity index is `1.2`, they would receive `83.33` shares (100 / 1.2).

```js
  function executeSupply(/*params*/ ) external returns (DataTypes.SharesType memory minted) {
    // some code ..
    IERC20(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);
    bool isFirst;
 >>   (isFirst, minted.shares) = balance.depositCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);
    // more code ...
  }
    function depositCollateral(/*prams*/) internal returns (bool isFirst, uint256 sharesMinted) {
 >>    sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
  }
```

- The issue arises during the withdrawal process for example. The `getSupplyBalance()` function is responsible for calculating the current value of a user's shares. However, its formula is flawed.The current formula attempts to calculate the increase in balance by subtracting the initial value of shares from their current value than it add it the shares amount not balance . This approach fails to account for the full appreciation of the shares over time.

```js
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```

Mathematically, the current formula can be expressed as:
$balance = shares + (shares * currentIndex) - (shares * initialIndex)$

- continue with previous example, if a user tries to withdraw immediately after depositing (assuming the index hasn't changed):
  $balance = 83.33+ (83.33 * 1.2 - 83.33 * 1.2)  = 83.33$

This result incorrectly suggests that the user's balance is only 83.33 tokens, despite having deposited 100 tokens.

- in this case the user can only withdraw `83.33` tokens instead of their original `100` and if they attempting attempting to withdraw the original deposit amount(100) the transaction will revert because of `validateWithdraw` function check.

```js
  function executeWithdraw(/*params*/) external returns (DataTypes.SharesType memory burnt) {
    // some code ..

    if (params.amount == type(uint256).max) params.amount = balance;
 >>   ValidationLogic.validateWithdraw(params.amount, balance);
   // more code ...
  }

    function validateWithdraw(uint256 amount, uint256 userBalance) internal pure {
    require(amount != 0, PoolErrorsLib.INVALID_AMOUNT);
    require(amount <= userBalance, PoolErrorsLib.NOT_ENOUGH_AVAILABLE_USER_BALANCE);
  }
```

## Impact

- Permanent loss of funds for users
- Broken protocol accounting in most functions

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

## Tool used

Manual Review

## Recommendation

- The correct calculation should simply multiply the number of shares by the current index:
  $shares * currentIndex$

In our example, this would correctly yield:
$balance =83.33 * 1.2 = 100 tokens$

```diff
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index);
  }
```