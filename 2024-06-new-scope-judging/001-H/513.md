Shiny Seaweed Mongoose

High

# Due to an error in the logic when trying to withdraw, a lender who deposits after interest rate has already gone up won't be able to withdraw their whole balance.

### Summary

Lenders who deposit into the pool at a later time will receive fewer shares but at a higher liquidity index, the problem here is that during withdrawal the liquidity index is not taken into account so the lender cannot withdraw their whole balance.

### Root Cause

The root cause of this bug lies inside the withdraw function, it uses the share as the assets to withdraw but this is not true, because the shares * supplyLiquidityIndex is the actual value supplied by the lender.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L146

```js
  function executeWithdraw(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteWithdrawParams memory params
  ) external returns (DataTypes.SharesType memory burnt) {

    ...
 @->   IERC20(params.asset).safeTransfer(params.destination, params.amount);

   ...
  }

```

From the above function, we can see that the `params.amount` is what is sent to the lender but for lenders who deposit at a later time the real amount they have should have been  `params.amount * supplyLiqiudityIndex`

### Internal pre-conditions

Lending, borrowing, and repaying of loans have occurred in the pool.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Subsequent lenders will not be able to get their full deposit back.

### PoC

_No response_

### Mitigation

Add this to the executeWithdraw function

```diff
+    params.amount = params.amount.rayMul(balances[params.asset][params.position].lastSupplyLiquidtyIndex);

    // Burn debt. Which is burn supply, update total supply and send tokens to the user
    burnt.shares = balances[params.asset][params.position].withdrawCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);
```