Smooth Carbon Narwhal

Medium

# Users can receive additional shares in the curated vault

## Summary
The `curated vault` follows the `ERC4626` standard, where users receive `shares` when they `deposit` `assets` into `pools` through the `vault`. 
Over time, the `assets` per `share` increase due to the accumulation of `interest` in the `pools`.
When users `deposit`, the number of `shares` they receive is based on the `total assets` in the `vault`, making accurate asset calculations critical.
Currently, however, the `total asset amount` is being underestimated because the old `liquidity index` is used in the calculation. 
This results in an incorrect total, allowing new `depositors` to receive more `shares` than they should.
## Vulnerability Detail
When users `deposit` `assets` into the `curated vault`, the `total assets` are calculated on `line 323` and the corresponding `shares` are calculated based on this `total` on `line 328`.
```solidity
function deposit(uint256 assets, address receiver) public override returns (uint256 shares) {
323:  uint256 newTotalAssets = _accrueFee();

  lastTotalAssets = newTotalAssets;
328:  shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Down);
  _deposit(_msgSender(), receiver, assets, shares);
}
```
This calculation happens in the `_accrueFee` function.
```solidity
function _accrueFee() internal returns (uint256 newTotalAssets) {
  uint256 feeShares;
  (feeShares, newTotalAssets) = _accruedFeeShares();
  if (feeShares != 0) _mint(feeRecipient, feeShares);
  emit CuratedEventsLib.AccrueInterest(newTotalAssets, feeShares);
}
```
This function calls `_accruedFeeShares`.
```solidity
function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
  newTotalAssets = totalAssets();
}
```
Ultimately the `totalAssets` function is called. 
In `totalAssets`, the `vault` iterates through the `withdrawal pools`, summing their `balances`.
```solidity
function totalAssets() public view override returns (uint256 assets) {
  for (uint256 i; i < withdrawQueue.length; ++i) {
    assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
  }
}
```
Unfortunately, the `getBalanceByPosition` function uses the outdated `liquidity index`, leading to an undervaluation of the `total assets`.
```solidity
function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
  return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
}
```
## Impact
As a result, new `depositors` are awarded more `shares` than they deserve, while existing `depositors` suffer losses as their `share` of the `assets` is diluted.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L323
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L178
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L186
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L370
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L48
## Tool used

Manual Review

## Recommendation
```solidity
function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
-  return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
+  return _balances[asset][positionId].getSupplyBalance(_reserves[asset].getNormalizedIncome());
}
```