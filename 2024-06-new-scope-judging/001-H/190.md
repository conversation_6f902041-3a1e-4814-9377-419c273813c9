Sneaky Hazelnut Lizard

Medium

# User will never be able to clear their debt fully because Get Debt balance should return the Total debt asset but instead it returns debtshares + interest asset value.

### Summary

The function `getDebtBalance` in the ZeroLend contract is incorrectly calculating the **rebased value of debt** by directly adding **debt shares** to the interest gained. This leads to incorrect debt calculations, especially when users attempt to repay their entire debt by using the maximum debt value (i.e., `type(uint256).max`) causing the function repay to always underpay. 


### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L131-L141


Correct implementation in Reserve library

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveSuppliesConfiguration.sol#L37-L46

Causes an issue with repay 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126-L131

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L151

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L98-L118

The wrong assertion of the bad will lead to a user being unable to repay is max debt because the value gotten for the debt value is actually Debt shares + interest


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path


The primary issue lies in the following line of the code:

```solidity
return self.debtShares + increase;
```

By returning the sum of `debtShares` and the calculated `increase` directly, the function mistakenly combines shares and asset values, which leads to miscalculations. This issue becomes particularly problematic when the user tries to repay the maximum debt, causing  **underpayment**.

### Problematic Code
```solidity
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase; // Incorrect: Adds debt shares to the calculated increase
}
```

Here, `debtShares` represents the shares, not the actual value of the debt. Adding shares to the `increase` leads to mixing abstract share units with actual asset values.
users cannot input the correct figure themselves because 
```solidity
// If paybackAmount is more than what the user wants to payback, the set it to the
    // user input (ie params.amount)
    if (params.amount < payback.assets) payback.assets = params.amount;
```

### Correct Implementation

In contrast, the **correct approach** (as seen in implementations like Reserve library) is to simply multiply the `debtShares` by the current index to get the **rebased value** of the debt:

```solidity
function getDebtBalance(DataTypes.ReserveSupplies storage self, uint256 index) internal view returns (uint256 debt) {
    debt = self.debtShares.rayMul(index);  // Properly converting shares to the actual debt amount
}
```

### Example Flow with ZeroLend's Incorrect Implementation:
1. A user accumulates debt represented by shares (`debtShares`).
2. As time passes, interest increases based on the liquidity index.
3. The contract attempts to calculate the rebased debt, but instead of simply converting the shares to debt using the debt liquidity index, the shares and the interest increase are added together.
4. If the user tries to repay the debt using `type(uint256).max`, the result includes both **shares and the interest value**, leading to an incorrect amount being sent for repayment, which makes the debt unrepayable as long as index > ray. with increases with block.timestamp. 


### Impact

The flawed `getDebtBalance` function impacts several parts of the contract, most notably when a user tries to repay their debt with the maximum amount:

```solidity
if (params.amount == type(uint256).max) {
    params.amount = payback.assets;  // Incorrectly includes shares + interest, leading to overpayment
}
```

Here, the `payback.assets` includes both the shares and the added value, which then gets converted again into shares in the `repayDebt` function, leading to a reversal when we subtract the shares available.

### PoC

_No response_

### Mitigation


The solution is to modify the `getDebtBalance` function so that it properly converts the shares into asset values and does not add the shares directly to the interest increase. Here’s the corrected implementation:

#### Corrected `getDebtBalance`
```solidity
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares.rayMul(self.lastDebtLiquidtyIndex) + increase;  // Correctly rebases the shares
}
```