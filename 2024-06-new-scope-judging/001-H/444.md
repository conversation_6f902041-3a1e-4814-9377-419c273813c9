Shiny Daisy Osprey

High

# getSupplyBalance and getDebtBalance calculates wrong balances affecting a bunch of functions

### Summary

`getSupplyBalance` and `getDebtBalance` wrongly calculates user's balances, this would affect several internal functionalities (like `withdraw` and `repay`) that rely on these functions to calculate balances as well as external contracts that query these balances from the pool.


### Root Cause


The `getSupplyBalance` and `getDebtBalance` functions calculate the user's balance as

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }


  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }

```

However during supply and borrow the shares are calculated as 

```solidity
  function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
@>  sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;
  }


  function borrowDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
@>  sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.debtShares == 0;
    self.lastDebtLiquidtyIndex = index;
    self.debtShares += sharesMinted;
    totalSupply.debtShares += sharesMinted;
  }
```

This means that if the liquidity index at the time of supply or borrow is > 1, and as a result the `sharesMinted` < `amount`, then the amount returned by `getSupplyBalance` or `getDebtBalance` will always be less than the users actual balance.

For example,
- Alice deposits 120 assets at a liquidity index of 1.5, `sharesMinted` will be 80
- The liquidity index increases to 1.7, and Alice now attempts to withdraw her initial deposit + accrued profit
following `getSupplyBalance` calculations , Alice gets (80\*1.7 - 80\*1.5 = 16)  80 + 16 = 96 assets back , instead of 136 (120 deposit + 16 profit)


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

1. incorrect supply and debt balances
2. Grieving as full withdrawal or debt repayment is no longer possible in a single transaction (i.e it would require multiple transactions to completely withdraw all collateral or repay all debt)

### PoC

_No response_

### Mitigation

```diff
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares.rayMul(index);
  }


  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
-   uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares.rayMul(index);
  }

```