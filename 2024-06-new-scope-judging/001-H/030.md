Loud Rosewood Oyster

Medium

# Error in `PositionBalanceConfiguration::getDebtBalance`

## Summary
[PositionBalanceConfiguration::getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140) functionality always returns the wrong debt assets of a position, due to always returning the sum of the position debt in shares and its borrow interest in assets. 
## Vulnerability Detail
The Library [PositionBalanceConfiguration::getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140) functionality is queried a lot of times throughout the pool logic, the nft position manager contracts and the curated vaults to return the current debt balance of a position in assets, but currently only the borrowed interest is converted to assets value, while the stored debt, which are in shares are left as is:
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```
This thus always returns the sum of debt shares and an asset value borrow interest.

Tagging below some contracts logic where it's queried, expecting an asset return debt value:

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L151

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L78

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L121

## Impact
This will result in a lot of computational error, as the returned debt balance of every queried position will always be wrong.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140
## Tool used

Manual Review

## Recommendation
first convert the user debt shares to asset value before summing with the computed borrow interest value:
```solidity
return self.debtShares.rayMul(index) + increase;
```