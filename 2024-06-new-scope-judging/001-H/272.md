Curly Pineapple Armadillo

High

# `PositionBalanceConfiguration`'s `getSupplyBalance` and `getDebtBalance` are implemented incorrectly causing numerous criticial issues

### Summary

`PositionBalanceConfiguration`'s `getSupplyBalance` and `getDebtBalance` are implemented incorrectly and they return smaller amounts than what they should. As they are used in numerous parts of the protocol, many functions will not produce the desired results.


### Root Cause

- In [`getSupplyBalance`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L127-L128) and [`getDebtBalance` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L138-L139) the supply/debt balance is calculated incorrectly, causing the results to be less than what they should be.
- Here is how the supply balance is currently derived: 

`supplyShares + supplyShares.rayMul(index) - supplyShares.rayMul(lastSupplyLiquidtyIndex) `

Let's say that a user supplied 150 assets at an index equal to 1.5. Thus, they would be minted 150 / 1.5 = 100 supply shares. After some time interest accrues and the index goes up to 3. Now if `getSupplyBalance` is called it will return:

`100 + 100 * 3 - 100 * 1.5 = 250`

This is wrong as after the index has doubled in size since the initial deposit the user's initial shares should be worth 300 assets: `100 * 3`.

Essentially, since the user's deposited assets are scaled down by the index at the time of the deposit in order to get the shares: `assets / currentIndex`, there is no need for them to be further scaled down in `getSupplyBalance` and `getDebtBalance`. The only thing that needs to be done is to multiply the shares by the current index, which is how shares are converted to assets in all other parts of the code, except these two functions.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

Here is one possible scenario, but it is important to note that there are more issues, rooted in the wrong calculation in `getSupplyBalance` and `getDebtBalance`:
1. User deposits 100 ETH at an index of 2 RAY, they are minted 50e18 shares
2. The index goes up to 3 RAY and the 50e18 shares should be worth 150 ETH
3. The user withdraws all of their shares and `executeWithdraw` is called, which calculates that the asset balance of the user is only: `50e18 + 50e18 * 3 - 50e18 * 2 = 100 ETH`, instead of 150 ETH

The same issue occurs when debt is repaid, but in that case users will be unable to repay all of their debt, increasing the chances of them getting liquidated.


Here is another major issue, this time in `CuratedVault`:
1. The `totalAssets` function is used to calculate how much a user's vault shares are worth.
2. `totalAssets` calls `getBalanceByPosition` for every pool in the withdrawal queue
3. The issue is that `getBalanceByPosition` uses the incorrect `getSupplyBalance`, thus `totalAssets` will always return smaller results than intended
4. As a result, all functions in `CuratedVault` that call `_accrueFee` will operate incorrectly and not return the desired results by the protocol


`getSupplyBalance` is also used in `NFTPositionManagerSetters` and its result is passed inside of `_handleSupplies`  causing rewards to be less than what they should really be.


### Impact

Numerous key parts of the protocol do not function as they should, causing a variety of issues, including a loss of funds for users.

### PoC

_No response_

### Mitigation

[`getSupplyBalance`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L127-L128) and [`getDebtBalance` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L138-L139) should simply return the users' shares multiplied by the current index.