Able Concrete Jellyfish

Medium

# Inconsistent Data Handling in getSupplyBalance Leading to Incorrect Balance Calculation

## Summary
The `getSupplyBalance` function in the `PositionBalanceConfiguration` library may return incorrect supply balances due to inadequate handling of zero values and potential data inconsistencies. This can lead to misreporting of user balances, affecting the integrity of the financial operations within the smart contract.

## Vulnerability Detail
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
@=>    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
@=>    return self.supplyShares + increase;
  }
```
Issues arise when:
- `self.supplyShares` or `index` is zero, leading to a zero result from `rayMul`, which may not reflect the actual balance.
- `self.lastSupplyLiquidtyIndex` is not updated correctly, causing an incorrect calculation of the `increase`.

Scenario:
1. Assume `self.supplyShares` is 1000 and `index` is 0.
2. The function will return 0 as the balance, which is incorrect.
3. If `self.lastSupplyLiquidtyIndex` is outdated, the calculated `increase` will not reflect the actual accrued interest.

## Impact
- Users will see incorrect balances, which will affect their financial decisions.
- The financial integrity of the contract is compromised, potentially leading to disputes.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

## Tool used

Manual Review

## Recommendation
Validate inputs to ensure they are non-zero.
```diff
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
+   require(index != 0, "Liquidity index cannot be zero");
+   require(self.supplyShares != 0, "Supply shares cannot be zero");

+   uint256 currentSupply = self.supplyShares.rayMul(index);
+   uint256 previousSupply = self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);

    uint256 increase = currentSupply > previousSupply ? currentSupply - previousSupply : 0;
    return self.supplyShares + increase;
}
```