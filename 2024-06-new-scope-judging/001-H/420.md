Joyous Rainbow Shark

High

# `PositionBalanceConfiguration::getSupplyBalance()` adds interest in assets to a shares balance resulting in user unable to withdraw their entire balance

### Summary

Due to not converting the 'base' amount to a value denominated in assets `PositionBalanceConfiguration::getSupplyBalance()` returns a lower-than expected asset balance. This will block most users from withdrwaing all assets (when they minted shares with `liquidityIndex` > 1e27). Over time the impact increases because the delta between the expected value balance and the actual return value will increase due to the `liquidityIndex` increasing.

### Root Cause

[`PositionBalanceConfiguration::getSupplyBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129) adds an `increase` denominated in 'asset amount' to `self.supplyShares` denominated in shares:

```javascript
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
>@  return self.supplyShares + increase;
  }
```
This results in the function returning lower than expected supply balances.

This function is utilized in the pool withdraw flow in [`ValidationLogic::validateWithdraw()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L123) which checks the amount a user is attempting to withdraw does not exceed the return value for [`getSupplyBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118). Therefore users will be DOSed from withdrawing their entire balance if the `liquidityIndex` when they deposited was > 1e27.
For example, if the liquidityIndex is 1.2e27 and a user deposits 12e18 weth, they will only be able to withdraw ~10e18 weth.


The function is also called in [`CuratedVaultSetters::_accrueFee()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L178) via [`CuratedVaultGetters::_accruedFeeShares()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L186) and ['CuratedVault::totalAssets()'](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L370) and ['Pool::getBalanceByPosition()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L48). So the assets which a vault owns in underlying pools will be also be under-represented in the curated vault accounting.

### Internal pre-conditions

1. Interest needs to be earned on supplied assets, with liquidityIndex updated

### External pre-conditions

_No response_

### Attack Path

1. A pool's liquidityIndex is > 1e27
2. Any user supplies assets to a pool 
4. User is DOSed from accessing all of their deposited funds (including or excluding interest earned), even if the pool has liquidity to service the withdrawal.


### Impact

1. Users unable to withdraw all of their deposited funds (shown in the POC).
2. `CuratedVault::totalAssets()` does not return the index-adjusted asset value, leading to a range of issues in the curated vault due to under-representing the assets controlled by a curated vault. For example:
  - Total assets are [calculated incorrectly in `accureFeeShares`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L186)
  - Shares to mint upon a deposit are [calculated incorrectly](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L323-L329). Note that as the underlying pool's `liquidityIndex` increases (as it will over time), the assets will be more under-represented favouring early vault depositors at the expense of future vault depositors.
3. `NFTPositionManagerSetters::_supply()` calculates a position's asset amount incorrectly for the puposes of reward calculations
4. `NFTPositionManagerSetters::_withdraw()` calculates assets amount incorrectly for the puposes of reward calculations


### PoC

The following coded POC shows a user losing some of their deposit due to the bug. 

To run the test create a new file in `/test/forge/core/pool` and paste the below contents

```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {console2} from 'forge-std/src/Test.sol';
import {PoolWithdrawTests} from './PoolWithdrawTests.t.sol';
import {WadRayMath} from '../../../../contracts/core/pool/utils/WadRayMath.sol';
import {PoolErrorsLib} from '../../../../contracts/interfaces/errors/PoolErrorsLib.sol';

contract PoolUserUnableToWithdrawBalance is PoolWithdrawTests {
    using WadRayMath for uint256;

  function test_POC_UserUnableToWithdrawEntireBalance() external {
    uint256 supplyAmount = 50 ether;
    uint256 mintAmount = 100 ether;
    uint256 borrowAmount = 30 ether;
    uint256 index = 1;
    address alice = makeAddr('alice');
    address bob = makeAddr('bob');

    // Alice deposits and borrows from the pool to generate some interest
    _mintAndApprove(alice, tokenA, mintAmount, address(pool));
    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmount, index);
    pool.borrowSimple(address(tokenA), alice, borrowAmount, index);
    vm.stopPrank();

    // Some time passes, interest is accrued
    for(uint256 i = 0; i < 365; i++){
      vm.warp(block.timestamp + 1 days);
      pool.forceUpdateReserves();
      oracleA.updateRoundTimestamp();
    }
    assert(pool.getReserveData(address(tokenA)).liquidityIndex > 1e27); // Ensure interest has been accrued

    // New user deposits 1.08 e18 tokens
    _mintAndApprove(bob, tokenA, mintAmount, address(pool));
    vm.startPrank(bob);
    uint256 bobSupplyAmount = 1.08e18;
    pool.supplySimple(address(tokenA), bob, bobSupplyAmount, index);

    // Bob immediately attempts to withdraw their supplied balance and reverts
    vm.expectRevert("NOT_ENOUGH_AVAILABLE_USER_BALANCE");
    pool.withdrawSimple(address(tokenA), bob, bobSupplyAmount, index);
  }
}
```

### Mitigation

```diff
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index);
  }
```