Loud Rosewood Oyster

High

# Asset Withdrawal Exceeds Entitlement Due to Improper Index Update

## Summary
Users can withdraw more assets than they are worth by making additional deposits after an increment in the liquidity index.
## Vulnerability Detail
Currently, users can withdraw more assets than they are entitled to, by depositing additional assets after an increase in the liquidity index

+ To Illustrate:

CURRENT SYSTEM::

assuming user A deposits 100 USDC at index 1
lastSupplyLiquidtyIndex = 1
supply shares = 100
If the user makes another 110 USDC deposit at index 1.1
supply shares = 100 +110/1.1 -> 100 + 100 ==> 200
[_code snippet_](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L38-L50)

For simplicity, assuming the user attempts to completely withdraw all of his tokens at the liquidity index 1.1. the amount to be withdrawn will be set to the return value from  [getSupplyBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129), for user A, getSupplyBalance will return:

increase = 200 * 1.1 - 200 * 1.1 = 0
supply shares + increase = 200 + 0 ==> 200

during withdrawal, the shares to be burnt are calculated as: 
200 /1.1 = 181
supply shares = 200- 181==> 19
[_code snippet_](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L85-L96)

19 shares are left after the max withdrawal, thus allowing the user to withdraw above 217 USDC.
If we take his initial deposit of 100 USDC into account. user A deposited 100 USDC at index 1, then 110 USDC at index 1.1, followed up with some withdrawals. He is only supposed to accrue interest for his initial 100 USDC deposit, which should be:

100 * 1.1 - 100 * 1 = 110 - 100 ==> 10 USDC 

but the system currently allows user A to withdraw more than his supposed balance plus interest.

## Impact
users can withdraw more reserve assets than they should by depositing additional reserve assets after an increment in the liquidity index.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L106-L156

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L67-L75

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L58-L94
## Tool used

Manual Review

## Recommendation
calculate and store users' accrued interests before updating their position `lastSupplyLiquidtyIndex` to the current index during deposits.

During withdrawal, create a functionality that updates and stores a position interests, this functionality should then return the user-updated supply shares in assets.