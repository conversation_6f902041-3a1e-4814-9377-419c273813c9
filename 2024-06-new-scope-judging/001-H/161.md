Careful Fleece Pike

High

# Wrong calculation in `PositionBalanceConfiguration#getDebtBalance` when `borrowIndex > ray` will cause broken accounting

### Summary

Wrong calculation in `PositionBalanceConfiguration#getDebtBalance` when `borrowIndex > ray` will cause broken accounting.

### Root Cause

Wrong calculation in `PositionBalanceConfiguration#getDebtBalance`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140

```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```

`increase` is the increase in debt **asset** of a position comparing to the `lastDebtLiquidtyIndex`, but `self.debtShares` is the amount of debt **shares** of the position. The sum `self.debtShares + increase` is not equal to amount of debt assets.

### Internal pre-conditions

`borrowIndex` of a reserve is greater than `ray`.

### External pre-conditions

_No response_

### Attack Path

Accounting will be broken where the function `getDebtBalance` is used. Here we will demonstrate a vulnerability path for the `NFTPositionManager#repay` function,  because `getDebtBalance` is used at

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L121

1. When `borrowIndex` of `tokenA` is equal to `ray`, Alice borrows `1e18 tokenA`, and then she can repay successfully.
2. When `borrowIndex` of `tokenA` is greater than to `ray`. Alice borrows `1e18 tokenA`, she can not repay back because `previousDebtBalance`, `currentDebtBalance` are not reflect Alice's debt correctly, which leads to the check `previousDebtBalance - currentDebtBalance == repaid.assets` fail.

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L125

```solidity
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId);

    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```

### Impact

Accounting will be broken where the function `getDebtBalance` is used. In case of the vulnerability path above, the function `NFTPositionManager#repay` is unusable.

### PoC

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {NFTErrorsLib} from 'contracts/interfaces/errors/NFTErrorsLib.sol';

import {DeployNFTPositionManager} from 'test/forge/core/positions/DeployNFTPositionManager.t.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is DeployNFTPositionManager {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  uint256 mintAmount = 10 ether;
  uint256 borrowAmount = 1 ether;

  function setUp() public {
    _setUpPool();
    _setup();
  }

  function testRepayWhenBorrowIndexIsEqualToRay() public {
    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));
    _mintAndApprove(alice, tokenB, mintAmount, address(nftPositionManager));

    _mintAndApprove(bob, tokenA, borrowAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenA), bob, borrowAmount, 0);

    DataTypes.ExtraData memory dummyData = DataTypes.ExtraData(bytes(''), bytes(''));

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenB), address(0), mintAmount, 0, dummyData);

      vm.startPrank(alice);
      nftPositionManager.mint(address(pool));
      nftPositionManager.supply(params);
      vm.stopPrank();
    }

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 0, dummyData);

      vm.prank(alice);
      nftPositionManager.borrow(params);
    }

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), address(0), borrowAmount, 0, dummyData);

      vm.prank(alice);
      nftPositionManager.repay(params);
    }
  }
  function testRepayWhenBorrowIndexIsGreaterThanRay() public {
    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));
    _mintAndApprove(alice, tokenB, mintAmount, address(nftPositionManager));

    _mintAndApprove(bob, tokenA, borrowAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenA), bob, borrowAmount, 0);

    DataTypes.ExtraData memory dummyData = DataTypes.ExtraData(bytes(''), bytes(''));

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenB), address(0), mintAmount, 0, dummyData);

      vm.startPrank(alice);
      nftPositionManager.mint(address(pool));
      nftPositionManager.supply(params);
      vm.stopPrank();
    }

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 0, dummyData);

      vm.prank(alice);
      nftPositionManager.borrow(params);
    }

    skip(1 days);
    pool.forceUpdateReserves();
    console.log("borrowIndex: %e", pool.getReserveData(address(tokenA)).borrowIndex);

    {
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), address(0), borrowAmount, 0, dummyData);

      vm.expectRevert(NFTErrorsLib.BalanceMisMatch.selector);
      vm.prank(alice);
      nftPositionManager.repay(params);
    }
  }
}

```

### Mitigation

Fix the calculation in `PositionBalanceConfiguration#getDebtBalance`

```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    return self.debtShares.rayMul(index);
  }
```