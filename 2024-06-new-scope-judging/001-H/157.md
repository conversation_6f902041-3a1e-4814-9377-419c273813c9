Powerful Licorice Elephant

High

# Borrowers can repay the full debt without paying the interest.

## Summary
 when users borrow an asset 2nd time(or afterwards) after borrowing the asset 1st time, users interest is not increased to their debtshare from lastdebtindex to currentdebtindex and the user lastsdebtindex is updated to currentdebtindex. As a result, when the user borrows the asset after sometime, the user will pay interest from next borrowindex to currentborrowindex and previous interest will be not paid.

## root cause
when users borrow an asset 2nd time(or afterwards) after borrowing the asset 1st time, users interest is not increased to their borrowshare.


## internal pre condition
before borrowing users have to supply the asset 2nd time(afterwards) after borrowing the asset 1st time.

## Vulnerability Detail
1st time borrow:   current borrowindex = 1.2e18, borrow amount = 500e18. So after borrowing the user’s   self.debtShares = 416.6e18,  self.lastdebtLiquidtyIndex = 1.2e18.

2nd time borrow to this reserve:   this time current borrowindex = 1.25e18,borrow amounts = 100e18(share of 100e18 amount is 83.3e18), now 83.3e18 is added to self.debtShares, so updated   self.debtShares = 416.6+83.3 = 499.9e18 and  self.lastdebtLiquidtyIndex is updated from 1.2e18 to 1.25e18. But here problem is when the share of 100e18 amount i.e 83.3e18 is calculated, then previous interest i.e interest from index 1.2e18 to 1.2e18 is not added to the self.debtShares. As a result, users will not pay  the previous interest.

Repay full debt  immediately after 2nd time borrow(in same transaction):   during 2nd time borrow, borrowindex  = 1.25e18,self.lastdebtLiquidtyIndex = 1.25e18(as updated), now when one user repays full amounts,  executeRepay function calls getDebtBalance function to calculate the full debt amounts with interest , see getDebtBalance function, as now the user’s self.lastdebtLiquidtyIndex is equal to current borrowindex. So the user’s accrued interest is 0 and the user will pays debtshare.

## Impact
some users will not pay interest fully for borrowing from the pool.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137
## Tool used

Manual Review

## Recommendation
add the interest to debtshare,  when users borrow an asset 2nd time(or afterwards) after borrowing the asset 1st time.

