Joyous Cedar Tortoise

High

# getDebtBalance does not convert shares to asset

## Summary

`PositionBalanceConfiguration.getDebtBalance()` is supposed to return the rebased assets worth of debt owed by a position.

This means that it should return `debtShares * currentBorrowIndex`
However, the calculation in `PositionBalanceConfiguration.getDebtBalance()` is incorrect (See ‘Root Cause’)

This leads to a large number of issues throughout the protocol.

## Root Cause

[The calculation:
](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L141)
```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
  }
```

The mistake is that the liquidity index has not been applied to `self.debtShares`

The correct calculation would be:

`return self.debtShares.rayMul(index)`

## Internal pre-conditions

## External pre-conditions

## Attack Path

## Impact

As stated in the summary, this incorrect calculation leads to a large number of issues, wherever the debt balance of a position is obtained.

The most severe impact- repaying via the NFT Position manager will fail 100% of the time.

The debt balances will be calculated using `getDebtBalance()` which incorrectly returns debt shares mostly. However `repaid.assets` is assets, not shares.

Hence, `repaid.assets` will not be equal to the change in debt balance, so the [following check](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125) fails:

```solidity
    if (previousDebtBalance - currentDebtBalance != repaid.assets) {
      revert NFTErrorsLib.BalanceMisMatch();
    }
```

Another impact- [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126) the obtained value of `payback.assets` will be too low, so the debt cannot be completely repaid when `params.amount=type(uint256).max` 

## PoC

## Recommendation

The calculation should be identical to the calculation in [`ReserveSupplyConfiguration.getDebtBalance()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/configuration/ReserveSuppliesConfiguration.sol#L37-L45)

```diff
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 debt) {
-    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
-    return self.debtShares + increase;
+    return self.debtShares.rayMul(index);
}
```