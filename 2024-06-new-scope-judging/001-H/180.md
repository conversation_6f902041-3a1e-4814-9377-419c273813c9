Innocent Chartreuse Parrot

High

# Faulty debt balance calculation prevents users from repaying their debt

## Summary
Faulty debt balance calculation prevents users from repaying their debt

## Vulnerability Detail
Users' up to date debt balance is calculated as follows:

[PositionBalanceConfiguration::getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L139)

```solidity
  function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);
    return self.debtShares + increase;
```

As shown above, the debt balance (which should be denominated in assets) is valued as the `shares` plus the increase in `assets` (interest). This calculation fails to return the appropriate debt balance when the exchange ratio for `shares/assets` is no longer 1:1, i.e. the `borrowIndex` is above `1`. Here is a simple example:

```python
index = 2
borrowed = 10
shares = 10 / 2 = 5
lastDebtLiquidtyIndex = 2

getDebtBalance = shares + ((shares * 2) - (shares * 2)) = 5
actualDebt = borrowed = 10
```

Therefore, the calculation in `getDebtBalance` will underestimate the user's debt balance since it returns the `debtShares` and treats that value as equivalent to `assets`. 

This calculation becomes problematic during repayments via the `NFTPositionManager`:

[NFTPositionManagerSetters::_repay](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L119-L125)
```solidity
    uint256 previousDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId); // @audit: calls `getDebtBalance` function, returns shares
    DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
    uint256 currentDebtBalance = pool.getDebt(params.asset, address(this), params.tokenId); // @audit: calls `getDebtBalance` function, returns shares


    if (previousDebtBalance - currentDebtBalance != repaid.assets) { // @audit: comparing shares burnt with assets repaid
      revert NFTErrorsLib.BalanceMisMatch();
    }
```

The above call will revert with a `BalanceMisMatch` error due to the fact that the shares burnt during repayment are being incorrectly compared against the assets repaid. Continuing from our previous example:

```python
index = 2
shares = 5
borrowed = 10
repayAmount = 4

sharesBurnt = 4 / 2 = 2
assetsRepaid = repayAmount = 4
```

However, users who borrowed and are repaying via the `Pool` contract will still be able to repay their positions, but they will not be able to perform full repayments in a single transactions as expected, and instead will be forced to perform multiple "full" transactions until their debt is completely repaid. This is due to the fact that they can only at most repay `shares + increase` in one transaction.

Users can specify a repay amount of `type(uint256).max` in order to repay their full debt. However, the `getDebtBalance` will return an inaccurate debt amount and therefore users will repay less than their full debt amount. 

[BorrowLogic::executeRepay](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126-L151)
```solidity
    payback.assets = balances.getDebtBalance(cache.nextBorrowIndex);


    // Allows a user to max repay without leaving dust from interest.
    if (params.amount == type(uint256).max) {
      params.amount = payback.assets; // @audit: repayment capped at `shares + increase`
    }


    ValidationLogic.validateRepay(params.amount, payback.assets);


    // If paybackAmount is more than what the user wants to payback, the set it to the
    // user input (ie params.amount)
    if (params.amount < payback.assets) payback.assets = params.amount;


    reserve.updateInterestRates(
      totalSupplies,
      cache,
      params.asset,
      IPool(params.pool).getReserveFactor(),
      payback.assets,
      0,
      params.position,
      params.data.interestRateData
    );


    // update balances and total supplies
    payback.shares = balances.repayDebt(totalSupplies, payback.assets, cache.nextBorrowIndex); // @audit: `shares + increase` amount of debt repaid, not full debt balance
```

[PositionBalanceConfiguration::repayDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L117)
```solidity
  function repayDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastDebtLiquidtyIndex = index;
    self.debtShares -= sharesBurnt;
    supply.debtShares -= sharesBurnt;
```

## Proof of Concept

Place the following test in `NFTPositionManagerTest.t.sol` and run with `forge test --mc NFTPostionManagerTest --mt testCannotRepayDebt`:

*Note: The POC below showcases the greatest impact of this bug*

```solidity
    function testCannotRepayDebt() external {
        // beginning state: borrow index is > 1
        uint256 amount = 2000e18;
        uint256 borrowAmount = 800e18;
        _mintAndApprove(bob, tokenA, 2 * amount, address(pool));
        vm.startPrank(bob);

        pool.supplySimple(address(tokenA), bob, amount, 0);
        pool.borrowSimple(address(tokenA), bob, borrowAmount, 0);
        vm.stopPrank();

        vm.warp(block.timestamp + 10 days);
        assertGt(pool.getReserveNormalizedVariableDebt(address(tokenA)), 1e27);

        // update oracle answer to avoid stale price error for tests
        oracleA.updateAnswer(oracleA.latestAnswer());

        // alice supplies and borrows
        testShouldSupplyAlice();
        uint256 repayAmount = 10 ether;
        borrowAmount = 20 ether;
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory params =
          INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 1, data);

        vm.startPrank(alice);
        nftPositionManager.borrow(params);
        assertEq(tokenA.balanceOf(address(pool)), 30 ether + (amount - 800e18), 'Pool Revert');
        assertEq(tokenA.balanceOf(alice), 70 ether, 'Alice Revert');

        // alice can not repay debt
        params.amount = repayAmount;
        vm.expectRevert(bytes4(keccak256("BalanceMisMatch()")));
        nftPositionManager.repay(params);
        vm.stopPrank();
    }
```

Place the following test inside `PoolRepayTests.t.sol` and run with `forge test --mc PoolRepayTests --mt testFullRepaymentGriefed -vvv`:

*Note: The POC below showcases an additional, lesser impact for this bug*

```solidity
  function testFullRepaymentGriefed() external {
    // beginning state: borrow index is > 1
    _mintAndApprove(alice, tokenA, 2 * amount, address(pool));
    vm.startPrank(alice);

    pool.supplySimple(address(tokenA), alice, amount, 0);
    pool.borrowSimple(address(tokenA), alice, borrowAmount, 0);
    vm.warp(block.timestamp + 10 days);
    assertGt(pool.getReserveNormalizedVariableDebt(address(tokenA)), 1e27);

    // update oracle answer to avoid stale price error for tests
    oracleA.updateAnswer(oracleA.latestAnswer());

    // user borrows and calculated debt is immediately undervalued
    address user = address(0x69420);
    _mintAndApprove(user, tokenA, 2 * amount, address(pool));
    vm.startPrank(user);

    pool.supplySimple(address(tokenA), user, amount, 0);

    pool.borrowSimple(address(tokenA), user, borrowAmount, 0);

    uint256 debtShares = pool.getBalanceRaw(address(tokenA), user, 0).debtShares;
    uint256 borrowIndex = pool.getReserveNormalizedVariableDebt(address(tokenA));

    //emit log_named_uint("debt shares", debtShares);
    //emit log_named_uint("calculated debt", pool.getDebt(address(tokenA), user, 0));
    //emit log_named_uint("actual debt", debtShares.rayMul(borrowIndex));

    // user must submit multiple transactions in order to repay full debt
    uint256 numTransactions;
    for (uint256 i; i < 100; i++) {
        pool.repaySimple(address(tokenA), UINT256_MAX, 0);
        numTransactions += 1;

        debtShares = pool.getBalanceRaw(address(tokenA), user, 0).debtShares;
        borrowIndex = pool.getReserveNormalizedVariableDebt(address(tokenA));

        //emit log_named_uint("remaining debt shares", debtShares);
        //emit log_named_uint("remaining calculated debt", pool.getDebt(address(tokenA), user, 0));
        //emit log_named_uint("remaining actual debt", debtShares.rayMul(borrowIndex));

        if (debtShares == 0) break;
    }
    vm.stopPrank();
    emit log_named_uint("Number of txs required to fully pay off debt", numTransactions);
  }
```

## Impact
Greatest impact: Once the `borrowIndex` is above `1`, users who borrow via the `NFTPositionManager` will not be able to repay any of their debt. This will lead to their positions accruing interest until they are eligible for liquidation, in which case the user will lose collateral.  This also increases the protocol's risk of insolvency. 

Lesser impact: Once the `borrowIndex` is above `1`, users who borrow directly from the `Pool` will have to perform multiple additional `full` repayments in order to repay their entire debt. 

The greatest impact can be considered high and the likelihood for said impact is also high, thus this report is labeled as high severity.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L139

## Tool used

Manual Review

## Recommendation
I would recommend calculating the user's up to date debt balance the same way it is calculated in [`ReserveSuppliesConfiguration.sol`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveSuppliesConfiguration.sol#L43-L44): 

```solidity
debt = self.debtShares.rayMul(index);
```