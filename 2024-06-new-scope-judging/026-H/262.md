Stale Tweed Seal

High

# Liquidation doesn't prioritize lowest LTV/Liquidation Threshold assets

### Summary

Liquidation doesn't prioritize lowest LTV/Liquidation Threshold assets and since the liquidator can freely choose any collateral asset supplied by the liquidatee to perform liquidation, it can be disadvantageous for the liquidatee depending on which collateral asset is selected.

### Root Cause

* Liquidator can freely choose any collateral asset supplied by the liquidatee to perform the [liquidation](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L129-L130).
* The LTV and Liquidation Threshold of a position is calculated as a [weighted average](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L135-L139) of the LTVs and Liquidation Thresholds of the different supplied assets.

### Internal pre-conditions

A pool has :
1. `tokenA` with an LTV of 7500 and a Liquidation Threshold of 8000 and a price of $1
2. `tokenB` with an LTV of 6000 and a Liquidation Threshold of 6500 and a price of $2
3. `tokenC` with a price of $100

### External pre-conditions

N/A

### Attack Path

1. Alice supplies 1500 of `tokenA` and 750 of `tokenB`, the average LTV is 6750 and the average Liquidation Threshold is 7250 
2. Alice borrows 20.25 of `tokenC`
3. Price of `tokenC` increases to $125 and Alice becomes liquidatable
4. Bob liquidates Alice :
  * If `tokenA` is used first : the average LTV becomes 6000 and the average Liquidation Threshold becomes 6500. The health factor of the position ends up being below 1, allowing Bob to liquidate Alice again.
  * If `tokenB` is used first : the average LTV becomes 7500 and the average Liquidation Threshold becomes 8000. The health factor of the position ends up being above 1.

### Impact

Liquidatee position ends up with a less than optimal LTV, Liquidation Threshold and Health Factor and may be liquidatable more times than needed in the worse case.

### PoC

```diff
diff --git a/zerolend-one/test/forge/core/pool/PoolSetup.sol b/zerolend-one/test/forge/core/pool/PoolSetup.sol
index 742a71b..c6b9894 100644
--- a/zerolend-one/test/forge/core/pool/PoolSetup.sol
+++ b/zerolend-one/test/forge/core/pool/PoolSetup.sol
@@ -55,13 +55,11 @@ abstract contract PoolSetup is CorePoolTests {
     sources[2] = address(oracleC);
     sources[3] = address(oracleD);

-    DataTypes.InitReserveConfig memory config = _basicConfig();
-
     DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](4);
-    configurationLocal[0] = config;
-    configurationLocal[1] = config;
-    configurationLocal[2] = config;
-    configurationLocal[3] = config;
+    configurationLocal[0] = _basicConfig(7500, 8000, 18);
+    configurationLocal[1] = _basicConfig(6000, 6500, 18);
+    configurationLocal[2] = _basicConfig(8000, 8500, 18);
+    configurationLocal[3] = _basicConfig(6500, 7000, 18);

     address[] memory admins = new address[](1);
     admins[0] = address(this);
@@ -80,12 +78,16 @@ abstract contract PoolSetup is CorePoolTests {
     });
   }

-  function _basicConfig() internal pure returns (DataTypes.InitReserveConfig memory c) {
+  function _basicConfig(
+    uint256 ltv,
+    uint256 liquidationThreshold,
+    uint256 decimals
+  ) internal pure returns (DataTypes.InitReserveConfig memory c) {
     c = DataTypes.InitReserveConfig({
-      ltv: 7500,
-      liquidationThreshold: 8000,
+      ltv: ltv,
+      liquidationThreshold: liquidationThreshold,
       liquidationBonus: 10_500,
-      decimals: 18,
+      decimals: decimals,
       frozen: false,
       borrowable: true,
       borrowCap: 0,
```

```solidity
  function testLiquidationLTV() external {
    _mintAndApprove(alice, tokenA, 3000 ether, address(pool));
    _mintAndApprove(alice, tokenB, 3000 ether, address(pool));

    _mintAndApprove(bob, tokenC, 100 ether, address(pool));

    vm.startPrank(bob);
    pool.supplySimple(address(tokenC), bob, 30 ether, 0);
    vm.stopPrank();

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 1500 ether, 0);
    pool.supplySimple(address(tokenB), alice, 750 ether, 0);
    pool.borrowSimple(address(tokenC), alice, 20.25 ether, 0);
    vm.stopPrank();

    oracleC.updateAnswer(125e8);

    uint256 snapshot = vm.snapshot();
    // tokenB then tokenA
    vm.prank(bob);
    pool.liquidateSimple(address(tokenB), address(tokenC), pos, type(uint256).max);

    (, , uint256 availableBorrowsBase1, uint256 currentLiquidationThreshold1, uint256 ltv1, uint256 healthFactor1) = pool
      .getUserAccountData(alice, 0);

    assertGt(availableBorrowsBase1, 0);
    assertGe(healthFactor1, 1e18);

    // Second liquidation fails
    vm.expectRevert(bytes('HEALTH_FACTOR_NOT_BELOW_THRESHOLD'));
    vm.prank(bob);
    pool.liquidateSimple(address(tokenA), address(tokenC), pos, type(uint256).max);

    vm.revertTo(snapshot);

    // tokenA then tokenB
    vm.prank(bob);
    pool.liquidateSimple(address(tokenA), address(tokenC), pos, type(uint256).max);

    (, , uint256 availableBorrowsBase2, uint256 currentLiquidationThreshold2, uint256 ltv2, uint256 healthFactor2) = pool
      .getUserAccountData(alice, 0);

    assertEq(availableBorrowsBase2, 0);
    assertLt(healthFactor2, 1e18);

    assertGt(availableBorrowsBase1, availableBorrowsBase2);
    assertGt(currentLiquidationThreshold1, currentLiquidationThreshold2);
    assertGt(ltv1, ltv2);
    assertGt(healthFactor1, healthFactor2);

    vm.prank(bob);
    pool.liquidateSimple(address(tokenB), address(tokenC), pos, type(uint256).max);
  }
```

### Mitigation

Prioritize lower LTV / Liquidation threshold assets for liquidation.