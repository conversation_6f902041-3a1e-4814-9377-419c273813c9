Mythical Alabaster Snake

Medium

# A position could become less safe after liquidation

a position could be backed by multiple collateral assets, with different ltvs and liquidation threshold
suppose two tokens, A with a  liquidation threshold of 70% and B with a threshold of 90% 
Both A and B are valued at 1000 usd 
suppose at current conditions and prices 
assume bob has a liquidatable position of 2000 usd backed by 1..5A ie 1400 usd (980 borrwable) and .9B  900 usd(ie 810 borrowable)
avg threshold = (1500 * 70 +(900 * 90)) / 2400 = 77.5
health factor = 2400 * 77.5 / 100 / 2000 = 0.93
liquidation bonus = 105%

bob is max liquidatable 
if B is the collateral token used liquidated 
due to bonus about 857.14 of 900 of bobs token would be used for actual debt removal
bobs debt = 2000 - 857.14 = 1142.86

bob now has 1.4A collateral and 1142.86 debt 
avg threshold = 1500 * 70 / 1400 = 70 
health factor = 1500 * 70 / 100 / 1142.86 =  0.918

this leaves bob with an even less secure position than at the start of the liquidation, in a real world scenerio, assets like a would be the more volatile ones and this could easily become a case of insolevency and loss for the protocol
recommendation 
a possible solution would be to liquidate low threshold tokens then make a swap on a dex to the collateral liquidator wants 
