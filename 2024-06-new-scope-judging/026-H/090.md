Shiny Seaweed Mongoose

High

# Liquidiation can leave a position worst off than it was before by removing High LTV assets first.

### Summary

When partially liquidating a position that has multiple collaterals, the liquidation can decrease the health factor of the position, if the asset with the highest LTV is removed first. 

### Root Cause

The root cause of this issue lies in the fact that the liquidator can liquidate an unhealthy position by paying the debt with the collateral with the highest LTV and `liquidationThreshold`, when a debt is backed by multiple collaterals removing the most valuable assets will make the position worst.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L131

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L141

### Internal pre-conditions

- Token A: LTV: 8000, `liquidationThreshold`: 9000.
- Token B: LTV: 3000, `liquidationThreshold`: 4000.

1. <PERSON> supplies $1000 worth of tokens A and B.
2. She borrows  $1000 worth of token C.

### External pre-conditions

The Oracle price for both tokens A and B fell, and the current Health Factor is 0.9 ether, which is below the liquidation threshold.

### Attack Path

_No response_

### Impact

Liquidation should improve the health of a position, by reducing the health factor liquidation will push the position to insolvency.

### PoC

Add the following test to the `forge/core/pool` folder and run the test.

Results we can see that the Health Factor before liquidation was better.

Logs:
  HF before : 836178571428571429
  HF After  : 664108867428734168


```sol
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import './PoolSetup.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';

import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

contract LiquidationPOC is PoolSetup {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;

  address alice = address(1);
  address bob = address(2);

  uint256 mintAmountA = 500 ether;
  uint256 mintAmountB = 2000 ether;
  uint256 supplyAmountA = 500 ether;
  uint256 supplyAmountB = 750 ether;
  uint256 supplyAmountC = 500 ether;
  uint256 borrowAmountB = 700 ether;

  function setUp() public {
    _setUpCorePool();
    poolFactory.createPool(_poolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));

    pos = keccak256(abi.encodePacked(address(owner), 'index', uint256(0)));
    pos = keccak256(abi.encodePacked(alice, 'index', uint256(0)));
  }

  function testLiquidationSimple() external {
    _generateLiquidationCondition();
    (,,,,,uint healthFactorBefore) = pool.getUserAccountData(alice, 0);
  
    vm.startPrank(bob);
    vm.expectEmit(true, true, true, false);
    emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, supplyAmountA);
    vm.stopPrank();

    (,,,,, uint healthFactorAfter) = pool.getUserAccountData(alice, 0);

    console.log("HF before :", healthFactorBefore);
    console.log("HF After  :", healthFactorAfter);

  }



  function _generateLiquidationCondition() internal {
    oracleA.updateAnswer(5e10);
    oracleC.updateAnswer(5e10);

    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 500 tokenA
    _mintAndApprove(alice, tokenC, mintAmountA, address(pool)); // alice 500 tokenC

    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 550 tokenA alice supply
    pool.supplySimple(address(tokenC), alice, supplyAmountC, 0); // 2 tokenC alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, borrowAmountB, 0); // 100 tokenB alice borrow
    vm.stopPrank();

    assertEq(tokenB.balanceOf(alice), borrowAmountB);

    oracleA.updateAnswer(1.801e8);
    oracleC.updateAnswer(1.801e8);
  }


  function _poolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](4);
    assets[0] = address(tokenA);
    assets[1] = address(tokenB);
    assets[2] = address(tokenC);
    assets[3] = address(wethToken);

    address[] memory rateStrategyAddresses = new address[](4);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    rateStrategyAddresses[2] = address(irStrategy);
    rateStrategyAddresses[3] = address(irStrategy);

    address[] memory sources = new address[](4);
    sources[0] = address(oracleA);
    sources[1] = address(oracleB);
    sources[2] = address(oracleC);
    sources[3] = address(oracleD);

    DataTypes.InitReserveConfig memory config = _basicConfig();

    DataTypes.InitReserveConfig memory config2 = _basicConfig();

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](4);

    //LTV 8000
    config.ltv = 8000;
    config.liquidationThreshold = 9000;
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    //LTV 3000
    config2.ltv = 3000;
    config2.liquidationThreshold = 4000;
    configurationLocal[2] = config2;
    configurationLocal[3] = config2;


    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

}

```

### Mitigation

1. Liquidation should check if the health factor increases, if it doesn't increase it should revert.
2. Liquidation should prioritize the low LTV assets.