Careful Fleece Pike

Medium

# Missing a `updateInterestRates` step in `executeMintToTreasury`

### Summary

Missing a `updateInterestRates` step in `executeMintToTreasury` will cause `liquidityRate` and `borrowRate` of an reserve to be a wrong value.

### Root Cause

It is evident in the pool's actions such as: supply, withdraw, borrow, repay, the function `updateInterestRates` is called to update the `liquidityRate` and `borrowRate` of an reserve basing the `_liquidityAdded` or `_liquidityTaken`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L69

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L125

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L88

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139

But in the `executeMintToTreasury` function, the liquidity is taken out of the pool but the `updateInterestRates` is not called to update the `liquidityRate` and `borrowRate` of the reserve

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }

```

### Internal pre-conditions

`accruedToTreasuryShares` is greater than zero. This will happen when the interest from the borrowers accrues or there is a flash loan.

### External pre-conditions

_No response_

### Attack Path

A user withdraws from a pool, which in turn triggers the `executeMintToTreasury` function 

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolSetters.sol#L84

### Impact

`liquidityRate` and `borrowRate` of a reserve will be wrong until someone triggers the `updateInterestRates` function to the reserve.

### PoC

_No response_

### Mitigation

Call to `updateInterestRates` in `executeMintToTreasury`.