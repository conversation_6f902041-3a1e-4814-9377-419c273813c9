Clever Ebony Halibut

Medium

# `executeMintToTreasury()` doesn't update state causing a loss of yield

## Summary
The `executeMintToTreasury()` function doesn't update the state, whith the amount of fees that is leaving the protocol as fees to the treasutry 


## Vulnerability Detail
As we can see in the  executeMintToTreasury functions the `supplyShares` get's reduced by the amount of protocol accrued fees. This amount that is flowing out of the protocol affects the interest rate calculation, as the amount of liquidity available in the protocol is lowered, which will affect the utilitzation ratio which is required in every interestRatecalaculation 
```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;
      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```
The second impact of this bug is the fact that the accounting for  reserve.underlyingBalance would be broken. As it needs to be reduced by the amount of tokens that will be sent to the treasury

## Impact
Due to the missing interestrateUpate call, the amount of accrued fees that left the protocl to be sent to the treasury are not registered, leading to a lowered interest rate, than the actual rate. This leads in the protocol having a lesser interest  rate than it should, which results in a loss of yield for the protocol and the liquidity providers

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83C1-L103C4
## Tool used

Manual Review

## Recommendation
We recommend that the protocol add an interestRate update with the amount of tokens accrued to the treasury to correctly mirror the right interest rate calculations
The fix sould look like this


```diff
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
++    bytes32 pos
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
++    reserve.updateInterestRates(totalSupplies, cache, params.asset, IPool(address(this)).getReserveFactor(), 0, amountToMint, "", "");        
      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;
      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```