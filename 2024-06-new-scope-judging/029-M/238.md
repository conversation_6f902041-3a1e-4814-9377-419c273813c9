Joyous Cedar Tortoise

Medium

# executeMintToTreasury does not update interest rate

### Summary

Whenever assets are transferred to/from a pool, the utilization rate changes. This means that the interest rate should be updated.

[`executeMintToTreasury`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) does not update interest rate after transferring out assets.

After `executeMintToTreasury`  is called, it transfers out assets, this increases the utilization ratio, therefore the interest rate should be increased to reflect the change in utilization

### Root Cause

Not updating the interest rate after sending out assets in `executeMintToTreasury`

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Borrowers pay less interest than they should

Suppliers earn less interest than they should

### PoC

The following POC shows that due to not updating the interest rate in `executeMintToTreasury` , borrowers pay 0.2% less interest and the suppliers earn 0.2% less interest.

You can compare what the actual index should be by applying the recommendation to update the interest rate in `executeMintToTreasury` , and then running the test again, it will return an index that is 0.2% larger.

Add the following to `PoolFlashLoanTests` 

<details>
<summary> Coded PoC </summary>

```solidity
function flashLoanHelper() public {
    // setup and perform 10 flash loans to accrue some shares for the treasury
    bytes memory emptyParams;
    MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
    address flashLoaner = makeAddr('flashLoaner');
    _mintAndApprove(flashLoaner, tokenA, 5000 ether, address(pool));

    for (uint256 i = 0; i < 10; i++) {
      pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 900 ether, emptyParams);
    }
  }

  function test_executeMintToTreasuryDoesNotUpdateInterestRate() external {
    // Setup the users
    address borrower = makeAddr('borrower');
    _mintAndApprove(borrower, tokenB, 5000 ether, address(pool));
    _mintAndApprove(alice, tokenA, 500 ether, address(pool));
    _mintAndApprove(bob, tokenA, 500 ether, address(pool));

    // Set the reserve factor
    poolFactory.setReserveFactor(10_000);

    // Set the flash loan fee
    poolFactory.setFlashloanPremium(200);

    // Users supply token A to the pool
    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 500 ether, 0);

    vm.startPrank(bob);
    pool.supplySimple(address(tokenA), bob, 500 ether, 0);

    // borrower deposits collateral and borrows
    vm.startPrank(borrower);
    pool.supplySimple(address(tokenB), borrower, 1000 ether, 0);
    pool.borrowSimple(address(tokenA), borrower, 100 ether, 0);
    vm.stopPrank();

    // bunch of flash loans happen to accrue to the treasury
    flashLoanHelper();

    DataTypes.ReserveData memory data = pool.getReserveData(address(tokenA));
    assertNotEq(data.accruedToTreasuryShares, 0);

    // Alice withdraws all her token A, this will also send out `accruedToTreasuryShares` of tokenA to the treasury
    // but it will not update the interest rate
    vm.startPrank(alice);
    pool.withdrawSimple(address(tokenA), alice, type(uint256).max, 0);

    // The borrower has been paying a lower interest rate as a result for the next 30 days
    vm.warp(block.timestamp + 30 days);

    uint256 currentIndex = pool.getReserveNormalizedVariableDebt(address(tokenA));
    console.log(currentIndex);
    // 1001801815800994309405365247, is the index when not updating interest

    // Now add the recommendation to the `executeMintToTreasury` function
    // re run the test

    // 1003832740936327768897371150, is the index when updating interest rate
    // it is 0.2% higher
    // this means that previously when the interest was not updated, the borrower paid 0.2% less interest
    // and suppliers earned 0.2% less interest than they should have
  }
```
</details>

### Mitigation

Update the interest rate after transferring out assets in `executeMintToTreasury`

```diff
DataTypes.ReserveCache memory cache = reserve.cache(totalSupply);

+reserve.updateInterestRates(
+  totalSupply,
+  cache,
+  asset,
+  10_000,
+  0,
+  amountToMint,
+  0,
+  ""
+);
```