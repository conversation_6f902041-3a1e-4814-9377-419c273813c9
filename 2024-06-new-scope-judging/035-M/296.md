Polished Iris Antelope

Medium

# Allowances are not cleared from a ```CuratedVault``` to a ```Pool``` after its removal leading to potential issues in next implementations of the ```Pool``` (in scope as per README).

## Summary
When a ```Pool``` is added on the ```supplyQueue``` of a ```CuratedVault```, it is approving ```type(uint256).max``` on it but if it gets removed, this approval do not get erased.

## Vulnerability Detail
According per README, potential issues in future integrations are welcome and should be reported. As we can see in the ```setSupplyQueue()```, when a new ```Pool``` is added, the ```CuratedVault``` approves it max :
```solidity
  function setSupplyQueue(IPool[] calldata newSupplyQueue) external onlyAllocator {
    uint256 length = newSupplyQueue.length;

    if (length > MAX_QUEUE_LENGTH) revert CuratedErrorsLib.MaxQueueLengthExceeded();

    for (uint256 i; i < length; ++i) {
      IERC20(asset()).forceApprove(address(newSupplyQueue[i]), type(uint256).max);
      if (config[newSupplyQueue[i]].cap == 0) revert CuratedErrorsLib.UnauthorizedMarket(newSupplyQueue[i]);
    }

    supplyQueue = newSupplyQueue;
    emit CuratedEventsLib.SetSupplyQueue(_msgSender(), newSupplyQueue);
  }
```
In the current ```Pool``` implementation, it can not happens the ```Pool``` to move funds from the ```CuratedVaults```, however in a future integration it is possible for this to happen. This vulnerability arises form the fact that when the ```Pool``` is removed from the invested pools in the ```CuratedVault```, the approval is not cleared and as a results the ```CuratedVault``` has active allowances to external contracts that may be exploited.

## Impact
The impact of this vulnerability is crucial, if in the next implementations of the ```Pool``` it is able to ```transferFrom``` the ```CuratedVault```. This means that it will be able to move funds out from the Vault without it's approval since it is no more invested in it.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L192-L225
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L177-L189

## Tool used
Manual Review

## Recommendation
Consider deleting all approvals on the ```Pool``` that is about to be removed, after it's removal.