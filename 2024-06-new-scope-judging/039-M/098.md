Chilly Cherry Deer

Medium

# Misaligned scaling in the `validateSupply` calculation will lead to excessive supply for the protocol, this bypass the intended supply cap

###  Details

Misaligned scaling in the `validateSupply` calculation will lead to excessive supply for the protocol, exploit lead this  discrepancy to bypass the intended supply cap.

[ValidationLogic.sol#L85-L86](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L85-L86)

```solidity
// supply calculation : the total supply, including the new amount, should not exceed the supply cap unless the cap is zero.
require(
      supplyCap == 0
// @audit-issue `total supply is not scaled to match the supply cap`
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
```
- `totalSupplies.supplyShares`: Represents the current supply shares.
-  `reserve.accruedToTreasuryShares`: Represents shares accrued to the treasury.
-  total supply: Adds `totalSupplies.supplyShares`  and `reserve.accruedToTreasuryShares` to get the current supply state.
- Interest Adjustment: Uses `rayMul(cache.nextLiquidityIndex)` to adjust the total supply for interest accruals.
- New Amount Addition: Adds `params.amount`  to the adjusted total supply.
- `supplyCap * (10 ** cache.reserveConfiguration.getDecimals())`: Represents the maximum allowable supply, adjusted for decimals.
- Scaling the supply cap: Compares the adjusted total supply (including the new amount) against `supplyCap * (10 ** cache.reserveConfiguration.getDecimals())`
- where ` uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();` represents the maximum allowed supply.
- `cache.reserveConfiguration.getDecimals()` returns the number of decimal places used for scaling.

#### Issue : The units are not aligned, as the total supply is not scaled to match the supply cap, leading to an incorrect comparison.
- 
` totalSupply = (totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount`

- 
`scaledSupplyCap = supplyCap * (10 ** cache.reserveConfiguration.getDecimals())`



### Root Cause

root cause of the issue lies in the misalignment of unit scaling between the total supply calculation and the supply cap comparison within the `validateSupply` function.
- [ValidationLogic.sol#L85-L86](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L85-L86)

-  The total supply, calculated as 
`(totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount`, is not scaled to match the unit scale of the supply cap, which is scaled by `(10 ** cache.reserveConfiguration.getDecimals())`.

### Internal pre-conditions

The issue arises directly from the logic used in the `validateSupply` function

### External pre-conditions
n/a

### Attack Path

1. attacker identifies that the total supply calculation `(totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount` is not scaled to match the unit scale of the supply cap.

2. they initiates a supply transaction with an amount that, due to the scaling misalignment, appears to be within the supply cap when checked by the protocol's logic.

3. The protocol processes the transaction, comparing the unscaled total supply against the scaled supply cap `(supplyCap * (10 ** cache.reserveConfiguration.getDecimals()))`, leading to a false positive that the supply is within limits.
4.  they successfully supplies more assets than the protocol intends to allow, bypassing the supply cap due to the incorrect comparison.
5. Cumulative Exploitation: If they exploit this flaw, the total supply could significantly exceed the intended limits, leading to potential liquidity issues and destabilizing the protocol's economic model.

6. The excessive supply affects liquidity management, potentially leading to financial loss/instability.

### Impact

- the code compares the total supply, adjusted by interest and new amounts, against a scaled supply cap. As these units are not aligned, the comparison fails to enforce the intended limit.

- the scaling of the total supply and the supply cap are misaligned, the protocol incorrectly allow users to supply more assets than intended. This could lead to a situation where the supply cap is effectively bypassed.


### Mitigation

ensure a valid comparison, both the total supply and the supply cap should be in the same unit scale.