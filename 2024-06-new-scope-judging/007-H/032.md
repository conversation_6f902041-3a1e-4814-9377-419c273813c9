Loud Rosewood Oyster

Medium

# Inconsistent Borrow Shares Handling Prevents Full Debt Repayment

## Summary
Borrowing more than once could make it impossible to fully repay the borrowed debt without leaving dust behind
## Vulnerability Detail
Repay logic breaks when a user borrows more than once
+ To Illustrate:

Case 1:

user A borrows 100 USDC at the borrow index of 1, in [PositionBalanceConfiguration::borrowDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L62-L74), the shares minted are calculated as borrow amount/index = 100 / 1 ==> 100
debt shares = 100
lastDebtLiquidtyIndex = 1
If the user decides to repay all of his debt at a later borrow index of 1.1, [getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140) returns:
debt shares + accrued borrow interest
interest = 100 * 1.1 - 100 * 1 => 110 - 100 ==> 10
 = 100 + 10 ==> 110
In repayDebt, sharesBurnt = 110 / 1.1 ==> 100
this thus leaves zero debt

Case 2:

User A Borrows 50 USDC at index 1
debt shares = 50
borrows another 50 USDC when the borrow index is 1.1. Shares minted = borrow amount / current borrow index
= 50 / 1.1 ==> 45.45
debt shares = 50 + 45.45 => 95.45

For simplicity, assuming the user wishes to repay all of his debt with a current borrow index of 1.1

[getDebtBalance](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L137-L140) returns: 95.45 x 1.1 - 95 x 1.1 => 95.45 + 0
==> 95.45
In [repayDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L118), sharesBurnt = 95.45 / 1.1 ==> 86.7 leaving approximately 8 shares behind.


## Impact
contrary to the comment [_here_](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L128), it's impossible to completely clear a debt, if the user borrows again after an increment in the borrow index
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L118
## Tool used

Manual Review

## Recommendation
calculate and store users' accrued borrow interests before updating their position `lastDebtLiquidtyIndex` to the current index during borrowing.

During repayments, create a functionality that updates and stores a position borrow interests, this functionality should then return the user-updated debt shares in assets.