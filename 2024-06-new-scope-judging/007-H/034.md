Loud Rosewood Oyster

High

# Premature Interest Rate Update Leads to Miscomputed Liquidity and Borrow Rates

## Summary
During debt repayment in the pool, interest rates are wrongly updated before paying off the debt, thus always resulting in the computation of wrong rates, due to using an outdated total debt.

## Vulnerability Detail
During all liquidity changes in the pool, [ReserveLogic::updateInterestRates](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145-L182) is called to update borrow and liquidity rates. 
One of the variants used for the calculation is the reserve asset's total debt, the intended behaviour is to update the rates if the debt changes, hence during repayment of debt, the rates are to be updated after a debt has been repaid. However, the current implementation updates the interest rates before repaying debt, thus updating the rates using an outdated total debt.

+ To Illustrate:

Assuming the current total debt of a reserve is 1,000 If a borrower repays 200, the new total debt is now 800, but the function currently updates the interest using total debt as 1,000, repaying and updating the cached total debt to 800 after the interest rates have already been updated.

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L117-L160

```solidity
    // update balances and total supplies
    payback.shares = balances.repayDebt(totalSupplies, payback.assets, cache.nextBorrowIndex);
    cache.nextDebtShares = totalSupplies.debtShares;
```
## Impact
Wrong liquidity and borrow rates will further result in incorrect liquidity and borrow index, this could result in healthy positions appearing unhealthy and could also inflate borrowers' paid interests.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145-L182
## Tool used

Manual Review

## Recommendation
Update the current implementation to update interest rates only after repaying and updating the cached debt shares