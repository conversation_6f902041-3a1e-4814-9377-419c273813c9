Acrobatic Banana Poodle

Medium

# `BorrowLogic::executeRepay` incorrect order of update may cause incorrect interest rates

### Summary


`BorrowLogic::executeRepay` updates the interest rate first before updating the `nextDebtShares`, which will result in out-dated evaluation of total debt. This incorrect evaluation may lead to incorrect interest rate depending on the interest rate strategy.


### Root Cause

`BorrowLogic::executeRepay` updates the interest rates via `reserve.updateInterestRates` and then update the `cache.nextDebtShares`: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139-L152

However, the cache's `nextDebtShares` will be used in the interest rates calculation to evaluate the total debt: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158 



Not that in [Aave V3's executeRepay function](https://github.com/aave/aave-v3-core/blob/6d6fa53d360b43f492ff5b3c7033f95aee4f1335/contracts/protocol/libraries/logic/BorrowLogic.sol#L221-L232) the interest rate is correctly updated after reserveCache was updated.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

As the result the interest rate calculation (line 139 BorrowLogic.sol) will use the previous `nextDebtShares` and as the result use the incorrect (not-updated) total debt value to calculate the liquidity rate and borrow rate.
Depending on the interest rate strategy, this out-dated total debt will lead to incorrect interest rate information, which may cause unexpected behavior or potential exploitation.

### PoC

_No response_

### Mitigation

Consider changing `BorrowLogic::executeRepay` to first adjust the `cache.nextDebtShares` variable before calling `reserve::updateInterestRates()`.

