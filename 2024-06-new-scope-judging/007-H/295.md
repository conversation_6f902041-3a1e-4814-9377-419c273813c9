Special Macaroon Shetland

Medium

# Interest rate is not updated correct after debt repayment, it can cause higher borrowing rate

### Summary

In [BorrowLogic.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139) the interest rate is updated. Due to wrong execution sequence, `totalDebt` is forwarded through interest rate strategy with wrong number. 

### Root Cause

The root cause of this problem is wrong sequence diagram. Repaid amount is determined just after the `repayDebt()` call and we need this data for correct interest rate update. 
```solidity
   reserve.updateInterestRates(
      totalSupplies,
      cache,
      params.asset,
      IPool(params.pool).getReserveFactor(),
      payback.assets,
      0,
      params.position,
      params.data.interestRateData
    );

    // update balances and total supplies
    payback.shares = balances.repayDebt(totalSupplies, payback.assets, cache.nextBorrowIndex);
&>  cache.nextDebtShares = totalSupplies.debtShares;
```

`cache.nextDebtShares` should be updated before updating the interest rate because it's used in interest rate calculation process.


```solidity
&>  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
```

### Internal pre-conditions

N/A

### External pre-conditions

N/A

### Attack Path

In order to demonstrate the impact following scenario will be appropriate.

1. Alice has 100WETH debt.
2. Alice repaid her debt now `totalDebt` should be reduced but instead it will keep same.
3. Borrowing rate will be higher than expected because `totalDebt` will remain high.

### Impact

High - Because of wrong execution sequence, interest rate is affected. Interest rate will affect  borrowing index and this is crucial for correct execution and calculation. In the conclusion, it will affect the reserve index.

Reserve indexes is also important, because they are used to calculate the price of shares. Relation between assets and shares is given in following equation:

$Shares * Index = Assets$

 If an user repay his debt, the next user have to pay higher than expected because borrow index will be higher than expected.
### Mitigation

It should update the interest rate just after determining the `nextDebtShares` variable

```solidity

    // update balances and total supplies
    payback.shares = balances.repayDebt(totalSupplies, payback.assets, cache.nextBorrowIndex);
    cache.nextDebtShares = totalSupplies.debtShares;

    reserve.updateInterestRates(
      totalSupplies,
      cache,
      params.asset,
      IPool(params.pool).getReserveFactor(),
      payback.assets,
      0,
      params.position,
      params.data.interestRateData
    );
```
    