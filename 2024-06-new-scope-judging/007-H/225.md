Curly Pineapple Armadillo

Medium

# Debt rates will be wrongly set when debt is repaid

### Summary

In `executeRepay` interest rates are updated before the debt is repaid, causing the new debt rates to be calculated wrongly as `vars.totalDebt` will be set to a value prior to the repayment.

### Root Cause

- In [`executeRepay`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L117) the interest rates are calculated before the debt is actually repaid and the previous value of `totalSupplies.debtShares`, prior to the repayment, is used when getting `vars.totalDebt`. `vars.totalDebt` is used when calculating what the debt rate should be when calling [`calculateInterestRates`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L161). Thus, as the total debt includes the debt that is removed/repaid, `vars.totalDebt` will be higher, causing the borrow rate to be higher than expected.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

- The borrow rate will be higher than expected after debt is repaid. This causes a loss of funds for borrowers and potential unexpected liquidations as borrowers will have to account for the higher borrow rate.

### PoC

_No response_

### Mitigation

The borrow rate should be updated after the debt is repaid, just as it is currently done in [`executeBorrow`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51). In `executeBorrow` when `updateInterestRates` is called the new value of `_cache.nextDebtShares` is used.