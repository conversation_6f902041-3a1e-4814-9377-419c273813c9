Acrobatic Rainbow Grasshopper

High

# Interest rate will be wrong after a repay

## Summary
Interest rate will be wrong after a repay
## Vulnerability Detail
Upon repaying, we call `ReserveLogic::updateInterestRates()` to update the interest rates. There, to calculate that, we use the total debt:
```solidity
vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);
(vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt, <@
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
```
Then, a bit later in the borrowing flow, we have this line:
```solidity
cache.nextDebtShares = totalSupplies.debtShares;
```
This was the value used to calculated the total debt that is then used to determine the new rates. Based on that, we know that this value is out-of-sync as it's updated afterwards. This makes the new rates wrong as they used an out-of-sync value. For example, let's take a look at the Aave codebase (https://github.com/aave/aave-v3-core/blob/b74526a7bc67a3a117a1963fc871b3eb8cea8435/contracts/protocol) which this protocol was heavily inspired from (`ReserveLogic::updateInterestRates()`):
```solidity
vars.totalVariableDebt = reserveCache.nextScaledVariableDebt.rayMul(
      reserveCache.nextVariableBorrowIndex
    );

    (
      vars.nextLiquidityRate,
      vars.nextStableRate,
      vars.nextVariableRate
    ) = IReserveInterestRateStrategy(reserve.interestRateStrategyAddress).calculateInterestRates(
      DataTypes.CalculateInterestRatesParams({
        unbacked: reserve.unbacked,
        liquidityAdded: liquidityAdded,
        liquidityTaken: liquidityTaken,
        totalStableDebt: reserveCache.nextTotalStableDebt,
        totalVariableDebt: vars.totalVariableDebt, <@
        averageStableBorrowRate: reserveCache.nextAvgStableBorrowRate,
        reserveFactor: reserveCache.reserveFactor,
        reserve: reserveAddress,
        aToken: reserveCache.aTokenAddress
      })
    );
```
We can see that it's pretty much the same thing in that function. We calculate the total debt and then we calculate the new rates based on it. However, let's take a look at `BorrowLogic::executeRepay()`:
```solidity
...
else {
      reserveCache.nextScaledVariableDebt = IVariableDebtToken(
        reserveCache.variableDebtTokenAddress
      ).burn(params.onBehalfOf, paybackAmount, reserveCache.nextVariableBorrowIndex);
    }

    reserve.updateInterestRates(
      reserveCache,
      params.asset,
      params.useATokens ? 0 : paybackAmount,
      0
    );
...
```
We can see that first `nextScaledVariableDebt` is updated and only then we update the interest rates, they are clearly in-sync.
## Impact
Interest rate will be wrong after a repay
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139-L152
## Tool used

Manual Review

## Recommendation
Update the variable before updating the interest rates