Powerful Licorice Elephant

Medium

# reserve.updateInterestRates is not done properly in the function executeRepay.

## Summary
cache.nextDebtShares is updated before reserve.updateInterestRates update in the function executeBorrow. But in the function executeRepay,cache.nextDebtShares is updated after reserve.updateInterestRates update which is incorrect. As a result, _reserve.liquidityRate/_reserve.borrowRate value may become unexpected.

## Vulnerability Detail
when  the function executeRepay is called, totalSupplies.debtShares is increased based on new borrowed amounts. cache.nextDebtShares stores the previous debtshares(i.e debtshares before adding the new borrowed amounts to the debtshares) of the reserve. 

After increasing  totalSupplies.debtShares based on new borrowed amounts, cache.nextDebtShares is updated to totalSupplies.debtShares i.e  cache.nextDebtShares = totalSupplies.debtShares;

Afterthat  reserve.updateInterestRates is called to update the interest rate.here reserve.updateInterestRates takes DataTypes.ReserveCache memory cache i.e cache as input.in cache cache.nextDebtShares is an important value because see   function updateInterestRates(library ReserveLogic) _reserve.liquidityRate/_reserve.borrowRate is updated based on   vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);


But when the function executeRepay is called, cache.nextDebtShares is updated to totalSupplies.debtShares after calling  reserve.updateInterestRates which is incorrect. When repaying, totalSupplies.debtShares is decreased, so InterestRates should update based on this decreased  totalSupplies.debtShares. As cache.nextDebtShares is updated to totalSupplies.debtShares after calling  reserve.updateInterestRates ,so interest rate is updated based on previous  totalSupplies.debtShares(which is bigger) which will result incorrect _reserve.liquidityRate/_reserve.borrowRate.


## Impact
_reserve.liquidityRate/_reserve.borrowRate value may become unexpected.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L152
## Tool used

Manual Review

## Recommendation

in the function executeRepay,cache.nextDebtShares should be  updated before reserve.updateInterestRates update.