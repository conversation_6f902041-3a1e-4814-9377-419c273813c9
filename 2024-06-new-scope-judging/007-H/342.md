Dizzy Ra<PERSON>berry Pig

High

# Wrong logic in BorrowLogic.sol::executeBorrow library will lead to higher-than-expected interest rates calculation for existing borrowers and lenders.

### Summary

In the BorrowLogic.sol library, a user's amount-to-be-borrowed is added to the totalSupplies.debtShares during the call to `PositionBalanceConfiguration::borrowDebt()` function. #see below:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L82

However, the update of  ` cache.nextDebtShares = totalSupplies.debtShares;` in `BorrowLogic.sol::executeBorrow()::L83` will skew the actual utilization rate of the system, resulting in an inaccurate inflation of the system's debt at that specific moment of the borrow operation. This is due to the fact that, the value of **cache.nextDebtShares** is used in the calculation of interest rates on the reserve in `ReserveLogic.sol::updateInterestRates()` function. See Below:
    https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158

Now, due to the system's debt being overinflated due to the current implementation, the returned borrow rates and liquidity rates will be inaccurate and wrong. What this implies is that, there is an higher-than-expected interest rates posed on existing borrowers and lenders which is incorrect.

### Root Cause

updating `cache.nextDebtShares = totalSupplies.debtShares` in `BorrowLogic.sol::executeBorrow()::83` to be used in the interest rates calculation is a logical bug that would use an overinflated system's debt value as the borrowable amount has not yet been transferred to the user at this time of interest calculation. 

Now, the increment of **cache.nextDebtShares** to include a user's yet-to-be-borrowed amount would be a double dipping of the system's debt shares. It is my understanding that interest rates are calculated based on utilization rates. That is, the interest rate is supposed to reflect the system's state before any new borrow occurs.
However, since the user's debt shares are added to `cache.nextDebtShares`, it implies the code is applying future states of users to the current interest rate calculation, which will skew the actual accurate utilization rate of the system. 

Interest rates calculation should be based on a state prior to the new borrow, maintaining fairness for existing borrowers and lenders. However, with the current logic implementation, the utilization rate is artificially inflated leading to a higher-than-expected interest rates for users of Zerolend. 

### Internal pre-conditions

This is bound to happen anytime borrow operation is invoked on Zerolend.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Users are charged a higher-than-expected interest rates which is unfair to existing borrowers and lenders of Zerolend.

### PoC

1. Since the BorrowLogic.sol library is a fork of AAVE's v3-origin codebase, I am going to demonstrate that AAVE doesnot implement this wrong logic in its code. #see below:

https://github.com/aave-dao/aave-v3-origin/blob/6948864fc7e74b2b29fadfe2007998992060f84b/src/core/contracts/protocol/libraries/logic/BorrowLogic.sol#L125-L127
```solidity
      (isFirstBorrowing, reserveCache.nextScaledVariableDebt) = IVariableDebtToken(
        reserveCache.variableDebtTokenAddress
      ).mint(params.user, params.onBehalfOf, params.amount, reserveCache.nextVariableBorrowIndex);
```

After AAVE has minted the debt shares of the user's borrow amount to the system, it doesnot update its `reserveCache.nextScaledVariableDebt` to include the user's minted debt shares. It still uses the previous debt shares of the system prior to the user's borrow to calculate its interest rates, which is the required correct logic. # see below where it calls its interest rates update function with the cached system debt shares prior to the user's borrow.
https://github.com/aave-dao/aave-v3-origin/blob/6948864fc7e74b2b29fadfe2007998992060f84b/src/core/contracts/protocol/libraries/logic/BorrowLogic.sol#L146
```solidity
    reserve.updateInterestRatesAndVirtualBalance(reserveCache, , , );
```


2. Aside AAVE's logic being wrongly implemented, I am going to analyse the repayment logic of Zerolend in `BorrowLogic.sol::executeRepay()` function to ascertain whether it uses the cached system debt shares prior to the user's repayment to calculate the interest rates or follows the wrong logic in `BorrowLogic.sol::executeBorrow()`

In `BorrowLogic.sol::executeRepay()` function, it calls the `reserve.updateInterestRates()` function first using the cached system debt shares prior to a user's repayment to calculate the interest rates of the system. # see below:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L139-L147

It is only after the interest rates have been calculated using the system debt shares prior to repayment, that `cache.nextDebtShares` is updated to include user's repayment shares. # see below:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L151-L152

And besides `cache.nextDebtShares = totalSupplies.debtShares` on L152 of `BorrowLogic.sol::executeRepay()` function is not used anywhere in the function afterwards, making it redundant, so it might as well be deleted.

### Mitigation

The recommended mitigation is to follow AAVE's logic so that users of Zerolend gets accurate interest rates calculation applied to them. 
Delete the `cache.nextDebtShares = totalSupplies.debtShares;`  from the `BorrowLogic.sol::executeBorrow().L83` function

```solidity
-     cache.nextDebtShares = totalSupplies.debtShares;
```

As an additional recommendation, since the`cache.nextDebtShares = totalSupplies.debtShares;` on L152 of `BorrowLogic.sol::executeRepay()` is not used anywhere in the function afterwards, it might as well be deleted as it makes the codebase redundant.