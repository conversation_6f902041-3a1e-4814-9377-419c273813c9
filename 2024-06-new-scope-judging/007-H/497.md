Massive Glass Crane

High

# Pool.sol:liquidate()::Since cache.nextDebtShares is not  updated prior wrong interest rate is calculated.

### Summary
  [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239) `_repayDebtTokens()` is used to calculate the `nextDebtShares` for the `debtReserve` that the liquidator is liquidating.

  After that , [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163) `updateInterestRate()` is executed for the `debtReserve` to update the new `interestRate` considering the `newDebtShare` of the reserve.

But there are 2 reasons why the new `interestRate` reflects the wrong value.
1) `vars` variable which contains the value of `debtReserveCache` is passed as a `memory` instead of `storage`.
2) instead of subtracting `burnt` shares from  `vars.debtReserveCache.nextDebtShares`, they are assigning the burnt shares to the same.

### Root Cause

When a `liquidator` executes a liquidation , `debtShares` are `burnt` from the `debtReserve` specified by the `liquidator` using the [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161) `_repayDebtTokens()`.

```solidity
_repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset])
```
here the `vars` has the key `debtReserveCache` which stores the `reserveData` for that debtAsset.

Now in the `_repayDebtTokens()` , vars is actually treated as a `memory` variable and hence the changes that has been made to the `debtReserveCache` is lost when the function pointer comes outside of the function.
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
 =>   LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    vars.debtReserveCache.nextDebtShares = burnt;
  }
  ```

And we are continuing to call `updateInterestRate()` pasing the same un-updated `vars.debtReserveCache` as the `_cache` parameter which in turn uses it to find the new `interestRate`.[ReserveLogic.sol#L158](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158)


Hence the new InterestRate will be calculated incorrectly casuing losses to the protocol and users.


(Note: vars.debtReserveCache.nextDebtShares is incorretly asiggned to burnt shares instead of subtracting the same. This becomes an issue only when the first issue is resolved.)


### Internal pre-conditions
Liquidator executes liquidation.


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact
Since the wrong interest rate is calculated because the reserveCache is not updated , it will inflict losses to the protocol and the users.


### PoC


### Mitigation
 use `storage` instead of `memory` and also correct the calculation of nextdebtShares.

Or add this code before calling updateInterestRate in the executeLiquidation()
```solidity
 vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;
```

