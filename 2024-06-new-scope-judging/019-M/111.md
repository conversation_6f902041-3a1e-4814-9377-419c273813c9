Polite Spruce Duck

High

# Mitigation for inflation attacks is wrongly placed


## Summary

Zerolend attempts to integrate the popular Openzeppelin fix for the classic vault/pools inflation attack, however the mitigation is incorrectly applied.

## Vulnerability Detail

Take a look at https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L32-L52

```solidity
  uint256 internal constant VIRTUAL_SHARES = 0;

  /// @dev A number of virtual assets of 1 enforces a conversion rate between shares and assets when a market is
  /// empty.
  uint256 internal constant VIRTUAL_ASSETS = 0;

  /// @dev Calculates the value of `shares` quoted in assets, rounding down.
  function toAssetsDown(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivDown(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }

  /// @dev Calculates the value of `shares` quoted in assets, rounding up.
  function toAssetsUp(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivUp(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }
```

This is the `SharesMathLib` that always gets used whenever there is a share price conversion to be processed.

Now per the [documentation](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L22-L23), this implementation is to curb the popular inflation attack bug window, to quote the docs:

> /// @notice Shares management library.
> /// @dev This implementation mitigates share price manipulations, using OpenZeppelin's method of virtual shares:
> /// https://docs.openzeppelin.com/contracts/4.x/erc4626#inflation-attack.

Considering the [`SharesMathLib's` code snippets](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L20-L23) already indicates the protocol is aware of this bug case we are not going too deep into the details of the bug, but rather we'd focus on how it's not been curbed.

Now going to the linked Openzeppelin's article about how this bug case has been made more expensive with their virtual shares/assets and offsets logic, we can deduce it to the fact that these values **are not expected to be zero** otherwise the whole logic has been thrown out the window and the bug case is back active, to quote one of the multiple excerpts that proves this:

> ...the virtual shares and assets make this attack non profitable for the attacker.

This seems practical too, considering in Zerolend's implementation when _quoting_ shares for assets or vice versa, the below is being used: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L39-L52

```solidity
  function toAssetsDown(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivDown(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }

  function toAssetsUp(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivUp(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }
```

But having both our virtual shares and assets as `0` means the real calculation being done is https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L39-L52

- assets = shares.mulDivDown(totalAssets, totalShares);
- assets = shares.mulDivUp(totalAssets, totalShares);

Which leaves the logic open to the inflation attack as explained [here](https://docs.openzeppelin.com/contracts/4.x/erc4626#inflation-attack).

> _Also per the discussion with the sponsors, they had set this to a different value but tests were failing in regards to the accounting state which is why they erroneously reset it back to 0, showcasing how they've forgotten this is to be used for protection against inflation attacks_

## Impact

The vault is vulnerable to the first depositor/inflation attack, considering the mitigation applied is insufficient since no implementation of `VIRTUAL_SHARES` & `VIRTUAL_ASSETS` exist.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L32-L52

## Tool used

Manual Review

## Recommendation

Consider passing a not so high value for `VIRTUAL_SHARES` & `VIRTUAL_ASSETS` but enough to ensure maximum precision and low enough not to inflate the assets.
