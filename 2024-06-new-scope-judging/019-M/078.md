Ancient Crepe Griffin

Medium

# VIRTUAL_ASSETS and VIRTUAL_SHARES are zero in SharesMathLib

## Summary
Here we are both VIRTUAL_ASSETS and VIRTUAL_SHARES are zero.we are calculating the toAssetsDown and toAssetsUp.

## Vulnerability Detail
 function toAssetsDown(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivDown(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }

  //
  function toAssetsUp(uint256 shares, uint256 totalAssets, uint256 totalShares) internal pure returns (uint256) {
    if (totalShares == 0) {
      return 0;
    }
    return shares.mulDivUp(totalAssets + VIRTUAL_ASSETS, totalShares + VIRTUAL_SHARES);
  }
}
## Impact
wrong calculation of toAssetsUp.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/SharesMathLib.sol#L39

## Tool used

Manual Review

## Recommendation
keep value of VIRTUAL_ASSETS and VIRTUAL_SHARES be 1.