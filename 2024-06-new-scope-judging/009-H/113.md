Rapid Onyx Meerkat

High

# The flashloan process adds incorrect liquidity amount can leading to a `LiquidityRate` error

### Summary

When a user successfully repays the borrowed assets to the pool, a `_liquidityAdded` value is used to track the pool's balance. This value is also used to update the `LiquidityRate` and `BorrowRate` via an external `IReserveInterestRateStrategy`. However, the `_liquidityAdded` value is incorrect, leading to potential errors in the rate calculations.

### Root Cause

When a flashloan is executed, the borrowed amount is first transferred from the pool to the user's receiving address. However, during this process, the balance of the underlying tokens is not decreased. When the user repays the borrowed assets, the borrowed amount along with the `totalPremium` is added back to the pool.

[_handleFlashLoanRepayment#L106-L118](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L118)
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;  <@

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');   <@
```

[ReserveLogic.sol#L160-L171](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L160-L171)
```solidity
    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,   <@
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
```


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

1.the calculation result about `LiquidityRate` and `BorrowRate` is incorrect
2.the tracking balance of underlying token is incorrect

### PoC

add test to file `NFTPositionManagerTest.t.sol`
```solidity
  function testFlashLoan() public {
    //alice supply
    uint256 mintAmount = 100 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, 2e18, 1, data);

    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);
    vm.stopPrank();

    //set flashLoanPremiumToProtocol.
    poolFactory.setFlashloanPremium(100);

    //executeMintToTreasury
    tokenA.mint(address(this), 100e18);
    tokenA.approve(address(pool), type(uint256).max);
    pool.flashLoan(address(this), address(tokenA), 2e18, bytes(''), data);

    //get current supply balance.
    DataTypes.ReserveSupplies memory ret
     = pool.getTotalSupplyRaw(address(tokenA));

    console2.log("current total supply:",ret.underlyingBalance);
  }
```

out:
```shell
Ran 1 test for test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[PASS] testFlashLoan() (gas: 698722)
Logs:
  current total supply: 4020000000000000000
Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 11.87ms (1.76ms CPU time)

Ran 1 test suite in 155.01ms (11.87ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

### Mitigation

```diff
@@ -115,7 +116,7 @@ library FlashLoanLogic {
 
     _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
 
-    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
+    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), _params.totalPremium, 0, '', '');
 
     IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);
```