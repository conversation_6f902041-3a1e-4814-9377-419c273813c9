Acrobatic Rainbow Grasshopper

Medium

# Total supply values will be wrong after a flashloan and a DOS is possible

## Summary
Total supply values will be wrong after a flashloan
## Vulnerability Detail
Upon a flashloan, we go through the following flow - `Pool::flashLoan()` -> `PoolSetters::_flashLoan()` -> `FlashLoanLogic::executeFlashLoanSimple()` -> `FlashLoanLogic::_handleFlashLoanRepayment()`.

There is pretty much no logic of importance in the first 2 functions so we will go to `executeFlashLoanSimple()`. There, we validate the reserve is not frozen, calculate the fees, transfer the flashloaned amount and call `executeOperation()` on the receiver for him to do whatever he desires with the received amount:
```solidity
    ValidationLogic.validateFlashloanSimple(_reserve);

    IFlashLoanSimpleReceiver receiver = IFlashLoanSimpleReceiver(_params.receiverAddress);
    uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);
    IERC20(_params.asset).safeTransfer(_params.receiverAddress, _params.amount);

    require(
      receiver.executeOperation(_params.asset, _params.amount, totalPremium, msg.sender, _params.params),
      PoolErrorsLib.INVALID_FLASHLOAN_EXECUTOR_RETURN
    );
```
Then, we call `_handleFlashLoanRepayment()`. There, we calculate the total amount to return (flashloned amount + fee), do some state changes to the reserve, accrue the fees to the treasury, update the interest rates and transfer the total amount to return from the receiver to the contract. The issue is that upon updating the interest rates, we call it with the total amount to return (flashloaned amount + fees):
```solidity
reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
```
That value is used as the added liquidity and is used to update the interest rates and update the total supplies based on that value. However, that is not the actual amount that went into the protocol, only the fees are new in the protocol but the flashloaned amount is not. Thus, the following will happen after a flashloan:
1. There is 100 in liquidity and someone flashloans them
2. Fee is 20 so the user has to repay 120
3. Instead of adding 20 to liquidity, 120 is added so now the total liquidity is 220 which is completely wrong

This also makes overflowing the variable through repeatedly flashloaning an asset a possibility which would DOS all supplies for a particular pool. This is more likely on chains with very low gas fees (such chains are to be used according to README) as a lot of transactions will be needed for that to happen which would not cost much on such chains. For example, on Linea, the gas fees for that would be around 20$:
1. Using Aave liquidity for the DAI pool as example value, currently 1.61e26
2. 3.5e38 is more than the type(uint128).max, thus we need 2173913043478 transactions for an overflow
3. According to a test I wrote, a flashloan would cost 77774 gas
```solidity
  function testFlashloanCostGas() public {
    bytes memory emptyParams;
    FlashloanReceiver mockFlashSimpleReceiver = new FlashloanReceiver(address(pool));
    _generateFlashloanCondition();

    deal(address(tokenA), address(mockFlashSimpleReceiver), 10e18);

    uint256 gasBefore = gasleft();
    vm.startPrank(alice);

    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 100 ether, emptyParams);
    vm.stopPrank();

    uint256 gasAfter = gasleft();

    console.log(gasBefore - gasAfter);
  }
```
5. At the time of writing, gas is 0.06 GWEI on Linea so around 4666 GWEI for 1 transaction
6. That equals 1.0143478e+16 ETH of gas fees costs to overflow it which equals around 22$ at the current prices
## Impact
Total supply values will be wrong after a flashloan
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118
## Tool used

Manual Review

## Recommendation
Deduct the amount from the total supplies before then increasing it by amount + fee as usual