Bright Cloth Troll

Medium

# Flashloan Adds Interest on top of already interest accruing amount

## Summary

Users can acquire flashloans from the pools. The amount taken by the user from the pool is given back to the pool in the same transaction. The amount in the pools are already accruing interest and when they were initially added to the protocol, the interest rate update was applied on this new amount.

## Vulnerability Detail

When user takes a flashLoan the control moves from FlashLoanLogic::executeFlashLoanSimple() -> _handleFlashLoanRepayment() and then ends.

The first function validates the flashLoan and calculates the totalPremium (fee) on the amount being taken by the user in the flashLoan

```solidity
uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);
IERC20(_params.asset).safeTransfer(_params.receiverAddress, _params.amount);
```

The second function _handleFlashLoanRepayment() handles the repayment of the amount + totalPremium known as amountPlusPremium;

```solidity
function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
    // because amount is already being involved in the current interest, the liquidity added to the pool is the premium only.
    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```

The flashloan only has the fee which is coming in as new liquidity, the amount taken by the user and provided back was already present in the pool and was already accruing interest on it but the protocol adds both amountPlusPremium in updating the Interest Rates, this is named as liquidityAdded in the updateInterestRates() function. This breaks the math of the protocol and wrongly computes the new interest rates as the amount was already accounted for before when it was supplied, but the fee was not.


## Impact

Wrongly computes Interest rates of the protocol and inflates the liquidityAdded in the system.

## Code Snippet

[FlashLoanLogic.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L65-L124)

## Tool used

Manual Review

## Recommendation

Only provide the premium as liquidityAdded in the updateInterestRates() function.