Acrobatic Banana Poodle

Medium

# `FlashLoanLogic::executeFlashLoanSimple` incorrectly adds the `amountPlusPremium` to the `underlyingBalance` of the totalSupply

### Summary

`FlashLoanLogic::executeFlashLoanSimple` will incorrectly adds the `amountPlusPremium` to the `underlyingBalance` when only the premium is added

### Root Cause

The `FlashLoanLogic::executeFlashLoanSimple` function does not subtract the amount from the `underlyingBalance` when the amount is transferred out. Then in the `_handleFlashLoanRepayment`, it calls the `_reserve.updateInterestRates` with `amountPlusPremium`, which will add the `amountPlusPremium` to the `totalSupply.underlyingBalance`:

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176



### Internal pre-conditions

- No precondition needed

### External pre-conditions

- No precondition needed

### Attack Path

1. Anyone uses flashloan

### impact

Whenever flashloan is used, it will inflate the `underlyingBalance` for the corresponding reserve. 

### PoC

_No response_

### Mitigation

Subtract the underlyingBalance when the flashloan amount is transferred out

