Long Coffee Cat

Medium

# `totalSupplies.underlyingBalance` is not adopted for flash loans and can be incorrect

### Summary

`updateInterestRates()` logic for `underlyingBalance` treats `_liquidityAdded` as new liquidity, while it is not the case when it is been called from `_handleFlashLoanRepayment()`, where it is new for the balance within the call, but is not for the system balance accumulator, which is not decreased when flash loan funds are being issued

### Root Cause

For flash loans `amountPlusPremium` is not new funds added to the underlying balance, `totalSupplies.underlyingBalance`, only the premium part is:

[FlashLoanLogic.sol#L106-L118](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L118)

```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

>>  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
```

This way `underlyingBalance` will be more and more bloated with each flash loan:

[ReserveLogic.sol#L176-L177](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176-L177)

```solidity
>>  if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
    else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();
```

Which can interfere with downstream systems:

[UIHelper.sol#L91-L121](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/periphery/ui-helpers/UIHelper.sol#L91-L121)

```solidity
  function getPoolAssetConfig(IPool pool, address asset) public view returns (ReserveConfig memory config) {
    DataTypes.ReserveConfigurationMap memory configRaw = pool.getConfiguration(asset);
    DataTypes.ReserveData memory data = pool.getReserveData(asset);
    DataTypes.ReserveSupplies memory supplies = pool.getTotalSupplyRaw(asset);

    ...
    config.supplyShares = supplies.supplyShares;
    config.supplyAssets = pool.totalAssets(asset);
>>  config.underlyingBalance = supplies.underlyingBalance;
```

### Internal pre-conditions

There is a material amount of liquidity available for flash loans. This is true whenever a pool has any substantial amount of available liquidity, i.e. most of the times in going concern conditions

### External pre-conditions

Flash loans system provides are used by outside actors

### Attack Path

Attacker can inflate `underlyingBalance` at the cost of using flash loans. More probable though is unintended inflation of `underlyingBalance` via usual usage of flash loans, when their premium is being paid by external operations where the funds are used, e.g. by arbitrage

### Impact

`underlyingBalance` being reported to all the integrations can be substantially inflated. The error accumulates with each flash loan by the full amount loaned, i.e. the figure can become magnitudes bigger than actual underlying balance quickly enough. It is one of the most crucial metric of any pool and there is high probability that this misreporting causes material impact downstream

### PoC

Ordinary usage of flash loans provides the impact

### Mitigation

Consider adding the flag for the flash loan case, e.g. `isFlashLoan`:

[ReserveLogic.sol#L176-L177](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176-L177)

```diff
-   if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
+   if (_liquidityAdded > 0) totalSupplies.underlyingBalance += isFlashLoan ? _premiumAdded.toUint128() : _liquidityAdded.toUint128();
    else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();
```