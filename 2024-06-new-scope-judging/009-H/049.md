Loud Rosewood Oyster

Medium

# Flash Loan Error: Reserve Balances Incorrectly Increased After Loan Repayment

## Summary
Reserves underlying balances are wrongly incremented by the flash loan amount plus premium, whenever a flash loan is taken.

## Vulnerability Detail
Let's assume that the current reserve underlying balance equals 1,000.

According to the current implementation: If a user, <PERSON>, takes a flash loan of 500 of the reserve assets, assuming no premium is charged. In [_handleFlashLoanRepayment](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L123) function, [updateInterestRates](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145-L182) is called, here the reserve liquidity and borrow rates are updated, and then the reserve underlying balance is incremented by the amount of flash loan taken, i.e.:
underlying balance = 1,000 + 500 => 1500

The reserve will hence appear to hold more tokens in the contract than it does after the flash loan.

## Impact
Due to flash loan actions, the underlying reserve asset balance will always greatly overstate the actual pool reserve balance.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L123

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145-L182
## Tool used

Manual Review

## Recommendation
Deduct the flash loan amount from the reserve underlying balance before making the transfer to the receiver address
