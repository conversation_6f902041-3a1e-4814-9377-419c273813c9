Clever Ebony Halibut

Medium

# Interest Rate are updated incorrectly in `FlashloanLogic`

## Summary
The function `_handleFlashLoanRepayment` in the FlashLoanLogic contract includes the full flash loan amount (`amountPlusPremium`) instead of just the premium when updating interest rates. This issue leads to an incorrect adjustment of the interest rates after a flash loan is repaid, leading to a potential 
## Vulnerability Detail
The problem exists on how the state is updated during the flashloan repayment  in the method `_handleFlashLoanRepayment()`
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
@>>    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```
the `updateInterestRates()` function expects to recieve the amount that have been added to the pool after the transaction in order to correctly update state varibles as the underlyingBalance and to correctly calculate the interestRate (having this knowledge is necessary to correctly calculate the poolUtilization ratio, which every interestRateModel relies On)

The problem however is that the amount passed as amountAdded is the flashloaned amount plus Premium. The Flashloand amount was already in the pool to begin with, adding this **amount twice will result in significant inflation in the available liquidity Calculation**

```solidity
  function updateInterestRates(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ReserveCache memory _cache,
    address _reserveAddress,
    uint256 _reserveFactor,
@>>    uint256 _liquidityAdded,
    uint256 _liquidityTaken,
    bytes32 _position,
    bytes memory _data
  ) internal {
    UpdateInterestRatesLocalVars memory vars;

    vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
@>>        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );

    _reserve.liquidityRate = vars.nextLiquidityRate.toUint128();
    _reserve.borrowRate = vars.nextBorrowRate.toUint128();

@>>    if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
    else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();

    emit PoolEventsLib.ReserveDataUpdated(
      _reserveAddress, vars.nextLiquidityRate, vars.nextBorrowRate, _cache.nextLiquidityIndex, _cache.nextBorrowIndex
    );
  }
```

## Impact
- UnderlyingBalance Accounting will be completely broken after each flashloan repayment
- Interest rate calculations will be completely inflated (more liquidity will be assumed), causing artificially lowered intererstRates 
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106C1-L123C4
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145C1-L182C4
## Tool used

Manual Review

## Recommendation
Consider only adding the totalPremium as the added amount to mitigate this issue
```diff
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
-    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
+    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(),_params.totalPremium, 0, '', '');
    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```