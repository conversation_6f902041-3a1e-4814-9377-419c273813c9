Smooth Carbon Narwhal

Medium

# The interest rate is incorrectly calculated during flash loans

## Summary
In the protocol, `depositors` can supply `assets` as `collateral` to the `pool` and earn `interest` from `borrowers`. 
`Borrowers`, in turn, can `borrow` `assets` and are required to pay `interest`. 
It's essential that `interest rates` are correctly calculated for both `debt` and `collateral`.
However, during `flash loan`, the `interest rates` are incorrectly calculated. 
Additionally, the `underlyingBalance` is tracked inaccurately.
## Vulnerability Detail
When `liquidity` changes (such as during `deposits`, `repayments`, `borrowing`, or `withdrawals`), the `interest rate` adjusts. 
```solidity
function updateInterestRates(
  DataTypes.ReserveData storage _reserve,
  DataTypes.ReserveSupplies storage totalSupplies,
  DataTypes.ReserveCache memory _cache,
  address _reserveAddress,
  uint256 _reserveFactor,
  uint256 _liquidityAdded,
  uint256 _liquidityTaken,
  bytes32 _position,
  bytes memory _data
) internal {
158:  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);
  
  (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
    .calculateInterestRates(
    _position,
    _data,
    DataTypes.CalculateInterestRatesParams({
165:      liquidityAdded: _liquidityAdded,
166:      liquidityTaken: _liquidityTaken,
      totalDebt: vars.totalDebt,
      reserveFactor: _reserveFactor,
      reserve: _reserveAddress
    })
  );

  _reserve.liquidityRate = vars.nextLiquidityRate.toUint128();
  _reserve.borrowRate = vars.nextBorrowRate.toUint128();

176:  if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
177:  else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();
  );
}
```
Specifically, in `line 158`, `totalDebt` represents the current `total debt` of the `asset reserve`.
When `liquidity` increases (e.g., `deposits` or `repayments`), the `_liquidityAdded` value is positive (`line 165`). 
Conversely, when `liquidity` decreases (e.g., `borrowing` or `withdrawals`), the `_liquidityTaken` value is positive (`line 166`). 
These values are used to calculate new `interest rates` for both `debt` and `collateral`.
In `lines 176` and `177`, the `underlyingBalance` is updated based on changes in `liquidity`.

In `line 79`, the requested amount is sent to the `flash loan receiver`, and at this point, the `interest rate` is not updated because the `assets` will be returned within the same transaction.
```solidity
function executeFlashLoanSimple(
  address _pool,
  DataTypes.ReserveData storage _reserve,
  DataTypes.ReserveSupplies storage _totalSupplies,
  DataTypes.FlashloanSimpleParams memory _params
) external {
  uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);

79:  IERC20(_params.asset).safeTransfer(_params.receiverAddress, _params.amount);
  
  require(
    receiver.executeOperation(_params.asset, _params.amount, totalPremium, msg.sender, _params.params),
    PoolErrorsLib.INVALID_FLASHLOAN_EXECUTOR_RETURN
  );
}
```
The `receiver` is required to `repay` both the `borrowed amount` and the `premium` in `line 111`. 
In `line 118`, the `interest rates` are updated for this `asset`.
```solidity
function _handleFlashLoanRepayment(
  DataTypes.ReserveData storage _reserve,
  DataTypes.ReserveSupplies storage _totalSupplies,
  DataTypes.FlashLoanRepaymentParams memory _params
) internal {
111:  uint256 amountPlusPremium = _params.amount + _params.totalPremium;
  DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
  _reserve.updateState(_params.reserveFactor, cache);
  _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

118:  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

  IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);
}
```
However, the issue lies in the fact that `_liquidityAdded` is calculated as `_params.amount + _params.totalPremium`. 
In reality, the actual increase in `assets` is only the `premium`, as the `_params.amount` is simply sent and returned by the `flash loan receiver`.
## Impact
`Interest rates` are a crucial component of the lending protocol. 
Additionally, due to this issue, the `underlyingBalance` can no longer be trusted.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L177
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L79
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L111-L118
## Tool used

Manual Review

## Recommendation
```solidity
function _handleFlashLoanRepayment(
  DataTypes.ReserveData storage _reserve,
  DataTypes.ReserveSupplies storage _totalSupplies,
  DataTypes.FlashLoanRepaymentParams memory _params
) internal {
  uint256 amountPlusPremium = _params.amount + _params.totalPremium;
  DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
  _reserve.updateState(_params.reserveFactor, cache);
  _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

-  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
+  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), _params.totalPremium, 0, '', '');

  IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);
}
```