Decent Walnut Squirrel

High

# The protocol updates interest rates wrongly when `flashLoan`.

## Summary
When `flashLoan` repayment, the protocol considers `amountPlusPremium` (= amount + premium), not only premium as added.   
This is wrong. In fact, `amount` is value repayed from receiver by `flashLoan`, not added to protocol.

## Vulnerability Detail
`FlashLoanLogic.sol#executeFlashLoanSimple()` which is called on `flashLoan` is as follows.
```solidity
  function executeFlashLoanSimple(
    address _pool,
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashloanSimpleParams memory _params
  ) external {
    // The usual action flow (cache -> updateState -> validation -> changeState -> updateRates)
    // is altered to (validation -> user payload -> cache -> updateState -> changeState -> updateRates) for flashloans.
    // This is done to protect against reentrance and rate manipulation within the user specified payload.

    ValidationLogic.validateFlashloanSimple(_reserve);

    IFlashLoanSimpleReceiver receiver = IFlashLoanSimpleReceiver(_params.receiverAddress);
    uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);
79  IERC20(_params.asset).safeTransfer(_params.receiverAddress, _params.amount);

    require(
      receiver.executeOperation(_params.asset, _params.amount, totalPremium, msg.sender, _params.params),
      PoolErrorsLib.INVALID_FLASHLOAN_EXECUTOR_RETURN
    );

86  _handleFlashLoanRepayment(
      _reserve,
      _totalSupplies,
      DataTypes.FlashLoanRepaymentParams({
        reserveFactor: _params.reserveFactor,
        pool: _pool,
        asset: _params.asset,
        receiverAddress: _params.receiverAddress,
        amount: _params.amount,
        totalPremium: totalPremium
      })
    );
  }
```
As we can see, on L79 it transfers `_params.amount` to the receiver.   
and `_handleFlashLoanRepayment()` function called on L86 is as follows.   
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

118 _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```
As we can see on L118, the protocol considers `amountPlusPremium` as added.   
This is wrong. In fact, only `_params.totalPremium` is added.

## Impact
When `flashLoan`, the protocol updates `interestRates` wrongly. So the protocol interestRates and indexes are broken by `flashLoan`.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118

## Tool used

Manual Review

## Recommendation
The `_handleFlashLoanRepayment()` function has to be modified as follows.
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

-   _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');
+   _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), _params.totalPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```