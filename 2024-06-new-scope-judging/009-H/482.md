Careful Fleece Pike

Medium

# A user uses flash loan will increase `ReserveSupplies.underlyingBalance` a wrong amount

### Summary

A user uses flash loan will increase `ReserveSupplies.underlyingBalance` a wrong amount. 

### Root Cause

In `_handleFlashLoanRepayment`, `_liquidityAdded`, which passed to `updateInterestRates`, is `amountPlusPremium`

```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

>>  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```

This will cause `ReserveSupplies.underlyingBalance` to increase by the amount of the flash loan.

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

A user uses flash loan.

### Impact

- `ReserveSupplies.underlyingBalance` will not reflect the correct value
- Since `ReserveSupplies.underlyingBalance` has `uint128` type, in case of a token with a high total supply, the pool which includes this token could be DoS on the `supply` and `repay` function on this token, because `ReserveSupplies.underlyingBalance` could be overflow.

### PoC

Run command: `forge test --match-path test/PoC/PoC.t.sol -vv`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {IPool} from 'contracts/interfaces/pool/IPool.sol';
import {MockFlashLoanSimpleReceiver} from 'contracts/mocks/MockSimpleFlashLoanReceiver.sol';
import {PoolSetup} from 'test/forge/core/pool/PoolSetup.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is PoolSetup {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  uint256 tokenAmount = 1 ether;

  function setUp() public {
    _setUpPool();

    _mintAndApprove(bob, tokenA, tokenAmount, address(pool));
    vm.prank(bob);
    pool.supplySimple(address(tokenA), bob, tokenAmount, 0);
  }

  function testPoC() public {
    bytes memory emptyParams;
    MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);

    console.log("underlyingBalance before flash loan: %e", pool.getTotalSupplyRaw(address(tokenA)).underlyingBalance);

    vm.startPrank(alice);
    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), tokenAmount, emptyParams);
    vm.stopPrank();

    console.log("underlyingBalance after flash loan: %e", pool.getTotalSupplyRaw(address(tokenA)).underlyingBalance);
  }
}
```

Logs:

```bash
  underlyingBalance before flash loan: 1e18
  underlyingBalance after flash loan: 2e18
```

### Mitigation

In case of a flash loan, `ReserveSupplies.underlyingBalance` should only be increased by `_params.totalPremium`.