Great <PERSON> Shetland

Medium

# `FlashLoanLogic::_handleFlashLoanRepayment` wrongly adds the whole amount + premium to the underlying supply balance

### Summary

When a flash loan is issued, a receiver receives X amount of assets, and at the end of the flash loan the receiver has to pay X + Y assets, where Y represents some premium. So, the new incoming assets to the pool are just Y, however, in `FlashLoanLogic::_handleFlashLoanRepayment`, `amountPlusPremium` (X + Y) is passed `_reserve.updateInterestRates` which adds that to the underlying balance, where in reality it should just be premium (Y).

This results in an inaccurate representation of the underlying balance of the pools.

### Root Cause

`amountPlusPremium` is being passed to `_reserve.updateInterestRates` which updated the underlying supply, [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118).

### Impact

Inaccurate representation of the total underlying balance of the pools.

### PoC

Add the following test in `zerolend-one/test/forge/core/pool/PoolFlashLoanTests.t.sol`.
```solidity
function testWrongTotalUnderlying() public {
  MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
  _generateFlashloanCondition();
  uint256 amount = tokenA.balanceOf(address(pool));

  // Increase flashloan premium
  poolFactory.setFlashloanPremium(2);

  // Underlying balance supply is the same as the balance of the pool
  DataTypes.ReserveSupplies memory supplies = pool.getTotalSupplyRaw(address(tokenA));
  assertEq(supplies.underlyingBalance, amount);

  // Flashloan
  pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), amount, '');

  // Underlying balance supply is more than double the balance of the pool
  supplies = pool.getTotalSupplyRaw(address(tokenA));
  assertGt(supplies.underlyingBalance, amount * 2);
}
```

### Mitigation

When repaying a flash loan make sure only the premium value is added to the underlying balance and not the whole `amountPlusPremium`.