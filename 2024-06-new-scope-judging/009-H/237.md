Dizzy <PERSON><PERSON><PERSON> Pig

High

# Lack of update of a reserve's liquidity when a flashloan is invoked will lead to wrong accounting and rate manipulation of a reserve

### Summary

When a flashloan is requested on Zerolend, before transferring the requested amount to the caller, the total liquidity of the underlying reserve ought to be updated with this requested amount. 

**However, the update seems to be missing in `FlashLoanLogic::executeFlashLoanSimple()` function, leaving the liquidity of the underlying reserve in a stale state.** Now, when the flashloan is repaid, the total liquidity of the underlying reserve becomes over-inflated in value, affecting liquidity rate calculations and wrong accounting in Zerolend.

Now, a malicious attacker can weaponise this vulnerability to manipulate the liquidity rates and borrow rates of the reserve during the flashloan execution, which might lead to possible draining of the pool's funds.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L77-L79


Before transferring the requested flashloaned amount to the receiver, the total liquidity of the underlying reserve ought to be decremented with the flashloaned amount. However, that update is missing in `FlashLoanLogic.sol::executeFlashLoanSimple()`  function as shown in the code snippet above.

### Internal pre-conditions

This is bound to happen whenever a flashloan is invoked on Zerolend.

### External pre-conditions

_No response_

### Attack Path

1. When a flashloan is requested, the total liquidity of the underlying reserve remains stale as demonstrated in the provided code snippets.

2. However, during repayment of the flashloaned amount, the liquidity of the underlying reserve is updated both with the requested flashloan amount + flash fee. This is done through the call to `reserve.updateInterestRates()`  in the `FlashLoanLogic.sol::_handleFlashLoanRepayment()` function. See code snippet below:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L118

#See the `reserve.updateInterestRates()` function updating the liquidity of the reserve upon receiving the flashloan repayment amount.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176-L177

3. Now, due to this, the total liquidity of the reserve will always report an over-inflated value, that will lead to wrong accounting in Zerolend. 

4. Now, during the flashloan execution, an attacker can use the staleness of the liquidity of the underlying reserves to manipulate the liquidity rate of the reserve, and accumulate higher interest rates on their supply, or the borrow rate of the reserve to be able to borrow more than the pool's allowable.

### Impact

1. Wrong accounting of total liquidity of a reserve

2. Possible rate manipulation using user specified payload during the flashloan request

### PoC

1. Since most of Zerolend's logic contracts are forked from AAVE v3-origin codebase, I am going to provide link to the AAVE codebase where AAVE updates the total liquidity of a reserve when a flashloan is requested. See AAVE codebase below:

https://github.com/aave-dao/aave-v3-origin/blob/6948864fc7e74b2b29fadfe2007998992060f84b/src/core/contracts/protocol/libraries/logic/FlashLoanLogic.sol#L99-L103
```solidity
      if (reservesData[params.assets[vars.i]].configuration.getIsVirtualAccActive()) {
        reservesData[params.assets[vars.i]].virtualUnderlyingBalance -= vars
          .currentAmount
          .toUint128();
      }
```


### Mitigation

The correct recommendation is to follow AAVE's by updating the total liquidity of the reserve when a flashloan is taken, before transferring the flashloaned amount to the caller.  Add the line marked as + in the below snippet to the FlashLoanLogic.sol library 

```solidity
    uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);
+   totalSupplies.underlyingBalance -= _params.amount.toUint128();
    IERC20(_params.asset).safeTransfer(_params.receiverAddress, _params.amount);
```

In addition, I would recommend Zerolend to even check if there's enough liquidity of a reserve before allowing the flashloan request to proceed and revert with a proper error if not, other than a transfer failed error.