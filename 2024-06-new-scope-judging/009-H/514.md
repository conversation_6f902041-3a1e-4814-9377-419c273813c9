Massive Glass Crane

High

# Pool.sol::FlashLoan:: amountPlusPremium is considered instead of totalPremium to calculate the new interest Rate

## Summary
In the function `_handleFlashLoanRepayment()` ,` nwe  nterestRate` is calculated by passing `amountPlusPremium` as `liquidityTaken` parameter for the `updateInterestRate`.

`amountPlusPremium` consists of  `_params.amount` and `_params.totalPremium` where `params.amount` was already presnt in the reserve and the user who executed flashLoan only added the `totalPremium` amount.

## Vulnerability Detail

When a flashLoan is executed `params.amount` is sent to the `caller` from the reserve without updating the state or `interestRate`.

Then the protocol will execute the tranaction where  `_params.amount` + `_params.totalPremium` is transferred from the `caller` to the pool address.

Since `_params.amount` was taken from the `reserve` and returned back to the `reserve` and only the `totalPremium` , that the user has to pay extra ,  is added to the reserve ,the new Interest rate should be calculated for only the `totalPremium`

but instead the total `amountPlusPremium` is considered for the new Interest Calculation which also affects the `totalSupply.underlyingbalance`.
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L111-L118

```solidity
  function _handleFlashLoanRepayment(
...
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

...
=>  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');


    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);


    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
    ```
## Impact
This will update the interest rate using the incorrect liquidity value.

## Code Snippet

## Tool used

Manual Review

## Recommendation

```solidity

=>  _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), _params.totalPremium, 0, '', '');
   
    ```