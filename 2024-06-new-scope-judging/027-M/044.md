Acrobatic Rainbow Grasshopper

High

# Users can increase their rewards and dilute rewards for other users

## Summary
Users can increase their rewards and dilute rewards for other users
## Vulnerability Detail
Users can supply and borrow tokens using the NFT position manager and they will be staked rewards based on the amount they have supplied/borrowed. In `NFTPositionManagerSetters:_supply()`, we have the following lines:
```solidity
pool.supply(params.asset, address(this), params.amount, params.tokenId, params.data);
uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
_handleSupplies(address(pool), params.asset, params.tokenId, balance);
```
We supply to the pool, then we get the balance of the user's position and call `_handleSupplies()`:
```solidity
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }

  function _handleDebt(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, true);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```
This allows users to do the following in order to increase their rewards and dilute rewards for other users:
1. Supply using the NFT position manager to a pool
2. The position will accrue interest
3. After interest has been accrued, supply again with an amount that will mint the position 1 share, that way the supply function in the pool does not revert
4. This function will return the position assets with accrued interest:
```solidity
uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
```
5. Now we increased our balance based on the interest we accrued
6. Thus, even though we supplied a minimal amount equal to 1 share, we increase the rewards we will be given and dilute the rewards for other users while users who don't abuse this trick will be left with their initial supply amount

Similar thing can happen with withdrawing where someone withdrawing might actually end up with a bigger balance than before withdrawing which is completely illogical and shouldn't be the case.
## Impact
Users can increase their rewards and dilute rewards for other users
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L44-L61
## Tool used

Manual Review

## Recommendation
You would have to not include the interest accrued upon supplying