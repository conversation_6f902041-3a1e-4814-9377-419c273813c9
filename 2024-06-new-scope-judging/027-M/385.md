Clever Ebony Halibut

Medium

# NFTRewardsDistributor tracks user's balance in terms of assets instead of shares leading to a loss of yield

## Summary
The current implementation of the `NFTRewardsDistributor` and `NFTPositionManagerSetters` contracts in the ZeroLend protocol leads to a loss of yield for non-active users. This issue arises from the lack of utilization of shares to track user's deposited assets in pools. Instead, the protocol tracks the underlying balance in assets, which does not account for the yield accrued over time (for debt and collateral deposits). As a consequence, when a user does not actively interact with their position, their rewards are calculated based on the initial deposited/borrowed amount rather than the current balance including accrued yield.
## Vulnerability Detail
The bugs persists in the whole `NFTPositionManager` in all the functions that update the user's balances (debt/supply). To showcase the bug will simply show how the `_supply` function updates the tracked  user balance in for the rewards.

https://github.com/A2-Security/zeroland/blob/0d7e24006744096574a53762d76dd7c5d3d0f7b6/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L44-L59

```solidity
  function _supply(AssetOperationParams memory params) internal nonReentrant {
    if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
    if (params.tokenId == 0) {
      // NOTE wtf?
      if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();
      params.tokenId = _nextId - 1;
    }

    IPool pool = IPool(_positions[params.tokenId].pool);

    IERC20(params.asset).forceApprove(address(pool), params.amount);
    pool.supply(params.asset, address(this), params.amount, params.tokenId, params.data);

    // update incentives
@>    uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
@>   _handleSupplies(address(pool), params.asset, params.tokenId, balance);
```
As we can see the _supply function queries the position balance of underlying assets and updates the state using that value.
This is how the pool.getBalance function is implemented : 
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L57C1-L60C4
```solidity
  function getBalance(address asset, address who, uint256 index) external view returns (uint256 balance) {
    bytes32 positionId = who.getPositionId(index);
@>>    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
  }
```
as we can see it converts the share balance of the position to its corresponding asset balance by using the liquidity index of the reserve.

this balance of assets will be set as the new balance of the position in the _handleSupplies function : 
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165C1-L183C4
```solidity
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```

### Proof of Concept
Alice deposits 1000 USDC in the pool using the NFTPositionManager contract.
Let's assume a 10% APY on the deposit, so in one year Alice will have 1100 USDC after a year.
Now Bob deposits 1000 USDC in the pool.

With the current implementation alice and bob will have the exact same balance in  NFTRewardsDistributor because the balance they have in the pool is the same(`_handleSupplies()` will be called with the same value for both). Even though Alice has 1100 USDC in the pool and Bob only has 1000 USDC, both of them will have the same balance in the NFTRewardsDistributor and get rewarded the same amount of rewards. (same share of the rewards)


## Impact
The primary impact of this bug is the incorrect allocation of rewards to users who do not frequently interact with their positions. Specifically, users who hold positions over extended periods without active engagement receive rewards based on their initial deposit, not accounting for any growth. This could potentially result in users receiving fewer rewards than they are entitled to, thus discouraging long-term holdings and negatively affecting user experience and trust in the protocol.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L57C1-L58C75

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L99C1-L100C75
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L121C1-L132C82

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L57C1-L60C4

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165C1-L183C4

## Tool used

Manual Review

## Recommendation



To mitigate this issue, it is recommended to modify the tracking mechanism to use shares instead of raw asset balances.
This can be implemented by calling the `pool.supplyShares()` and `pool.debtShares()` instead of `pool.getBalance()`, when querying the balance to be passed  to  the `_handleSupplies()` function.
