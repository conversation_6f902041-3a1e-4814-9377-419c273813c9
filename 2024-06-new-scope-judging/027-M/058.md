Smooth Zinc Puppy

Medium

# Frequent Updaters will Gain Disproportionate Rewards Affecting Passive Users

### Summary

The reward calculation based on current balance in NFTPositionManager will cause an unfair distribution of rewards for passive users as frequent updaters will make small deposits to get accrued interest included in reward share calculations.

### Root Cause

The choice to calculate rewards based on the current balance in NFTRewardsDistributor without considering updates due to interest in underlying pool is a problem as it allows users to increase their reward share through frequent small deposits compared to passive users.
1. https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L98
```solidity
  function earned(uint256 tokenId, bytes32 _assetHash) public view returns (uint256) {
    return _balances[tokenId][_assetHash].mul(rewardPerToken(_assetHash).sub(userRewardPerTokenPaid[tokenId][_assetHash])).div(1e18).add(
      rewards[tokenId][_assetHash]
    );
  }
```
The rewards are calculated solely based on the balances state maintained by NFTPositionManager in NFTRewardsDistributor.

2. The updates to this balance are done only when doing supply/withraw like operations where pool.getBalance is triggered https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L57
```solidity
    uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
```
3. pool.getBalance returns the balance including interest at   function getBalance(address asset, address who, uint256 index) external view returns (uint256 balance) {
```solidity
function getBalance(address asset, address who, uint256 index) external view returns (uint256 balance) {
    bytes32 positionId = who.getPositionId(index);
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
  }

```
### Internal pre-conditions

1. Reward rate needs to be set to a non-zero value
2. Interest rate in the Pool contract needs to be greater than 0%

### External pre-conditions

_No response_

### Attack Path

1. Assume the following parameters:
   - Pool interest rate: 5% APY
   - Reward rate: 10% of deposited value per year in ZERO tokens
   - Initial deposit: 1,000,000 USDC
   - Time frame: 1 year (365 days)

2. Both User A and User B deposit 1,000,000 USDC into the pool
3. User B sets up an automated system to make tiny deposits of 1 USDC daily, while User A remains passive
4. Each tiny deposit by USer B triggers a balance update and reward recalculation in the NFTPositionManager
5. Over the course of a year (365 days):
   - User A's balance in Pool: 1,000,000 USDC initial + 50,000 USDC interest = 1,050,000 USDC
   - User B's balance in Pool: Also 1,050,000 USDC (exactly the same as User A)
6. However, reward calculation in NFTPositionManager differs:
   - User A's reward based on: 1,000,000 USDC (initial deposit, never updated)
   - User B's reward based on: Gradually increasing balance from 1,000,000 to 1,050,000 USDC (updated daily)
7. Approximate reward calculation (assuming constant reward rate, in reality user B rewards increase will come at expense of User A):
   - User A receives: 100,000 ZERO tokens (10% of 1,000,000)
   - User B receives: approximately 102,500 ZERO tokens (10% of average balance ~1,025,000)
8. Attacker gains an extra 2,500 ZERO tokens (2.5% more) compared to the passive user

### Impact

Passive users suffer some loss potential rewards over a year. The frequent updaters gain in rewards at the expense of passive users.

### PoC

_No response_

### Mitigation

_No response_