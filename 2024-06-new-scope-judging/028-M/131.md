Powerful Licorice Elephant

Medium

# Nft position owner can’t disable an asset from ReserveAsCollateral.

## Summary
a position’s supply collateral address can be disabled/enabled from useAsCollateral by calling the function setUserUseReserveAsCollateral(contract Pool).Nft tokenid owner can’t independently disable/enable useAsCollateral by calling the function setUserUseReserveAsCollateral(contract Pool). The reason is nft tokenid’s position is created by NFTPositionManager address and tokenid. There is no implementation to disable/enable useAsCollateral from an asset in NFTPositionManager.So  setUserUseReserveAsCollateral must be implemented in NFTPositionManager.


## root cause
 there is no implementation to disable/enable useAsCollateral from an asset in NFTPositionManager.


## Vulnerability Detail
 1.  Let’s assume <PERSON> mints a nft in  NFTPositionManager and the tokenid of the nft is 1.
  
2. Now <PERSON> supplies two assets(assume asset1 and asset2) in the nft position(nft’s pool).Both asset1 and asset2 will be set as useAsCollateral.
 
3.  Now <PERSON> wants to disable useAsCollateral from asset1. But there is no implementation to disable/enable useAsCollateral from an asset in NFTPositionManager.
 
4. Nft tokenid owner can’t independently disable/enable useAsCollateral by calling the function setUserUseReserveAsCollateral(contract Pool). The reason is nft tokenid’s position is created by NFTPositionManager address and tokeind. So  setUserUseReserveAsCollateral must be implemented in NFTPositionManager.

## Impact
there is no flexibility/choice to disable/enable useAsCollateral for nft tokenid owner
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L175
## Tool used

Manual Review

## Recommendation
Implement setUserUseReserveAsCollateral in NFTPositionManager so that one can disable/enable useAsCollateral from an asset.




