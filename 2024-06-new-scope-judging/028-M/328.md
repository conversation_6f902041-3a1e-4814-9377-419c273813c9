Soft Rose Cod

Medium

# Users are not able to call ```Pool.sol#setUserUseReserveAsCollateral``` on their NFT positions

### Summary

In order to call pool functionality on behalf of a position, a user is required to go through the ```NFTPositionManager.sol``` contract. However, there is no built in functionality inside the contract that allows a user to call the ```Pool.sol#setUserUseReserveAsCollateral()``` function for their NFT position.

### Root Cause

The ```NFTPositionManager.sol``` contract is missing functionality that allows a user to call the ```Pool.sol#setUserUseReserveAsCollateral()``` function on behalf of their NFT position.

The ```_setUserUseReserveAsCollateral``` function generates a position hash based on the msg.sender by calling ```msg.sender.getPositionId(index)```. 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L204-L222
This means the position hash is based on the msg.sender. In order to call this function for a NFT position, the msg.sender would have to be ```address(NFTPositionManager)```. However the NFTPositionManager does not implement functionality to call this.

### Internal pre-conditions

None

### External pre-conditions

None

### Attack Path

1. User decides they want to enable or disable a currency as collateral
2. They despair because they are unable to

### Impact

An important protocol feature is inaccessible for users.

### PoC

_No response_

### Mitigation

Implement a function inside ```NFTPositionManager.sol``` that allows users to update their positions collateral.