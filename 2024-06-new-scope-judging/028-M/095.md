Steep Aqua Yak

Medium

# Users can not set status for `useReserveAsCollateral`

## Summary
The contract `NFTPositionManager` does not have logic to call to `Pool.setUserUseReserveAsCollateral()`

## Vulnerability Detail
The function [`Pool#setUserUseReserveAsCollateral`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L175-L177) only allows [caller to update his positions' `useReserveAsCollateral` status](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L204-L223). 
However, the contract `NFTPositionManager` does not have logic that calls to the function `Pool#setUserUseReserveAsCollateral`

## Impact
User can not set `useReserveAsCollateral` status for his positions

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L204-L223

## Tool used

Manual Review

## Recommendation
Add logic that calls to the function `Pool#setUserUseReserveAsCollateral`