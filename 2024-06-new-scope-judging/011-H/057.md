Smooth Zinc Puppy

Medium

# Malicious users will exploit NFT based reward distribution flaws, affecting legitimate users and the protocol

### Summary

The lack of synchronization between the NFTPositionManager and Pool contracts will cause an unfair distribution of rewards, impacting legitimate users and the protocol, as malicious actors will create easily liquidatable positions to continue receiving unearned rewards.

### Root Cause

The choice to maintain separate balance tracking in the NFTPositionManager without a mechanism to update on liquidations is a problem, as it allows for discrepancies between actual and recorded balances. The balance gets updated on suppy/withdraw/borrow/repay  in _handleSupplies and _handleDebt methods.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L171 and https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L181

_balances getting updated in _handleSupplies.

```solidity
function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```

The rewards are based on these balances:https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L98-L101
```solidity
function earned(uint256 tokenId, bytes32 _assetHash) public view returns (uint256) {
    return _balances[tokenId][_assetHash].mul(rewardPerToken(_assetHash).sub(userRewardPerTokenPaid[tokenId][_assetHash])).div(1e18).add(
      rewards[tokenId][_assetHash]
    );
  }
  ```
  
  But there are no ways to update these balances on liquidation of positions.

### Internal pre-conditions

1. User needs to mint an NFT position and supply assets to set balance > 0 for a given tokenId and assetHash
2. User's position needs to become undercollateralized to be eligible for liquidation

### External pre-conditions

1. Asset price needs to decrease sufficiently to make the user's position undercollateralized

### Attack Path

1. Attacker mints an NFT position through NFTPositionManager
2. Attacker supplies a small amount of collateral and borrows the maximum allowed to create a position which has rewards
3. Attacker waits for price to make their position liquidatable
4. A third party or attacker itself liquidates the attacker's position directly through the Pool contract
5. NFTPositionManager's balances remain unchanged, still showing the pre-liquidation amounts
6. Attacker continues to call getReward() to receive rewards based on the outdated balances

### Impact

The protocol suffers a loss of reward tokens distributed unfairly. Legitimate users receive fewer rewards than they should, as the total reward pool is diluted by attackers claiming unearned rewards. The attacker gains these unearned rewards without maintaining the expected position.


### PoC

_No response_

### Mitigation

Implement a callback mechanism in the Pool contract to notify the NFTPositionManager of liquidations.
