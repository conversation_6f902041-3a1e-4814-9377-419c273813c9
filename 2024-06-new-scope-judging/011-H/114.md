Careful Fleece Pike

High

# A NFT's `_balances` is not updated during liquidation will cause `_balances` and `_totalSupply` to report a stale value

### Summary

A NFT's `_balances` is not updated during liquidation will cause `_balances` and `_totalSupply` to report a stale value.

### Root Cause

In the `NFTPositionManager` contract, there are storage variables:
- `_balances`: stores the total supply and total debt of a NFT for each asset
- `_totalSupply`: stores the total supply and total debt for each asset

These variables are updated on supply, borrow, withdraw, and repay to reflect the underlying supply and debt

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L58

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L79

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L100

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L132

During the liquidation of a position, its debt will decrease, its supply will increase 

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L174-L176

but the debt and supply of the position are not updated in the `NFTPositionManager` contract, which will cause `_balances` and `_totalSupply` to report the stale value.

### Internal pre-conditions

A NFT position is liquidated.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The storage variables `_balances` and `_totalSupply` will report the stale value, which leads to the rewards in the `NFTPositionManager` contract to be wrongly distributed.

The `REWARDS_ALLOCATOR_ROLE` can add rewards for an `_assetHash`, which identified by a tuple of `(address pool, address asset, bool isDebt)`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125-L149

```solidity
  function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
    rewardsToken.transferFrom(msg.sender, address(this), reward);

>>  bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }

    // Ensure the provided reward amount is not more than the balance in the contract.
    // This keeps the reward rate in the right range, preventing overflows due to
    // very high values of rewardRate in the earned and rewardsPerToken functions;
    // Reward + leftover must be less than 2^256 / 10^18 to avoid overflow.
    uint256 balance = rewardsToken.balanceOf(address(this));
    require(rewardRate[_assetHash] <= balance.div(rewardsDuration), 'Provided reward too high');

    lastUpdateTime[_assetHash] = block.timestamp;
    periodFinish[_assetHash] = block.timestamp.add(rewardsDuration);
    emit RewardAdded(_assetHash, reward);
  }
```

Because of the stale `_balances`:
- An honest user will lose out their rewards for the supply asset that is increased during liquidation but not reflected in `_balances`
- An attacker will mint a NFT and borrow to make their position liquidatable, and then liquidate the position themselves. The attacker will benefit from the rewards for their debt in the `NFTPositionManager` contract without having a real debt in their position. They can repeat this process many times to create more phantom debt to skew the rewards toward them.

### PoC

_No response_

### Mitigation

1. In `NFTPositionManager`, add an external function to update `_balances` and `_totalSupply` manually, this function:
   - Take in three input parameters `address pool, address asset, uint256 tokenId`.
   - Check if `pool` is registered in the `PoolFactory`.
   - Read the position's supply and debt from the `pool`.
   - Call to `_handleSupplies` and `_handleDebt` to update `_balances` and `_totalSupply`.
2. In `Pool`, the liquidation function should take `address user, uint256 index` as input parameters instead of `bytes32 pos`.
3. At the end of the liquidation, if the `user` address is the `NFTPositionManager` then call to the function in step 1 to update `_balances` and `_totalSupply`.