Innocent Chartreuse Parrot

Medium

# Violators will continue to earn rewards on their outdated debt balance post liquidation

## Summary
Violators will continue to earn rewards on their outdated debt balance post liquidation

## Vulnerability Detail
A user's "staked" debt is adjusted (incremented/decremented) in the `_handleDebt` internal function:

[NFTRewardsDistributor::_handleDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L175-L182)

```solidity
  function _handleDebt(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, true);
    uint256 _currentBalance = _balances[tokenId][_assetHash];


    _updateReward(tokenId, _assetHash);


    _balances[tokenId][_assetHash] = balance; // @audit: user's "staked" debt balance adjusted
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
```

This internal function is currently only accessible via [borrows](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L79) and [repayments](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L132) via the `NFTPositionManager`.

However, a user's debt is also adjusted during liquidation, but the `_handleDebt` logic is [not triggered during liquidations](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247). Therefore, a liquidation will result in a user's debt balance decreasing but their "staked" debt balance will remain the same.

## Proof of Concept
Place the following test inside `NFTPositionManagerTest.t.sol` and run with `forge test --mc NFTPostionManagerTest --mt testStakedDebtBalanceStaysSamePostLiquidation`:

```solidity
    function testStakedDebtBalanceStaysSamePostLiquidation() public {
        // alice supplies tokenA
        testShouldSupplyAlice();

        // bob supplies tokenB to pool 
        _mintAndApprove(bob, tokenB, 100 ether, address(pool)); 

        vm.startPrank(bob);
        pool.supplySimple(address(tokenB), bob, 50 ether, 0); 
        vm.stopPrank();

        // alice borrows tokenB
        uint256 borrowAmount = 10 ether;
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory params =
          INFTPositionManager.AssetOperationParams(address(tokenB), alice, borrowAmount, 1, data);

        vm.startPrank(alice);
        nftPositionManager.borrow(params);
        vm.stopPrank();

        bytes32 assetHash = nftPositionManager.assetHash(address(pool), address(tokenB), true);
        uint256 startDebtBalInPool = pool.getDebt(address(tokenB), address(nftPositionManager), 1);
        uint256 startDebtBalInRewards = nftPositionManager.balanceOfByAssetHash(1, assetHash);

        assertEq(startDebtBalInPool, startDebtBalInRewards);

        // position becomes liquitable (can be via interest accrural or external price event)
        oracleA.updateAnswer(5e3);

        // alice is liquidated and debt balance decreases in pool
        bytes32 pos = keccak256(abi.encodePacked(address(nftPositionManager), 'index', uint256(1)));
        vm.prank(bob);
        pool.liquidateSimple(address(tokenA), address(tokenB), pos, 1 ether);

        uint256 endDebtBalInPool = pool.getDebt(address(tokenB), address(nftPositionManager), 1);

        assertLt(endDebtBalInPool, startDebtBalInPool);

        // alice's staked debt balance did not decrease in the NFTPositionManager contract
        uint256 endDebtBalInRewards = nftPositionManager.balanceOfByAssetHash(1, assetHash);
        assertEq(startDebtBalInRewards, endDebtBalInRewards);
    }
```

## Impact
After liquidations, the violator will continue to earn rewards based on their outdated "staked" debt balance. 

Note that in the case where only part of the violator's debt is repaid during liquidation, a good samaritan is able to re-adjust the violator's "staked" debt balance by repaying some amount of the violator's remaining debt via the `NFTPositionManager::repay` function. This will trigger a call to the `_handleDebt` function and thus correctly re-adjust their "staked" balance. However, the violator will continue to earn rewards on their outdated debt balance until such an action is taken.

In the case where the violator's entire debt balance is repaid during liquidation, there will be no way to re-adjust their "staked" debt balance since the only way to trigger the `_handleDebt` function would be by performing a null repayment (since the violator has no more debt), but a repayment value of `0` is [not allowed](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L185-L186).

In both cases, the violator will be earning more rewards than they are supposed to and thus diluting other users' rewards. However, in the second case the violator will continue to indefinitely earn rewards for any live reward durations, while in the first case the violator will earn outsized rewards until someone recognizes this and figures out the proper remediation (we can not guarantee this will occur instantly).

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247

## Tool used

Manual Review

## Recommendation
I would recommend exposing a permissioned external function in the `NFTPositionManager` that solely calls into the `_handleDebt` function, and is only callable by a pool contract. This function can then be called during liquidations to properly update the violator's "staked" debt balance. 

However, it should be noted that debt positions can also occur directly via the `Pool` contract and thus not earn rewards (which are tracked in the `NFTPositionManager` contract). Therefore, there should also be a method introduced that allows the pool to determine if the debt position originated from the `NFTPositionManager` contract. If so, then it can call into the new external function to adjust the violator's "staked" debt balance during liquidations.