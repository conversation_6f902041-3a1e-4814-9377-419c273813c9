Blunt Lime Shark

High

# Persistent Reward Accrual in NFTRewardsDistributor  After Liquidation due to incorrect balance tracking

### Summary

The `NFTRewardsDistributor.sol` contract contains a critical vulnerability in its reward mechanism. The contract fails to update the balance tracking when a user's position is partially or completely liquidated. This oversight allows users to continue earning rewards on assets they no longer possess, leading to an unfair distribution of rewards and potential economic exploitation of the protocol.
The vulnerability stems from the absence of a mechanism to adjust the _balances mapping in the NFTRewardsDistributor contract when liquidation occurs. The _balances mapping is crucial for calculating rewards, as seen in the earned function:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L98-L102

However, the contract lacks functionality to update these balances when liquidation occurs. The _handleSupplies and _handleDebt functions are designed to update balances, but they are not called during the liquidation process:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165-L184



### Root Cause

The root cause of this vulnerability lies in the incomplete integration between the liquidation mechanism and the reward distribution system. Specifically:
In NFTRewardsDistributor.sol, the choice to update balances only through _handleSupplies and _handleDebt functions is a mistake, as it fails to account for balance changes that occur during liquidation events as they don't get called at the time of liquidation. This design oversight results in a disconnect between the actual user positions and the balances used for reward calculations.

### Internal pre-conditions

None

### External pre-conditions

None

### Attack Path

1.User A supplies 100 tokens to a pool and receives a corresponding NFT position.
2.User A's position is tracked in _balances, and they start accruing rewards.
3.Due to market fluctuations, User A's position becomes undercollateralized and is fully liquidated.
4.The liquidation process occurs in the pool contract but does not trigger any balance updates in the NFTRewardsDistributor contract.
5.User A's _balances in the NFTRewardsDistributor contract remains unchanged.
6.User A continues to accrue rewards based on their pre-liquidation balance, despite no longer having any assets in the pool.

### Impact

This vulnerability can have severe consequences for the protocol:

Unfair Reward Distribution: Users whose positions have been liquidated can continue to accrue rewards, diluting the rewards pool for legitimate users who still have active positions.
Economic Exploitation: Malicious users could intentionally allow their positions to be liquidated while still benefiting from ongoing reward accrual, effectively earning rewards without risk.
Inflation of Rewards: The total supply used for reward calculations may become inflated, as it includes balances from liquidated positions, potentially leading to higher reward rates than intended.
Protocol Insolvency: In extreme cases, if a significant portion of liquidated positions continue to accrue rewards, the protocol may not have sufficient funds to pay out all claimed rewards.
Reduced Protocol Efficiency: The discrepancy between actual and tracked balances can lead to suboptimal capital allocation and inefficient use of the reward pool.

### PoC

Not required.

### Mitigation

_No response_