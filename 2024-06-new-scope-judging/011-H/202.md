Joyous Cedar Tortoise

High

# The owner of an NFT position that got liquidated will continue to earn rewards based on the amount supplied and borrowed pre-liquidation

### Summary

The [`NFTRewardsDistributor`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L35) distributes rewards to users proportional to the amount they supplied/borrowed.

Whenever a user supplies/borrows/withdraws/repays the protocol correctly updates their balance by calling `_handleDebt` or `_handleSupplies`.

If a user’s position gets liquidated, their collateral and debt amounts will reduce.

The issue is that when a user is liquidated their balance is never updated in `NFTPositionManager`, so they will continue to earn rewards as if they were never liquidated.

### Root Cause

When a user is liquidated their balance is never updated in NFTPositionManager, so they will continue to earn rewards as if they were never liquidated

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Liquidated user will unfairly earn the same rewards as healthy users, this is equivalent to the liquidated user stealing rewards from the healthy users

### PoC

_No response_

### Mitigation

_No response_