Acrobatic Rainbow Grasshopper

High

# Users can artificially increase their rewards

## Summary
Users can artificially increase their rewards
## Vulnerability Detail
Upon supplying or borrowing using the NFT position manager, users can be staked rewards based on the amount they have supplied or borrowed. This allows users to do 1 of those 2 things:
1. Supply tokens, then borrow from themselves and continuously repeat that
2. <PERSON>rrow tokens, then supply those tokens and borrow again, rinse and repeat

Both of those actions will allow them to have a huge amount of rewards to be distributed to them due to those functions called upon supplying and borrowing:
```solidity
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }

  function _handleDebt(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, true);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```
## Impact
Users can artificially increase their rewards
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L24
## Tool used

Manual Review

## Recommendation
Fix is not trivial