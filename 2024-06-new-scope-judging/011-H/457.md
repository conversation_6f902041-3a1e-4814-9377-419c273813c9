Curly <PERSON>ard

High

# Attacker can get most of the borrowing reward in NFTPositionManager without actual holding debts

### Summary

The `NFTPositionManager` has extra rewarding design for both supplier and borrower. However, the user can repay the debt directly at pool which leave `NFTPositionManager` keep counting the reward for such debt. This would allow attackers to get most of the borrowing reward without actual holding the debts.

### Root Cause

The root cause is the inability of `NFTPositionManager` to keep tracking of the status of user's debt. Since the [repay function](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L115) of the pool is permissionless, user can repay the debt without calling `NFTPositionManager` contract causing the state out of date.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Attacker use flashloan and call `NFTPositionManager.mint()` & `NFTPositionManager.supply()`
2. Call `NFTPositionManager.borrow()` to create debt
3. Call `Pool.reapy()` to repay the debt
4. Call `NFTPositionManager.withdraw()` and payback flashloan

### Impact

Since the user can repay debt immediately at the time borrowed, attacker can leverage flashloan to have huge position and to create huge debt. This would allow the attacker to have majority debt balance of such `_assetHash` and thus can have most of the borrowing reward given by the `NFTPositionManager`.

### PoC

_No response_

### Mitigation

_No response_