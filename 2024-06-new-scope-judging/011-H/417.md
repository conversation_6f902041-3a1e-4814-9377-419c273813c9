Careful Fleece Pike

High

# In `NFTPositionManager`, `_totalSupply` and `_balances` will be stale when `borrowIndex` and `liquidityIndex` are increased

### Summary

In `NFTPositionManager`, `_totalSupply` and `_balances` will be stale when `borrowIndex` and `liquidityIndex` are increased will cause the rewards to be wrongly distributed.

### Root Cause

In the `NFTPositionManager` contract, there are storage variables:
- `_balances`: stores the total supply and total debt of a NFT for each asset
- `_totalSupply`: stores the total supply and total debt for each asset

These variables are updated on supply, borrow, withdraw, and repay to reflect the underlying supply and debt

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L58

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L79

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L100

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L132

Because `_balances` and `_totalSupply` store the value in asset, these value will not reflect the correct amount of underlying supply and debt when `borrowIndex` and `liquidityIndex` increased.

### Internal pre-conditions

The `borrowIndex` or `liquidityIndex` of a reserve is increased.

### External pre-conditions

_No response_

### Attack Path

When the `borrowIndex` or `liquidityIndex` of a reserve is increased, the value of `_totalSupply` and `_balances` will not reflect the correct amount of underlying supply and debt.

### Impact

The storage variables `_balances` and `_totalSupply` will report the stale value, which leads to the rewards in the `NFTPositionManager` contract to be wrongly distributed.

The `REWARDS_ALLOCATOR_ROLE` can add rewards for an `_assetHash`, which identified by a tuple of `(address pool, address asset, bool isDebt)`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125-L149

```solidity
  function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
    rewardsToken.transferFrom(msg.sender, address(this), reward);

>>  bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }

    // Ensure the provided reward amount is not more than the balance in the contract.
    // This keeps the reward rate in the right range, preventing overflows due to
    // very high values of rewardRate in the earned and rewardsPerToken functions;
    // Reward + leftover must be less than 2^256 / 10^18 to avoid overflow.
    uint256 balance = rewardsToken.balanceOf(address(this));
    require(rewardRate[_assetHash] <= balance.div(rewardsDuration), 'Provided reward too high');

    lastUpdateTime[_assetHash] = block.timestamp;
    periodFinish[_assetHash] = block.timestamp.add(rewardsDuration);
    emit RewardAdded(_assetHash, reward);
  }
```

Since the balance of a NFT will be updated to the correct value when interacting with the `NFTPositionManager`, a user who interacts with the `NFTPositionManager` more frequent will have more rewards than a user who does not interact at all, assuming both have the same amount of debt and amout of supply.

### PoC

Add a `getNFTBalances` in the `NFTPositionManagerGetters` contract for debugging

```diff
abstract contract NFTPositionManagerGetters is MulticallUpgradeable, NFTPositionManagerStorage {
  /// @inheritdoc INFTPositionManager
  function positions(uint256 tokenId) external view returns (Position memory) {
    return _positions[tokenId];
  }

+ function getNFTBalances(uint256 tokenId, bytes32 assetHash) external view returns (uint256) {
+   return _balances[tokenId][assetHash];
+ }
}
```

Due to a bug in `PositionBalanceConfiguration#getSupplyBalance` that we submitted in a different issue, fix the `getSupplyBalance` function before running the PoC

`core/pool/configuration/PositionBalanceConfiguration.sol`

```diff
library PositionBalanceConfiguration {
  function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index);
  }
}
```

Run command: `forge test --match-path test/PoC/PoC.t.sol -vv`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {console} from 'lib/forge-std/src/Test.sol';
import {DeployNFTPositionManager} from 'test/forge/core/positions/DeployNFTPositionManager.t.sol';


contract PoC is DeployNFTPositionManager {
    address alice = makeAddr('alice');
    address bob = makeAddr('bob');
    address cindy = makeAddr('cindy');

    uint256 tokenAmount = 1 ether;

    function setUp() public {
        _setUpPool();
        _setup();

        _mintAndApprove(alice, tokenA, tokenAmount, address(nftPositionManager));
        _mintAndApprove(bob, tokenA, tokenAmount + 1, address(nftPositionManager));

        _mintAndApprove(cindy, tokenA, tokenAmount, address(pool));
        _mintAndApprove(cindy, tokenB, tokenAmount, address(pool));
    }

    function testPoC() public {
        bytes32 assetHash = keccak256(abi.encode(address(pool), address(tokenA), false));
        uint256 aliceTokenId = 1;
        uint256 bobTokenId = 2;
        DataTypes.ExtraData memory dummyData = DataTypes.ExtraData(bytes(''), bytes(''));

        {
            vm.startPrank(alice);
            nftPositionManager.mint(address(pool));
            INFTPositionManager.AssetOperationParams memory params =
            INFTPositionManager.AssetOperationParams(address(tokenA), address(0), tokenAmount, aliceTokenId, dummyData);

            nftPositionManager.supply(params);
            vm.stopPrank();
        }

        {
            vm.startPrank(bob);
            nftPositionManager.mint(address(pool));
            INFTPositionManager.AssetOperationParams memory params =
            INFTPositionManager.AssetOperationParams(address(tokenA), address(0), tokenAmount, bobTokenId, dummyData);

            nftPositionManager.supply(params);
            vm.stopPrank();
        }

        {
            vm.startPrank(cindy);
            pool.supplySimple(address(tokenB), cindy, tokenAmount, 0);
            pool.borrowSimple(address(tokenA), cindy, tokenAmount, 0);
            vm.stopPrank();
        }

        skip(100 days);

        pool.forceUpdateReserves();
        console.log("liquidityIndex: %e", pool.getReserveData(address(tokenA)).liquidityIndex);

        console.log("Alice's NFT balance before Bob updates: %e", nftPositionManager.getNFTBalances(aliceTokenId, assetHash));
        console.log("Bob's NFT balance before Bob updates: %e", nftPositionManager.getNFTBalances(bobTokenId, assetHash));

        {
            INFTPositionManager.AssetOperationParams memory params =
            INFTPositionManager.AssetOperationParams(address(tokenA), address(0), 1, bobTokenId, dummyData);

            vm.prank(bob);
            nftPositionManager.supply(params);
        }

        console.log("Alice's NFT balance after Bob updates: %e", nftPositionManager.getNFTBalances(aliceTokenId, assetHash));
        console.log("Bob's NFT balance after Bob updates: %e", nftPositionManager.getNFTBalances(bobTokenId, assetHash));
    }
}
```

Logs:

```bash
  liquidityIndex: 1.011915223571982424399069527e27
  Alice's NFT balance before Bob updates: 1e18
  Bob's NFT balance before Bob updates: 1e18
  Alice's NFT balance after Bob updates: 1e18
  Bob's NFT balance after Bob updates: 1.011915223571982425e18
```

Bob's balance is only updated to the correct value after he interacts with the `NftPositionManager` contract.

### Mitigation

Store the supply and debt in `_totalSupply` and `_balances` in shares.