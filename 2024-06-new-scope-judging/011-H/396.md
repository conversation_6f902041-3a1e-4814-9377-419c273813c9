Clever Ebony Halibut

High

# Liquidated Positions Keep Receiving Rewards Forever in NFTPositionManager

## Summary

The `NFTPositionManager` contract tracks user balances for supplying, borrowing, repaying, and withdrawing assets to distribute rewards based on their position balances. However, when a user's position is liquidated, the `NFTPositionManager` is not notified about the balance decrease. As a result, users with liquidated positions continue to receive rewards based on their previous balances, even though they no longer hold any actual collateral or debt in the system. This leads to incorrect reward distribution and essentially stealing yield from other users.

## Vulnerability Detail

- The `NFTPositionManager` contract uses the `NFTPositionManagerSetters.sol` and `NFTRewardsDistributor.sol` contracts to track user balances and distribute rewards accordingly. When a user interacts with the `NFTPositionManager` to supply, borrow, repay, or withdraw assets, the `_handleSupplies` and `_handleDebt` functions in [`NFTRewardsDistributor.sol`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol) are called to update the user's balance which is crucial to accurately calculate rewards.

```js
// NFTPositionManagerSetters.sol
function _supply(AssetOperationParams memory params) internal {
// ...
_handleSupplies(address(pool), params.asset, params.tokenId, balance);
// ...
}

// NFTRewardsDistributor.sol
function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
// ...
_balances[tokenId][_assetHash] = balance;
_totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
// ...
}
```

- However, when a user's position is liquidated, the `NFTPositionManager` is not informed about the balance decrease. The liquidation process is handled by the `LiquidationLogic.sol` contract, so liquidator directly interacts with the `Pool` contract to liquidate the position. As a result, the `NFTPositionManager` is unaware of the liquidation which reduce both debt and supply balance of the position ,and continues to track the user's previous balances indefinitely.
- in this case the owners of those positions will continue receiving rewards forever and they just need to keep claiming rewards each time, without any actual balance.

**_Example scenario:_**

1. User A borrows 2000 USDC using 1 ETH as collateral.
2. User A's position is liquidated due to market fluctuations. and now he have 0 debt and 0 collateral.
3. The `NFTPositionManager` still think that User A has 1 ETH supplied and 2000 USDC borrowed.
4. User A will keep accumulating reward and claiming them forever based on 1ETH supply , and 2000usdc debt

## Impact

The inaccurate reward distribution for liquidated positions has the following impacts:

1. Users with liquidated positions unfairly receive rewards indefinitely, since they have no funds locked in the system or debt that accrues interest, which is essentially stealing yield.
2. The protocol loses rewards that should have been distributed to active users who maintain their positions.

## Code Snippet
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolSetters.sol#L148-L176
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol

## Tool used

Manual Review

## Recommendation

- To address this issue, it is recommended to notify the `NFTPositionManager` about the balance decrease when a position managed by the `NFTPositionManager` is liquidated.
  This can be achieved by adding a function in the `NFTPositionManager` contract that can be called by the pool in the liquidation process to update the user's balances accurately , and modify the pool to know if the position being liquidated is a positionManger position and notify it if so.