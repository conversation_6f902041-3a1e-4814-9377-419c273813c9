Innocent Chartreuse Parrot

Medium

# Violators will continue to earn rewards on their outdated collateral balance post liquidation

## Summary
Violators will continue to earn rewards on their outdated collateral balance post liquidation

## Vulnerability Detail
A user's "staked" collateral balance is adjusted (incremented/decremented) in the `_handleSupplies` internal function:

[NFTRewardsDistributor::_handleSupplies](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165-L173)
```solidity
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];


    _updateReward(tokenId, _assetHash);


    _balances[tokenId][_assetHash] = balance;
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```

This internal function is currently only accessible via [supplies](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L58) and [withdraws](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L100) via the `NFTPositionManager`.

However, a user's collateral balance is also adjusted during liquidation, but the `_handleSupplies` logic is [not triggered during liquidations](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L207-L230
). Therefore, a liquidation will result in a user's collateral balance decreasing but their "staked" collateral balance will remain the same.

## Proof of Concept
Place the following test inside `NFTPositionManagerTest.t.sol` and run with `forge test --mc NFTPostionManagerTest --mt testStakedCollatBalanceStaysSamePosLiquidation`:

```solidity
    function testStakedCollatBalanceStaysSamePosLiquidation() public {
        // alice supplies tokenA
        testShouldSupplyAlice();

        // bob supplies tokenB to pool 
        _mintAndApprove(bob, tokenB, 100 ether, address(pool)); 

        vm.startPrank(bob);
        pool.supplySimple(address(tokenB), bob, 50 ether, 0); 
        vm.stopPrank();

        // alice borrows tokenB
        uint256 borrowAmount = 10 ether;
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory params =
          INFTPositionManager.AssetOperationParams(address(tokenB), alice, borrowAmount, 1, data);

        vm.startPrank(alice);
        nftPositionManager.borrow(params);
        vm.stopPrank();

        bytes32 assetHash = nftPositionManager.assetHash(address(pool), address(tokenA), false);
        uint256 startCollateralBalInPool = pool.getBalance(address(tokenA), address(nftPositionManager), 1);
        uint256 startCollateralBalInRewards = nftPositionManager.balanceOfByAssetHash(1, assetHash);

        assertEq(startCollateralBalInPool, startCollateralBalInRewards);

        // position becomes liquitable (can be via interest accrural or external price event)
        oracleA.updateAnswer(5e3);

        // alice is liquidated and collateral balance decreases in pool
        bytes32 pos = keccak256(abi.encodePacked(address(nftPositionManager), 'index', uint256(1)));
        vm.prank(bob);
        pool.liquidateSimple(address(tokenA), address(tokenB), pos, 1 ether);

        uint256 endCollateralBalInPool = pool.getBalance(address(tokenA), address(nftPositionManager), 1);

        assertLt(endCollateralBalInPool, startCollateralBalInPool);

        // alice's staked collateral balance did not decrease in the NFTPositionManager contract
        uint256 endCollateralBalInRewards = nftPositionManager.balanceOfByAssetHash(1, assetHash);
        assertEq(startCollateralBalInRewards, endCollateralBalInRewards);
    }
```

## Impact
After liquidations, the violator will continue to earn rewards based on their outdated "staked" collateral balance. 

Note that whether all of the violator's collateral is seized or only part of it, a good samaritan is able to re-adjust the violator's "staked" collateral balance by supplying an amount to the user's position via `NFTPositionManager::supply`. This will trigger the `_handleSupplies` function and re-adjust the "staked" collateral balance. However, in either case the violator will be earning outsized rewards until this event is noticed by someone and the appropriate remediation is performed (we can not assume it will occur instantly). Therefore, until this occurs, the violator will be earning rewards they are not supposed to earn and thus will be diluting other users' rewards.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L207-L230

## Tool used

Manual Review

## Recommendation
I would recommend exposing a permissioned external function in the `NFTPositionManager` contract that solely calls into the `_handleSupplies` function. This function can then be called during liquidations to properly update the violator's "staked" collateral balance. 

However, it should be noted that positions can also be created directly via the Pool contract and thus not earn rewards (which are tracked in the `NFTPositionManager` contract). Therefore, there should also be a method introduced that allows the pool to determine if the position originated from the `NFTPositionManager` contract. If so, then it can call into the new external function to adjust the violator's "staked" collateral balance during liquidations.