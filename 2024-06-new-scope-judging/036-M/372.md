Hidden Gingham Troll

High

# Users will receive reduced rewards due to incorrect boosted balance calculation

### Summary

_No response_

### Root Cause

In [NFTRewardsDistributor.sol#L122](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L122) the return statement in the boostedBalance function incorrectly limits the boosted balance to the original balance.

### Internal pre-conditions

User needs to have a balance and voting power that could result in a boosted balance greater than their original balance.

### External pre-conditions

_No response_

### Attack Path

1. User stakes tokens and gains voting power.
2. User's boosted balance is calculated, but capped at their original balance.

```solidity
function boostedBalance(address account, uint256 balance) public view returns (uint256) {
    uint256 _boosted = (balance * 20) / 100;
    uint256 _stake = stakingToken.getVotes(account);

    uint256 _adjusted = ((balance * _stake * 80) / maxBoostRequirement) / 100;

    // because of this we are able to max out the boost by 5x
    uint256 _boostedBalance = _boosted + _adjusted;
@>    return _boostedBalance > balance ? balance : _boostedBalance;
  }
``` 
3. User receives rewards based on the capped balance instead of the higher boosted balance.

### Impact

The user will receive the lowest value between `_boostedBalance` and `balance`, instead of receiving the highest value between them.

### PoC

_No response_

### Mitigation

```diff
function boostedBalance(address account, uint256 balance) public view returns (uint256) {
    uint256 _boosted = (balance * 20) / 100;
    uint256 _stake = stakingToken.getVotes(account);

    uint256 _adjusted = ((balance * _stake * 80) / maxBoostRequirement) / 100;

    // because of this we are able to max out the boost by 5x
    uint256 _boostedBalance = _boosted + _adjusted;
-   return _boostedBalance > balance ? balance : _boostedBalance;
+  return _boostedBalance < balance ? balance : _boostedBalance;
  }
```