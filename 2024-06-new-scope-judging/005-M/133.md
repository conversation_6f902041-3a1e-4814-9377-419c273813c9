Big Admiral Dove

Medium

# Zero amount of asset will result in failure of reallocation in Curated Vault

## Summary

Reallocating with 0 amount of asset will cause the failure of the reallocation process.

## Vulnerability Detail

Reallocating zero assets to a pool means withdrawing the whole assets that the pool holds, and make the pool empty.

But in the `CuratedVault::reallocate()` function, if `allocation.assets` is 0, the function sets `toWithdraw` as 0.

```solidity
    ... ...
    if (allocation.assets == 0) {
        shares = supplyShares;
        toWithdraw = 0;
    }
    DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
    ... ...
```

Then the function tries to 0 assets from the pool via `pool.withdrawSimple()` and this will revert by the following validation:

```solidity
  function validateWithdraw(uint256 amount, uint256 userBalance) internal pure {
    require(amount != 0, PoolErrorsLib.INVALID_AMOUNT);
    require(amount <= userBalance, PoolErrorsLib.NOT_ENOUGH_AVAILABLE_USER_BALANCE);
  }
```

## Impact

Reallocating with 0 amount of asset will cause the failure of the reallocation process.

## Code Snippet

[core/vaults/CuratedVault.sol#L250](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L250)

## Tool used

Manual Review

## Recommendation

I'd suggest changing the issued code snippet like the below:

```diff
    ... ...
    if (allocation.assets == 0) {
        shares = supplyShares;
-       toWithdraw = 0;
+       toWithdraw = type(uint256).max;
    }
    DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
    ... ...
```
