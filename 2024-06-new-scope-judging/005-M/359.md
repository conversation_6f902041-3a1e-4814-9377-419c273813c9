Cheerful Flaxen Hedgehog

Medium

# DoS In Reallocate If One withdrawSimple function revert

### Summary

DoS In `Reallocate` If One `withdrawSimple` function revert

### Root Cause

Every` withdrawSimple()`  call could [revert](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L97) if the `amount` is zero.

When [`toWithdraw `= 0](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L250),as[ this call ](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L253)is not wrapped into a try-catch block, the transaction will revert and therefore the `Allocator` is not able to[ reallocates](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L232) the vault's liquidity

### Internal pre-conditions

`Allocator` needs to call` reallocate() ` to reallocates the vault's liquidity

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

DoS In Reallocate function.

### PoC

_No response_

### Mitigation

Wrap the[ withdrawSimple()](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L253) call into a try-catch block as [_withdrawPool](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L134)