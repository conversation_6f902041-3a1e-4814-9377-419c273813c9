Rural Eggshell Sheep

Medium

# reallocate will revert if toWithdraw greater than 0.

### Summary

 Rellocate function is used Reallocate the vault's liquidity so as to reach a given allocation of assets on each given market.
>    // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.

Here , ``it Guarantees that donations can be withdrawn but it will revert in the last check.``

If toWithdraw > 0 , the totalWithdrawn will be increased by burnt.assetsand which causes it to revert due to totalSupplied = 0.


### Root Cause

In `CuratedVault:L232`, due to this ``totalWithdrawn != totalSupplied`` check it will revert

### Internal pre-conditions

1. Allocator call reallocate

### External pre-conditions

_No response_

### Attack Path

1. donate to the pool which will increase supplyAssets.
2. Allocator calls reallocate
3. toWithdraw > 0 and totalWithdrawn = burnt.assets
4. and will revert
   

### Impact

Dos

### PoC

_No response_

### Mitigation

seperate this if else statement in two if statements.
1. if (toWithdraw > 0 ) for to withdraw remaning liquidity.
2. if (toWithdraw == 0 ) for to supply the withdrawn assets from ``1`` which will balance out at ``totalWithdrawn != totalSupplied``.
