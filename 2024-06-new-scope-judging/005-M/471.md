Ancient Gingerbread Rooster

High

# The `CuratedVault.reallocate` function will fail when `allocation.assets == uint256.max`, even though this scenario is possible

## Summary
The `CuratedVault.reallocate` function will fail when `allocation.assets == uint256.max`, even though this scenario is possible.

## Vulnerability Detail
The `CuratedVault` contract includes a `reallocate` function designed to redistribute asset balances across various markets. The desired asset amount for each market is specified by the `assets` parameter within the `MarketAllocation` structure. During execution, a scenario arises when `allocation.assets == uint256.max`, indicating that the market (or pool) should receive the maximum assets possible up to its cap.

However, based on current conditional logic, this allocation might fail if processed midway. The `suppliedAssets`, representing the amount to be provided to the pool, is determined by `totalWithdrawn.zeroFloorSub(totalSupplied)`. If this value exceeds the market's `supplyCap`, the transaction fails. This approach is flawed because the function should not aim to supply all available assets immediately. Instead, it should only supply up to the supplyCap, allowing any excess to be allocated to subsequent pools. A final check is already in place to ensure that all withdrawn assets are adequately distributed.

```solidity
  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    uint256 totalSupplied;
    uint256 totalWithdrawn;

    for (uint256 i; i < allocations.length; ++i) {
      MarketAllocation memory allocation = allocations[i];
      IPool pool = allocation.market;

      (uint256 supplyAssets, uint256 supplyShares) = _accruedSupplyBalance(pool);
      uint256 toWithdraw = supplyAssets.zeroFloorSub(allocation.assets);

      if (toWithdraw > 0) {
        ...
      } else {
>       uint256 suppliedAssets =
          allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets);

        if (suppliedAssets == 0) continue;

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) revert CuratedErrorsLib.UnauthorizedMarket(pool);

>       if (supplyAssets + suppliedAssets > supplyCap) revert CuratedErrorsLib.SupplyCapExceeded(pool);

        // The market's loan asset is guaranteed to be the vault's asset because it has a non-zero supply cap.
        IERC20(asset()).forceApprove(address(pool), type(uint256).max);
        DataTypes.SharesType memory minted = pool.supplySimple(asset(), address(this), suppliedAssets, 0);
        emit CuratedEventsLib.ReallocateSupply(_msgSender(), pool, minted.assets, minted.shares);
        totalSupplied += suppliedAssets;
      }
    }

    if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();
  }
```

## Impact
Reallocation loses its flexibility to supply markets and is likely to fail, even in scenarios where success is possible.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L257

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L265

## Tool used
Manual Review

## Recommendation
Supply should be provided up to the limit of the `supplyCap`.

```diff
  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    uint256 totalSupplied;
    uint256 totalWithdrawn;

    for (uint256 i; i < allocations.length; ++i) {
      MarketAllocation memory allocation = allocations[i];
      IPool pool = allocation.market;

      (uint256 supplyAssets, uint256 supplyShares) = _accruedSupplyBalance(pool);
      uint256 toWithdraw = supplyAssets.zeroFloorSub(allocation.assets);

      if (toWithdraw > 0) {
        ...
      } else {
-       uint256 suppliedAssets =
          allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets);

+       if(allocation.assets == type(uint256).max){
+         suppliedAssets = totalWithdrawn.zeroFloorSub(totalSupplied);
+         if(suppliedAssets > supplyCap - supplyAssets) {
+           suppliedAssets = supplyCap - supplyAssets;
+         }
+       } else {
+         suppliedAssets = allocation.assets.zeroFloorSub(supplyAssets);
+       }

        if (suppliedAssets == 0) continue;

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) revert CuratedErrorsLib.UnauthorizedMarket(pool);

        if (supplyAssets + suppliedAssets > supplyCap) revert CuratedErrorsLib.SupplyCapExceeded(pool);

        // The market's loan asset is guaranteed to be the vault's asset because it has a non-zero supply cap.
        IERC20(asset()).forceApprove(address(pool), type(uint256).max);
        DataTypes.SharesType memory minted = pool.supplySimple(asset(), address(this), suppliedAssets, 0);
        emit CuratedEventsLib.ReallocateSupply(_msgSender(), pool, minted.assets, minted.shares);
        totalSupplied += suppliedAssets;
      }
    }

    if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();
  }
```