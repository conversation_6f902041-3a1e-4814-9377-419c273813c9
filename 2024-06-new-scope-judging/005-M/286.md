Jumpy Watermelon Mockingbird

Medium

# Allocator cannot fully withdraw assets from a pool when setting allocation to zero

### Summary

In CuratedVault.sol, the `reallocate()` function allows the allocator to adjust asset allocations across different pools. Setting `allocation.assets` to 0 for a pool should indicate a full withdrawal of all assets from that pool. However, when `allocation.assets` is 0, the code sets `toWithdraw` to 0, preventing any asset withdrawal from the specified pool.

### Root Cause

The root cause of this vulnerability is in the following code block within the `reallocate()` function:

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L247-L251

This logic incorrectly sets `toWithdraw` to 0 when `allocation.assets` is 0, which contradicts the expected behavior of a full withdrawal.

### Internal pre-conditions

1. The vault has assets currently supplied to the target pool.
2. The allocator calls `reallocate()` with `allocation.assets` set to 0 for the target pool.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

- The allocator loses the ability to fully withdraw assets from a pool using the `reallocate()` function.
- This leads to assets being locked in pools that are no longer desired, affecting the overall strategy and performance of the vault.
- In scenarios where quick reallocation is needed (e.g., in response to market conditions), this bug prevents timely asset rebalancing.

### PoC

_No response_

### Mitigation

Modify the `reallocate()` function to correctly handle full withdrawals:

```solidity
if (allocation.assets == 0) {
  shares = supplyShares;
  toWithdraw = supplyAssets; // Withdraw all assets when allocation is set to 0
}
```