Modern Mauve Badger

Medium

# To withdraw the donation or Front running assets from market will result in DoS for `reallocation`

### Summary

The vault allows the allocator to reallocate assets across different pools and markets, ensuring that liquidity remains consistent across different trading venues. However, when the allocator wants to withdraw assets from one market to  reallocate them to another, or if there are donated tokens in the pool/market, or if there is a risk of frontrunning, the allocator might set `allocation.assets == 0`. This will cause a revert because the market does not permit withdrawing a zero amount.

```solidity
zerolend-one/contracts/core/vaults/CuratedVault.sol:247
247:         // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
248:         uint256 shares;
249:         if (allocation.assets == 0) {
250:           shares = supplyShares;
251:           toWithdraw = 0; // @audit-issue : due to this zero value it will revert
252:         }
253: 
```


### Root Cause

The Root cause of this Issue is The amount to withdraw is set to zero incase when `allocation.asset=0` Because of to withdraw donation or front running tokens from market.
[Link](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L232-L276).


### Internal pre-conditions

_No response_

### External pre-conditions

The market has some donated assets, or there is a risk of front running when reallocating assets between markets.


### Attack Path

The allocator calls the reallocation function to move assets between two markets. The allocator sets `allocation.assets = 0` for market 0 (which has donated assets or a risk of frontrunning) and intends to reallocate its assets to market 1. However, when the allocator executes the reallocation function, it results in a revert.


### Impact

The expected flow is to withdraw the donated or front running assets as well, but due to `toWithdraw = 0`, the transaction will revert, and no donated or front running tokens will be withdrawn.


### PoC

```solidity
  function testWilFailToReallocateAssets() public {
    _setCap(allMarkets[0], 1 ether);

    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    loanToken.mint(supplier, 1 ether);
    collateralToken.mint(borrower, 2 ether);

    vm.prank(supplier);
    allMarkets[0].supplySimple(address(loanToken), supplier, 1 ether, 0);

    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 2 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 1 ether, 0);
    vm.stopPrank();

    _forward(1000);
    oracleB.updateRoundTimestamp();

    loanToken.mint(supplier, 1 ether);

    vm.prank(supplier);
    vault.deposit(1 ether, onBehalf);

    assertEq(vault.maxDeposit(supplier), 0);

    MarketAllocation memory market1 = MarketAllocation(allMarkets[0], 0); // @audit : unknown frontrunning donations can be withdrawn
    MarketAllocation memory market2 = MarketAllocation(allMarkets[1], type(uint256).max);
    MarketAllocation[] memory arr = new MarketAllocation[](2);
    arr[0] = market1;
    arr[1] = market2;
    vm.prank(allocator);
    vm.expectRevert(bytes("INVALID_AMOUNT"));
    vault.reallocate(arr);
  }

```

Run with command : `forge test --mt testWilFailToReallocateAssets -vvv`

### Mitigation

_No response_