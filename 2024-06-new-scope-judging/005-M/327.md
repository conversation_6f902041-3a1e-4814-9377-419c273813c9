Clever Ebony Halibut

Medium

# Incorrect Implementation in Reallocate Function Prevents Full Withdrawal from Pools

## Summary

The `reallocate` function in `CuratedVault.sol` contains a logical flaw preventing complete asset withdrawal from markets. It incorrectly sets the withdrawal amount to 0, causing transaction failures and unintended behavior.

## Vulnerability Detail

- The reallocate function in `CuratedVault.sol` is designed to dynamically adjust asset distribution across different markets within the vault. It processes an array of `MarketAllocation` structures, each specifying a pool (market) and the desired asset amount for that pool. The function then attempts to adjust the supplied amount to reach the target allocation. If the amount exceeds the target, it will withdraw the excess, and if it's less than the target allocation, it will supply the missing amount.
  </br>

- Resetting a market's allocation to 0 (withdraw all supplied asset from that market) is a critical operation for several reasons:

  - Disabling underperforming or risky markets (remove a pool)
  - Changing investment strategies
  - Responding to market conditions or regulatory changes

To achieve this, allocators will set a market's assets in the `MarketAllocation` to 0, signaling a complete withdrawal from that market.

However, the current implementation prevents this operation due to a logical flaw in the withdrawal process.

```js

  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    // NOTE marketAllocation = market , assets
    uint256 totalSupplied;
    uint256 totalWithdrawn;

    for (uint256 i; i < allocations.length; ++i) {
     // some code ..

      if (toWithdraw > 0) {
        if (!config[pool].enabled) revert CuratedErrorsLib.MarketNotEnabled(pool);
        uint256 shares;
        if (allocation.assets == 0) {
          shares = supplyShares;
          toWithdraw = 0;
        }
        DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
        emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
        totalWithdrawn += burnt.assets;

// remaining code ..
  }
```

Note that when `allocation.assets` is 0, indicating a desire to fully withdraw, the code sets `toWithdraw` to 0. This leads to an attempt to withdraw 0 assets, which is the opposite of what we need. We should be withdrawing the entire amount. Moreover, this will cause the entire transaction to revert due to a check in `ValidationLogic.sol` when attempting to withdraw 0 assets.

```js
function validateWithdraw(uint256 amount, uint256 userBalance) internal pure {
require(amount != 0, PoolErrorsLib.INVALID_AMOUNT);
require(amount <= userBalance, PoolErrorsLib.NOT_ENOUGH_AVAILABLE_USER_BALANCE);
}
```

## Impact

- It results in unintended behavior where the reallocate function fails to perform complete withdrawals from markets as designed. This leads to a potential denial of service (DoS) on the reallocate function when attempting to withdraw the entire amount from any market. Consequently, allocators are unable to fully withdraw assets, limiting their ability to manage the vault's portfolio effectively.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L232-L276

## Tool used

Manual Review

## Recommendation

make this changes to `reallocate` function in `CuratedVault.sol` , which ensures that when `allocation.assets` is set to `0`, the full amount (including any frontrunning donations) will be withdrawn. It allows proper market disabling and effectively handles any potential frontrunning donations, contrary to the misleading comment in the original code :

```diff
if (toWithdraw > 0) {
  if (!config[pool].enabled) revert CuratedErrorsLib.MarketNotEnabled(pool);

-  // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
-  uint256 shares;
-  if (allocation.assets == 0) {
-    shares = supplyShares;
-    toWithdraw = 0;
-  }

  DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
  emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
  totalWithdrawn += burnt.assets;
}
```