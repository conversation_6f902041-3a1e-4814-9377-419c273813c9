Joyous Cedar Tortoise

High

# Incorrect handling of allocation.assets=0 in CuratedVault.reallocate()

### Summary

Incorrect handling of allocation.assets=0  in CuratedVault.reallocate() means that tokens 0 tokens will be withdrawn, instead of withdrawing 100% of the deposited assets

### Root Cause

Within CuratedVault.reallocate(), the [following logic](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/vaults/CuratedVault.sol#L246-L253) is implemented:
```solidity
// Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
uint256 shares;
if (allocation.assets == 0) {
	shares = supplyShares;
	toWithdraw = 0;
}

DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
```
This is incorrect because it is meant to withdraw 100% of the shares, as denoted by the `shares` local variable. However the `shares` variable is unused, and instead `toWithdraw` (0) is used.

This means that `0` tokens will be withdrawn instead of withdrawing `shares` shares.


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Reallocation will occur incorrectly, and in other cases will revert.

### PoC

Foundry test (Add to `test/forge/core/vaults/VaultTesting.sol`)

<details>
<summary> Test </summary>

```solidity
function testJ_reallocate() public {
        IPool pool0 = allMarkets[0];

        address user2 = makeAddr('user2');

        uint256 maxDeposit = vault.maxDeposit(supplier);
        console.log("max deposit is %e", maxDeposit);
        deal(address(loanToken), supplier, maxDeposit);

        vm.startPrank(supplier);
        vault.deposit(maxDeposit/5, supplier);

        // now a borrower comes in
        uint256 borrowed = 1e18;
        uint256 collateralBalance = borrowed * 2;
        deal(address(loanToken), borrower, 1e12); // to pay interest
        deal(address(collateralToken), borrower, collateralBalance);

        vm.startPrank(borrower);
        loanToken.approve(address(pool0), type(uint256).max);
        pool0.supplySimple(address(collateralToken), borrower, collateralBalance, 0);
        pool0.borrowSimple(address(loanToken), borrower, borrowed, 0);
        vm.stopPrank();

        MarketAllocation[] memory allocs = new MarketAllocation[](2);

        allocs[0].market = allMarkets[0];
        allocs[0].assets = 0;

        allocs[1].market = allMarkets[0];
        allocs[1].assets = type(uint256).max;
        console.log("time to reallocate\n");

        vm.startPrank(allocator);
        vm.expectRevert("INVALID_AMOUNT");
        vault.reallocate(allocs);
    }
```
</summary>
</details>

### Mitigation

When allocations.asset is set to 0 , use IPool.getBalance() to obtain the amount of assets to withdraw. Use that value when withdrawing from the pool, rather than toWithdraw which was set to 0