Steep Aqua Yak

Medium

# Reallocation will be blocked if trying to withdraw all from a market

### Summary

Pool withdrawal amount validation causes Vault reallocation to be blocked when trying to withdraw all asset from pool

### Root Cause

In `CuratedVault#reallocation` function, [it is allows to withdraw all asset from a market ](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L246-L251) by setting `allocation.assets == 0`.
However, [the Pool's validation does not allow to set withdrawal amount to zero](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L96-L99), which will cause the reallocation logic to be blocked in case withdraw all from a market

### Internal pre-conditions

1. Allocators needs to call function `CuratedVault#reallocate()` with an element of array `allocations` to satisfy `allocation.assets == 0`.
2. Allocators set amount to `0` to protect from front-running donations

### External pre-conditions

_No response_

### Attack Path

1. Allocators needs to call function `CuratedVault#reallocate()` with an element of array `allocations` to satisfy `allocation.assets == 0`. 

### Impact

1. Protocol can not be protected from unknown front-running donations from removing market
2. Can not withdraw all assets from a market in reallocation

### PoC

Add the test below to the test file `test/vaults/ERC4626Test.sol`
```solidity
... 
function test_reallocation_fail() public {
    _setCap(allMarkets[0], 100 ether);

    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    loanToken.mint(supplier, 1000 ether);
    collateralToken.mint(borrower, 200 ether);

    vm.startPrank(supplier);
    loanToken.approve(address(vault), 1000 ether);
    uint256 share = vault.deposit(50 ether, supplier);

    MarketAllocation[] memory allocations = new MarketAllocation[](1);
    allocations[0].market = allMarkets[0];  // SET ZERO AMOUNT
    vm.startPrank(owner);

    vm.expectRevert();
    vault.reallocate(allocations);
  }
```

Run the test `test_reallocation_fail` and the console shows:
```bash
Ran 1 test for test/forge/core/vaults/ERC4626Test.sol:ERC4626Test
[PASS] test_reallocation_fail() (gas: 561418)
```

### Mitigation

Set amount for `toWithdraw` in function `reallocate()` as below
```diff
        uint256 shares;
        if (allocation.assets == 0) {
          shares = supplyShares;
-          toWithdraw = 0;
+          toWithdraw = supplyAssets
        }
```