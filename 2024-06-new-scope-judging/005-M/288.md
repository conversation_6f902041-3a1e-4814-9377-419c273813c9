Curly Pineapple Armadillo

Medium

# `reallocate` reverts if all pool's assets are reallocated

### Summary

`reallocate` will revert if all assets of a pool are reallocated to another pool. This occurs because when `allocation.assets == 0` `toWithdraw` is set to 0, causing `withdrawSimple` to revert, as `Pool.sol` does not support zero withdrawals.
It is important to note that even if the call didn't revert, calling `reallocate` in the attempt to reallocate all assets from a pool will not reallocate any assets as `toWithdraw` is set to 0.

### Root Cause

- In [`reallocate`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L250) `toWithdraw` is set to 0 if all assets of a pool are reallocated to another pool
- `pool.withdrawSimple` reverts on zero withdrawals in [`validateWithdraw`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L97)

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Allocator wants to reallocate all assets from a pool in order to remove the pool from the withdrawal queue
2. The allocator is unable to as the call will revert

### Impact

A pool's assets cannot be fully reallocated.

### PoC

_No response_

### Mitigation

`reallocate` should properly handle full reallocations by actually withdrawing all of the assets a pool holds.