Long Coffee Cat

High

# `Allocator` request for full pool withdrawal produce no withdrawal at all that leads to funds misallocation

### Summary

CuratedVault cannot withdraw from a pool fully since `allocation.assets == 0` case logic leads to noop

### Root Cause


Since `shares` aren't utilized when `allocation.assets == 0` instead of full withdrawal nothing happens as it's `toWithdraw == 0` and no other control is used:

[CuratedVault.sol#L246-L255](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L246-L255)

```solidity
        // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
        uint256 shares;
        if (allocation.assets == 0) {
>>        shares = supplyShares;
>>        toWithdraw = 0;
        }

>>      DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
        emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
        totalWithdrawn += burnt.assets;
```

Notice that in MetaMorpho it is either assets (`withdrawn`) or `shares`:

[MetaMorpho.sol#L383-L389](https://github.com/morpho-org/metamorpho/blob/cc6b01610c9b0000d965faef705e1670859d9c0f/src/MetaMorpho.sol#L383-L389)

```solidity
                if (allocation.assets == 0) {
>>                  shares = supplyShares;
                    withdrawn = 0;
                }

                (uint256 withdrawnAssets, uint256 withdrawnShares) =
>>                  MORPHO.withdraw(allocation.marketParams, withdrawn, shares, address(this), address(this));
```

So, it is impossible to empty the pool and its removal, if needed, have to happen with non-zero assets invested

### Internal pre-conditions

Pool needs to be removed from usage as a part of usual Vault management

### External pre-conditions

There are any positive amount of assets invested in the pool by the Vault

### Attack Path

No direct attack is needed, it's a malfunction during normal Vault workflow

### Impact

It is not possible to withdraw from a pool fully. The consequence is possible misallocations until `Allocator` understands that `allocation.assets == 0` won't have any effect. Notice that this noop won't revert the allocation as withdrawal with `allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets)` have to be the last in order for `totalWithdrawn == totalSupplied` condition to be satisfied. So it will lead to an altered version of allocation, i.e. suboptimal allocation with regard to `Allocator` view, which leads to losses for Vault's users.

It will also lead to impossibility of pool removal without inflating Vault's total assets reading (this is a combined impact, see other issue): on the one hand it's impossible to fully withdraw from a pool, on the other non-empty withdrawal corrupts the accounting thereafter

### PoC

`Allocator` wants to remove the funds completely from a pool and set `allocation.assets == 0` for it, but any such `reallocate()` call results in no assets being withdrawn instead.

### Mitigation

Consider requesting the whole currently invested assets, e.g.:

```diff
                if (allocation.assets == 0) {
-                 shares = supplyShares;
-                 toWithdraw = 0;                
+                 pool.forceUpdateReserve(asset());
+                 toWithdraw = pool.supplyAssets(asset(), positionId);
                }
```