Big Mandarin Bison

Medium

# Asset reallocation failure due to incorrect handling of zero allocation

## Summary
The `curatedvault::reallocate` function is aimed at readjusting the supply in each pool. However, the function fails to handle cases where a pool is set to be allocated a zero amount.
## Vulnerability Detail
There is a logic error in the `reallocate()` function. The variable `toWithdraw` is erroneously set to zero when allocation.assets is zero. This results in the function attempting to withdraw zero assets from a pool where it should normally withdraw the total liquidity.

## Impact
The function will revert unnecessarily whenever the allocator attempts to allocate a zero value to a pool. This is because The total amount of assets withdrawn will not match the expected amount, causing the condition `if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation()` to revert.
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L232
## Code Snippet
```solidity
  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    uint256 totalSupplied;
    uint256 totalWithdrawn;
    //@audit reading from storage cost more gas 
    for (uint256 i; i < allocations.length; ++i) {
      MarketAllocation memory allocation = allocations[i];
      IPool pool = allocation.market;

      (uint256 supplyAssets, uint256 supplyShares) = _accruedSupplyBalance(pool);
      uint256 toWithdraw = supplyAssets.zeroFloorSub(allocation.assets);

      if (toWithdraw > 0) {
        if (!config[pool].enabled) revert CuratedErrorsLib.MarketNotEnabled(pool);

        // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
        uint256 shares;
        if (allocation.assets == 0) {
          shares = supplyShares;
@>          toWithdraw = 0; //bug towithdraw should be set to supplyAsset 
        }

        DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
        emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
        totalWithdrawn += burnt.assets;
      } else {
        uint256 suppliedAssets =
          allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets);

        if (suppliedAssets == 0) continue;

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) revert CuratedErrorsLib.UnauthorizedMarket(pool);

        if (supplyAssets + suppliedAssets > supplyCap) revert CuratedErrorsLib.SupplyCapExceeded(pool);

        // The market's loan asset is guaranteed to be the vault's asset because it has a non-zero supply cap.
        IERC20(asset()).forceApprove(address(pool), type(uint256).max);
        DataTypes.SharesType memory minted = pool.supplySimple(asset(), address(this), suppliedAssets, 0);
        emit CuratedEventsLib.ReallocateSupply(_msgSender(), pool, minted.assets, minted.shares);
        totalSupplied += suppliedAssets;
      }
    }

    if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();
  }
```
## Tool used

Manual Review

## Recommendation
```diff
- toWithdraw = 0;
+ toWithdraw = supplyAssets;
```