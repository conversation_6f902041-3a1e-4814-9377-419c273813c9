Fresh Sangria Mink

Medium

# Wrong stale price check in `PoolGetters::getAssetPrice`

## Summary

## Vulnerability Detail
`PoolGetters::getAssetPrice` uses the constant value of `1800` to do the staleness check but pools can be initialized with any ERC20 tokens, even the non-standard ones e.g USDC and BNB
>Any token that follows the ERC20 token standard. Even non-compliant ones like USDT, BNB etc… are fine.

It is an error to assume that the same time interval heartbeat can be used as a staleness check for every feed, as different feeds can have different heartbeats.

for example
1. Heartbeat for USDC on EThereum Mainnet is `86400s`

2. but `PoolGetters::getAssetPrice` uses `1800s`
>require(block.timestamp <= updatedAt + 1800, 'Stale Price');
## Impact
Price returned will not be accurate and can lead to financial loss for users as the `getAssetPrice` will return stale price
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161
## Tool used

Manual Review

## Recommendation
Add another variable in the `DataTypes::ReserveData` e.g heartbeat specific to that reserve, and pass it with oracle as an argument to the `PoolGetters::getAssetPrice` function