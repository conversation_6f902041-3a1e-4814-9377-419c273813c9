Big Mandarin Bison

High

# Incorrect stale time check resulting in periods of inactivity

## Summary
The 30-minute stale period is too short for the Ethereum and Linea networks, which are the primary chains on which the project will be deployed. Setting a 30-minute stale period for Ethereum could result in up to 30 minutes of inactivity, while for the Linea network, it could lead to over 23 hours of inactivity.
## Vulnerability Detail
The `PoolGetter::getAssetPrice` function includes a check for staleness, using a staleness threshold of 1,800 minutes (30 minutes). However, the heartbeat intervals for the `Ethereum` and `Linea network` are `1 hour` and `24 hours`, respectively. Due to this discrepancy, the function can only be called within 30 minutes after the last update. Consequently, for Ethereum, there can be up to 30 minutes of inactivity, and for the Linea network, more than 23 hours of inactivity before the next update is available. 
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L160

## Impact
Setting the stale period too short will render the protocol inoperable for extended periods. As a result, certain functions that depend on up-to-date price data will be unusable until the next update occurs.
1. Users incurring bad debt cannot be liquidated during this period of inactivity, which will lead to the accumulation of additional bad debt, as `executeLiquidationCall()` from the `LiquidationLogic` library also depend on the `PoolGetters::getAssetPrice` function
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94
2. Users cannot borrow during this period of inactivity, as the `validateBorrow()` from the `Validationlogic` library depends on the `PoolGetters::getAssetPrice` function
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L168
## Code Snippet
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
@>    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```

## Tool used

Manual Review
solodit

## Recommendation
I recommend using the `mapping` data type to record the staleness time of each collateral token and setting each token's staleness time with an appropriate stale period.

For all other possible EVM chains that the protocol might support in the future, as referenced in the project's README, the following heartbeat intervals should be considered for each of these chains: 
    On Ethereum, the oracle will update the price data [every ~1 hour] 
    (https://data.chain.link/ethereum/mainnet/crypto-usd/eth-usd).
    On Polygon, the oracle will update the price data [every ~25 seconds] 
    (https://data.chain.link/polygon/mainnet/crypto-usd/eth-usd).
    On BNB (BSC), the oracle will update the price data [every ~60 seconds] 
    (https://data.chain.link/bsc/mainnet/crypto-usd/eth-usd).
    On Optimism, the oracle will update the price data [every ~20 minutes] 
    (https://data.chain.link/optimism/mainnet/crypto-usd/eth-usd).
    On Arbitrum, the oracle will update the price data [every ~24 hours] 
    (https://data.chain.link/arbitrum/mainnet/crypto-usd/eth-usd).
    On Avalanche, the oracle will update the price data [every ~24 hours] 
    (https://data.chain.link/avalanche/mainnet/crypto-usd/eth-usd). 
The block of code below shows a brief example of how the implementation should go:
```solidity
mapping(address reserve => uint256 stalenessTime) staleTime;

function setStaleTime(address _reserve, uint256 staleness) public onlyOwner {
    staleTime[staleness];
}
```
```diff
   function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
-    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
+   require(block.timestamp <= updatedAt + staleTime[reserve], 'Stale Price');
    return uint256(price);
  ```