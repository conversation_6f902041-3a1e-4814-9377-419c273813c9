Itchy Pewter Canary

High

# PoolGetters:getAssetPrice() uses a staleness period of 30 minutes which is too low for almost every data feed

### Summary

`PoolGetters:getAssetPrice()` will only work for 30 minutes after the last update since a staleness period of 30 minutes is too low for almost every data feed.

### Root Cause

[`PoolGetters:getAssetPrice()` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161) incorrectly uses an heartbeat of 30 minutes for all ChainLink interactions:

```js
function getAssetPrice(address reserve) public view override returns (uint256) {
  (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
  require(price > 0, 'Invalid price');
  require(block.timestamp <= updatedAt + 1800, 'Stale Price'); // <@
  return uint256(price);
}
```

On [this page](https://docs.chain.link/data-feeds/price-feeds/addresses?network=ethereum&page=4&search=%2FUSD) we can see a list of all avaiable ChainLink Data Feeds and by clicking "show more" we can also see their heartbeat, notice that:
- all USD denominated feeds have 1 hour heartbeat on Mainnet
- all USD denominated feeds have 1 days heartbeat on Linea

This means that at least 1 hour on Mainnet, and 1 day on Linea, has to pass before the next price is pushed (it can also happen sooner if the deviation threshold is met) causing oracle-dependent operations to revert after 30 minutes have passed since the last update.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

It will only be possible to use this function in the first 30 minutes after the last update has been pushed onto the feed.

Especially on Linea, where USD feeds have an heartbeat of 1 day, it will be impossible to lend/borrow/liquidate untill a new price update is pushed.

### PoC

Add the following test to `PoolLiquidationTest.t.sol`:

```js
function test_getAssetPriceRevertsAfter30Minutes() public {
  pool.getAssetPrice(address(tokenA));
  vm.warp(block.timestamp + 31 minutes);
  vm.expectRevert();
  pool.getAssetPrice(address(tokenA));
}
```

### Mitigation

Configure an hearbeat for every asset by adding an `heartbeat` field to `ReserveData` and use it while checking for staleness:

```diff
function getAssetPrice(address reserve) public view override returns (uint256) {
  (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
  require(price > 0, 'Invalid price');
- require(block.timestamp <= updatedAt + 1800, 'Stale Price');
+ require(block.timestamp <= updatedAt + _reserves[reserve].heartbeat, 'Stale Price');
  return uint256(price);
}
```

Use the page mentioned above to correctly configure those values for different chains.