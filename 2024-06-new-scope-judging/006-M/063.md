Careful Fleece Pike

Medium

# Hardcoded oracle's heartbeat in `Pool` will cause using stale price in case of short heartbeat oracle and DoS in case of long heartbeat oracle

### Summary

Hardcoded oracle's heartbeat in `Pool` will cause using stale price in case of short heartbeat oracle and Do<PERSON> in case of long heartbeat oracle.

### Root Cause

The oracle's heartbeat is hardcoded to `1800`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L161

```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
>>  require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```

### Internal pre-conditions

`Pool`'s admin uses an oracle with a heartbeat that is different than `1800`.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

- For an oracle with a heartbeat that is shorter than `1800`, the reported price will be stale when `updatedAt + oracleHeartbeat < block.timestamp <= updatedAt + 1800`.
- For an oracle with a heartbeat that is longer than `1800`, the function `PoolGetters#getAssetPrice` will be DoS when `updatedAt + 1800 < block.timestamp <= updatedAt + oracleHeartbeat`, which will cause the `Pool`'s lending functions that use `PoolGetters#getAssetPrice` to be DoS.

### PoC

_No response_

### Mitigation

Add a heartbeat for every oracle. In `PoolGetters#getAssetPrice`, an oracle's `updatedAt` should be checked against its own heartbeat.