Delightful Tin Nightingale

Medium

# Positions, which are using assets with large heartbeat may accrue bad debt

### Summary

The protocol is designed to be generic and support pools with different erc20 assets. The problem is that [getAssetPrice](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L164) uses chainlink `latestRoundData` with a hardcoded staleness check of 30 minutes. The problem is that many [chainlink price feeds](https://docs.chain.link/data-feeds/price-feeds/addresses?network=ethereum&page=1) has a 24 hours, or 1 hour heathbeat  period. This means that if the price is in the deviation threshold, it is not being updated on-chain. This may introduce problems during liquidations, if someone has a liquidatable position, which contains asset, that are in the deviation threshold and has been `updated < block.timestamp - 1800 (seconds)`:
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```

### Root Cause

Harcoded price staleness check for 30 minutes.

### Internal pre-conditions

No such.

### External pre-conditions

- Using a price feed with a heartbeat > 30 minutes. 
Probability for this is high, because [BTC/USD](https://etherscan.io/address/******************************************) and [ETH/USD](https://etherscan.io/address/******************************************) has a heartbeat of 60 minutes (3600 seconds)

- Price of the asset being in the deviation threshold for more than 30 minutes, which in bear market conditions is commonly seen

### Attack Path

1. User A has a position in pool P, which has WETH, WBTC and DAI as tokens.
2. User A has borrowed DAI against WETH and WBTC
3. He has 50% WETH and 50% WBTC and an average liquidation threshold of 80% 
4. His position threshold becomes 81%, because WBTC price starts to drop
5. However WETH is still in deviation threshold and has been updated before more than 30 minutes
6. The following will result in liquidation transaction revert, because [we loop trough each collateral and try to fetch it's price](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L110-L116) -> [calculateUserAccountData](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L81-L125)
7. When eth price is updated, position has already accused bad debt and liquidators has no incentive to repay the debt

### Impact

Bad debt accrual 

### PoC

_No response_

### Mitigation

Allow pool owners to set price staleness params.