Acrobatic Rainbow Grasshopper

Medium

# Using a hardcoded value for the Chainlink stale price check is dangerous

## Summary
Using a hardcoded value for the Chainlink stale price check is dangerous
## Vulnerability Detail
This is how we fetch a price from Chainlink:
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
As seen in the second check, we use a hardcoded value of 1800 to determine whether a price is stale. As this protocol will be deployed on multiple chains as mentioned in the README and many tokens will be used, using a hardcoded value is very dangerous. For example, just by spending a minute on Chainlink, here are some feeds with different heartbeats (hover your mouse over `Trigger parameters` to see):
https://data.chain.link/feeds/ethereum/mainnet/btc-eth (86400 seconds)
https://data.chain.link/feeds/optimism/mainnet/link-usd (1200 seconds)

Most price feeds have a heartbeat of 3600 seconds which also makes the hardcoded value wrong and will lead to reverts when the price isn't actually stale.
## Impact
Using a hardcoded value for the Chainlink stale price check is dangerous and could lead to either stale prices or reverting upon non-stale prices
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163
## Tool used

Manual Review

## Recommendation
Use a different value based on the feed you are using