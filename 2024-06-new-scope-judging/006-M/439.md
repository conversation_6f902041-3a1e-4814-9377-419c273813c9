Shiny Daisy Osprey

High

# getAssetPrice uses a fixed heartbeat of 30 min for stale price check, this would DoS priceFeeds of oracles with > 30 min heartbeat

### Summary

Using the same heartbeat(30 min) for all oracles will result in Dos for oracles with a heartbeat greater than 30 min.

### Root Cause

In `PoolGetters::getAssetPrice` 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');// <@
    return uint256(price);
  }
```

1800 seconds (30 min) is used as the heartbeat for all oracles, the issue is that some oracles have a heartbeat greater than 30 min, so this would revert leading to a DoS on functionalities that require price feeds like borrowing, liquidations etc.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

DOS on Protocol functions that require price feeds e.g withdraw, borrow, repay etc

### PoC

_No response_

### Mitigation

Consider adding a heartbeat field to the ReserveData

```diff
struct ReserveData {
    ReserveConfigurationMap configuration;
    uint128 liquidityIndex;
    uint128 liquidityRate;
    uint128 borrowIndex;
    uint128 borrowRate;
    uint40 lastUpdateTimestamp;
    uint16 id;
    address interestRateStrategyAddress;
    address oracle;
+   uint heartbeat;
    uint256 accruedToTreasuryShares;
}

  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
-   require(block.timestamp <= updatedAt + 1800, 'Stale Price');
+   require(block.timestamp <= updatedAt + _reserves[reserve].heartbeat, 'Stale Price');
    return uint256(price);
  }
```
