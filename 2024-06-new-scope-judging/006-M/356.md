Happy Corduroy Dalmatian

High

# ZeroLendOne Protocol would not Operate on it's intended chain of deployment!

### Summary

The 30-minute stale price check in the ``PoolGetter::getAssetPrice()`` function will cause a failure in ``liquidation`` attempts and other ``price-dependent actions`` for users as the function will revert on Ethereum after 30 minutes of price data, leaving a 30-minute gap before the next price update. On the Linea chain, this issue is exacerbated due to its ``24-hour heartbeat``, rendering the protocol unable to operate for 23 hours and 30 minutes. As a result, the liquidation process will fail when price data is required for accurate execution.
There's also a few thing to note about since the protocol plans on deploying to ethereum and it L2's,let's consider the ETH / USD oracles on different chains =>

*On Ethereum, the oracle will update the price data [every ~1 hour](https://data.chain.link/ethereum/mainnet/crypto-usd/eth-usd).
*On Polygon, the oracle will update the price data [every ~25 seconds](https://data.chain.link/polygon/mainnet/crypto-usd/eth-usd).
*On BNB (BSC), the oracle will update the price data [every ~60 seconds](https://data.chain.link/bsc/mainnet/crypto-usd/eth-usd).
*On Optimism, the oracle will update the price data [every ~20 minutes](https://data.chain.link/optimism/mainnet/crypto-usd/eth-usd).
*On Arbitrum, the oracle will update the price data [every ~24 hours](https://data.chain.link/arbitrum/mainnet/crypto-usd/eth-usd).
*On Avalanche, the oracle will update the price data [every ~24 hours](https://data.chain.link/avalanche/mainnet/crypto-usd/eth-usd).
On some chains such as  Polygon, BNB, and Optimism, 30 min can be considered too large for the stale period, causing the ``PoolGetter::getAssetPrice()`` function to return stale price data.

Whereas, on some chains, such as Arbitrum and Avalanche, 30 min is too small. Specifically, if the  protocol is deployed to Arbitrum or Avalanche, the protocol will be unable to operate because of the ["require(block.timestamp <= updatedAt + 1800)"] condition will be met causing a transaction to be reverted.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158

In``PoolGetter::getAssetPrice()``the stale period for price data is set to 30 minutes, which is too short for chains like Ethereum (with a 1-hour update interval) and Linea (with a 24-hour update interval). This causes valid price data within these intervals to be marked as stale and causes the function to revert unnecessarily.

In getAssetPrice() at require(block.timestamp <= updatedAt + 1800), the condition for the stale price check causes the function to revert prematurely for Ethereum and Linea chains.
The choice of a 30-minute stale check period for these chains is too aggressive, causing unnecessary reverts and rendering the protocol unusable during large gaps in oracle updates.

### Internal pre-conditions

- The protocol has configured the getAssetPrice() function to check for a stale period of 30 minutes.
- A Chainlink oracle for ETH/USD updates the price data every 1 hour on Ethereum and every 24 hours on Linea.
- The updated price data is less than 1 hour old but more than 30 minutes old on Ethereum, or less than 24 hours old but more than 30 minutes old on Linea.

### External pre-conditions

- The Ethereum Chainlink oracle needs to update its price data every 1 hour.
- The Linea Chainlink oracle needs to update its price data every 24 hours.

### Attack Path

``On etheruem Network``
- A user attempts to liquidate another user's position on Ethereum after 35 minutes from the last price update.
- The getAssetPrice() function is called to fetch the latest price.
- The require(block.timestamp <= updatedAt + 1800) check fails because 35 minutes is greater than 30 minutes, marking the price data as stale.
- The function reverts, preventing the liquidation process from proceeding even though the price data is still valid within the 1-hour heartbeat.
``On Linea network``
- A user tries to liquidate another user's position on Linea after 2 hours from the last price update.
- The getAssetPrice() function is called to fetch the latest price.
- The 30-minute stale check fails as the price was updated only 2 hours ago but still within the 24-hour update window.
- The liquidation process fails, even though the price data is valid.

### Impact

* Ethereum Chain: Users attempting to interact with the protocol (e.g., liquidate or redeem) within 30-60 minutes of the last price update will face reverts, preventing these crucial actions.
* Linea Chain: Users attempting to interact with the protocol within 30 minutes to 24 hours of the last price update will face reverts, essentially rendering the protocol unusable for 23 hours and 30 minutes. This significantly impacts the protocol’s availability and ability to perform key actions, such as liquidations, leading to potential protocol disruption.

### PoC

```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
   @> require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```

### Mitigation

Adjust the stale period based on the chain where the protocol is deployed. For example:

Even on the same chain, different `` pool assets`` can have different heartbeats (the period to update the price data on chain). For instance, the heartbeat for the [DAI / USD oracle on Ethereum](https://data.chain.link/ethereum/mainnet/stablecoins/dai-usd) is ~1 hour, whereas the heartbeat for the [USDT / USD oracle on the same chain](https://data.chain.link/ethereum/mainnet/stablecoins/usdt-usd) is ~24 hours.

Thus, I recommend using the mapping data type to record the staleness check parameter of each `` pool assets``  and setting each token's staleness check parameter with an appropriate stale period.

Furthermore, I also recommend adding a setter function for updating the stale period of each `` pool assets`` .