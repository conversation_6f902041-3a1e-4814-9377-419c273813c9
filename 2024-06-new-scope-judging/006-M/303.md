Polished Iris Antelope

High

# ```Pool``` expects all chainlink price feeds to have the same staleness thershold (1800 seconds) while this is not the case.

## Summary
Calls on a ```Pool``` regarding stablecoins will likely revert since most of them have staleness threshold bigger than 1800 seconds as per [docs](https://medium.com/cyfrin/chainlink-oracle-defi-attacks-93b6cb6541bf#:~:text=The%20staleness%20threshold%20should%20correspond,Heartbeat”%20column%20for%20each%20feed.).

## Vulnerability Detail
The ```getAssetPrice()``` function in the Pool contract assumes that all Chainlink price feeds have a staleness threshold of 1800 seconds. However, different price feeds can have varying thresholds, often exceeding 1800 seconds. This assumption can cause calls to revert when using price feeds with longer staleness thresholds. The function's requirement check ```(block.timestamp <= updatedAt + 1800)``` fails if the price feed is considered "stale" based on this hard-coded value, even though the feed might still be valid within its actual threshold. As a result, the protocol cannot handle price feeds that exceed the assumed staleness duration.

## Impact
The Zerolend protocol is unusable for price feeds with staleness threshold bigger than 1800 seconds.

## Code Snippet
Here is the vulnerable code snippet that thinks that all price feeds have 1800 seconds staleness theshold :
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158C1-L163C4)

## Tool used
Manual Review

## Recommendation
Considering implementing different stalness threshold for different price feeds.