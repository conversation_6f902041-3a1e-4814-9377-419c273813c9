Polite Spruce Duck

Medium

# Hardcoded stale duration for all assets breaks pricing logic making core functionalities un-processable


## Summary

The [stale check is currently hardcoded](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L162) for all integrated assets which breaks the logic for most assets as this value is too small _(30 minutes)_ and forces a DOS in the case where the price does not go over their Chainlink specified deviation threshold within the timeframe.

## Vulnerability Detail

First, it would be key to note that, from the README, protocol is to integrate multiple standard tokens: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/README.md#L14-L17

```markdown
### Q: If you are integrating tokens, are you allowing only whitelisted tokens to work with the codebase or any complying with the standard? Are they assumed to have certain properties, e.g. be non-reentrant? Are there any types of [weird tokens](https://github.com/d-xo/weird-erc20) you want to integrate?

Only standard ERC20 tokens + USDC and BNB are in-scope

---
```

Now take a look at https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L164

```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');//@audit
    return uint256(price);
  }

```

This function is used to get the price of an asset, and it gets that by querying Chainlink's `latestRoundData`. This function is used in order to cover any functionality that requires prices in protocol, for example we can see how it's being used to determine the amount of user's collateral that's liquidatable here: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L373

```solidity
  function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    AvailableCollateralToLiquidateLocalVars memory vars;

    vars.collateralPrice = collateralPrice; // oracle.getAssetPrice(collateralAsset);
    vars.debtAssetPrice = debtAssetPrice; // oracle.getAssetPrice(debtAsset);@audit see the flow in execLiquidationCall() to see how getAssetPrice() is used to get

    vars.collateralDecimals = collateralReserve.configuration.getDecimals();
    vars.debtAssetDecimals = debtReserveCache.reserveConfiguration.getDecimals();

    unchecked {
      vars.collateralAssetUnit = 10 ** vars.collateralDecimals;
      vars.debtAssetUnit = 10 ** vars.debtAssetDecimals;
    }

    // This is the base collateral to liquidate based on the given debt to cover
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
      vars.debtAmountNeeded = (
        (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }

    if (liquidationProtocolFeePercentage != 0) {
      vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
      vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);
      return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
    } else {
      return (vars.collateralAmount, vars.debtAmountNeeded, 0);
    }
  }
```

Now per the check here we can see how this value is hardcoded as 30 minutes for all assets: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161-L162

```solidity
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');

```

However, navigating to the [Chainlink's official documentation](https://docs.chain.link/data-feeds) on price feeds, we can see that for different tokens, there is a different heartbeat and deviational margin, where the heartbeat pertains to how long the price feed can stay without an update if the deviational threshold is not passed, and the heartbeat for feeds vary from `< 30 minutes` and in some instances even as high as `1/3 days`.

This then leads us to scenarios where:

- In the case the heartbeat of asset's Chainlink pricefeed is less than 30 minutes (say 15 minutes), then this means that the protocol is actually going to ingest stale data for this asset... _NB some feeds even have a heartbeat of seconds, i.e less than minutes, see this [USDC/USD Chainlink feed](https://data.chain.link/feeds/polygon/mainnet/usdc-usd)._
- And in the case the heartbeat of asset's Chainlink pricefeed is more than 30 minutes (this could be as high as 24..72 hours, multiple feeds have a 1 day heartbeat), then this means that the protocol is actually going to be DOS'd from attempts of querying price data, that's to say, consider the feed has a 24 hour heartbeat, the first 30 minutes after an update this price would be rightly ingested in protocol however for the remaining 23hr30 minutes, the attempt at querying prices would fail.

Now the second is even very practical to apply to Zerolend's case.

One of the special mentions of the tokens in scope is `BNB`, checking the official feed address for `BNB` here: https://docs.chain.link/data-feeds/price-feeds/addresses?network=ethereum&page=1&search=bnb we see this data provided:

```markdown
🟡BNB / USD
******************************************
Heartbeat: 86400s
Deviation: 1%
Decimals: 8
Asset name: BNB
Asset type: Crypto
Market hours: Crypto
```

This showcases in a market that the price of BNB in real time fluctuates `~0.99%` up and down, Chainlink never updates it within the day since the deviation is not crossed this would then mean that in Zerolend's `PoolGetters.sol` we can only access the price in the first 30 minutes out of the day and for 98% of the day the prices can never gotten.

## Impact

As explained in the last section of _Vulnerability Detail_, this leads to two bug cases depending on the specific heartbeat of the collateral:

### First bug case

- If collateral's (asset's) `heartbeat < 1800 seconds` then protocol is going to ingest stale data for this collateral.

### Second bug case

- If collateral's (asset's) `heartbeat > 800 seconds` then we'd encounter complete DOS at attempts of querying prices for the duration of `heartbeat - STALE_DATA_TIMEOUT`

---

Now one might want to argue the impact of the first case is subtle as the only way the price doesn't get updated is if the deviational threshold isn't passed, but that's untrue as this heavily depends on what the deviational threshold is and the percentage of this could be very high for some feeds, would be key to note that a deviational threshold of `1%` in real sense actually works as a deviation of `~1.99%` this is cause the price difference could go both ways (up/down) before the upgrade to be triggered, that's if the feed is actively working even and not stale, i.e consider this minimalistic scenario:

- A feed with a `60` minutes heartbeat and `1%` deviational threshold is integrated
- Price is updated to $1000 (In volatile market conditions, _not uncommon in the crypto world_ ).
- Market gets quite stable for the next few minutes, then price starts falling, price drops to `$991` within the next few minutes (assume in the 35th minute after the first upgrade), Chainlink doesn't update the feed as the deviational threshold has not been broken.
- Market gets quite unstable for the next few minutes, price heavily falls again, then the real value of the asset drops to `$980` within the next few minutes (assume in the 50th minute after the first upgrade), Chainlink's price should be updated since the deviation threshold has been broken, but it doesn't, which means that there is currently an arbitrage of `$2%` between the real value for asset and what's been reported by Chainlink and used in the protocol, since the heartbeat of the feed is 60 minutes, we have ~2-3 minutes of more time to _atleast_ still rely on Chainlink, this is the only option as there is no integration of systems that would happen without quite _a little bit_ of lag _(when working right, however in our case we are using the stale check to ensure when the integration goes wrong we can maximize protocol protection as much as possible)_.
- Price continues to drop, within the next 3 minutes, price goes to `$970`, Chainlink's price is still not updated, which is expected, cause we can conclude from the previous step that something is up with Chainlink's pricefeed since the price broke the deviational threshold and it wasn't updated, now the price integrated to protocol has a `3%` difference with the real world value, in the correct integrational setting, this should be curbed, since if a heartbeat of a feed is `15 minutes` then it would only be logically right for the stale duration to also be the heartbeat, as if the last price update is older than the duration then something is definitely up with price provider... Now any user can integrate with protocol using this price, i.e they could get liquidations processed with this, etc.
- However this doesn't stop above, [there is still an additional `15 minutes` in the current stale duration check](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L162) which means that if the price of the asset keeps on dropping within this 15 minutes, protocol is just going to blindly ingest it, **even worsening** the already stated effect on the pricing logic of the protocol.

> Possibility for the above scenarios can be considered arguable in normal market conditions, but valid arguments can be made that this is actually possible as the crypto world is quite volatile, now if we consider this scenario to be backed by a heavy market news as somewhat a catalyst, then the likelihood massively increases, and a user with malicious intent can position themselves to make the most out of the market impact of this news, they could try to make the most out of their deposited asset, i.e mint more tokens than should be backed by their collateral.

---

However, non-dependent to the first case, the issue from the second case strongly stands, cause this leads to non-availability of any functionality that requires the pricing logic for the timeframe, using the already stated example in the _Vulnerability Detail_, this means that **in every single day** of the protocol being active for `98%` of the day this functionality is **not** going to be available for some integrated assets, considering protocol have specifically even stated their support of `BNB` and it having a [heartbeat of 1 day for it's BNB/USD](https://docs.chain.link/data-feeds/price-feeds/addresses?network=ethereum&page=1&search=bnb) we can assume this is going to break protocol's functionality.

Would be key to note that the `_calculateAvailableCollateralToLiquidate()` is always queried when processing liquidations via: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L139-L148

```solidity
  function executeLiquidationCall(
    // ..snip
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
    // ..snip
}
```

---

> Considering both stated impacts, this is borderline Medium/High as this leads to not only elongated `98%` DOS over the lifetime of protocol for some assets but even the ingestion of stale prices for others which would lead to non-accurate collateral tracking being processed which might even end up proclaiming some accounts as unhealthy and liquidatable _(unfairly)_, or liquidating unfair amount of assets or even cause for bad debts to accrue in protocol due to untimed liquidations.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L162
- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L373

## Tool used

Manual Review

## Recommendation

Consider not hardcoding the stale timeout to 1800 seconds and instead set the timeout value of a particular pricefeed in an asset per asset basis, i.e this value shouldn't be hardcoded and instead it should be dependent on the asset's pricefeed configuration on Chainlink.

It'd also be very important to chose the duration for this stale check dependent on the heartbeat of the pricefeed that's been integrated, i.e there is not really a correct logical reasoning to setting the stale duration value to `2%` the heartbeat of a feed as is currently done.
