Brave Ruby Dinosaur

High

# `PoolGetters::getAssetPrice()` uses a constant heartbeat interval for all price feeds, which is highly dangerous

## Summary
`PoolGetters::getAssetPrice()` uses a constant heartbeat interval for all price feeds when checking if the data feed is fresh. The problem arises because the heartbeat interval varies across different price feeds. For instance, the [USDC/USD](https://data.chain.link/feeds/ethereum/mainnet/usdc-usd) oracle has a 24-hour heartbeat, while the average for most feeds is around 1 hour. By using a fixed heartbeat interval, the `getAssetPrice()` function will often incorrectly mark valid prices as stale, potentially rendering the contract nonfunctional.

## Vulnerability Detail
`PoolGetters::getAssetPrice()` sets a constant heartbeat interval of 30 minutes. However, the protocol interacts with various ERC20 tokens, including USDC. For the [USDC/USD](https://data.chain.link/feeds/ethereum/mainnet/usdc-usd)  oracle, the heartbeat is 24 hours. This mismatch causes the `getAssetPrice()` function to frequently consider valid prices as stale, leading to contract downtime. This issue affects other price feeds as well, due to the varying heartbeat intervals.
## Impact
insufficient staleness checks and makes the contract nonfunctional for a major period of time. 
## Code Snippet
```solidity
  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
@>  require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161
## Tool used
Manual Review
## Recommendation
use seperate heartbeat Interval for each price feed. 
