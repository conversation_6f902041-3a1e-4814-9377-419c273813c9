Joyous Cedar Tortoise

High

# getAssetPrice stale price threshold of 1800 is too small, this makes the protocol unusable

### Summary

The [`getAssetPrice`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163) function uses a constant stale price threshold of 1800 (30 minutes) for all reserve tokens.

Most tokens on chainlink have a stale price threshold of 86400 (24 hours).

For example on Linea every single token USDC, USDT, DAI, BTC has a Heartbeat of 86400s. Therefore for the 23.5 hours of the day any function call that calls [`getAssetPrice`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163) will revert because the pool wrongly assumes it is a stale price, when in reality the heartbeat has not been exceeded.

### Root Cause

_No response_

### Internal pre-conditions

_No response_

### External pre-conditions

reserve tokens have a threshold > 1800s (all of them on [Linea](https://docs.chain.link/data-feeds/price-feeds/addresses?network=linea&page=1#linea-mainnet) most of them on [mainnet](https://docs.chain.link/data-feeds/price-feeds/addresses?network=ethereum&page=1#ethereum-mainnet))

### Attack Path

_No response_

### Impact

Liquidations will revert during 23.5 hours of every day, this increases the risk of bad debt accumulation significantly 

Borrowing is not possible during 23.5 hours of every day

### PoC

_No response_

### Mitigation

set a stale price threshold for each token, use that in the getAssetPrice function