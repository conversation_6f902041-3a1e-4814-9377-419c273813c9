Clever Ebony Halibut

Medium

# Fixed Heartbeat Interval for Price Validation leads to potential issues

## Summary
The `getAssetPrice` function in the pool contract currently employs a fixed heartbeat interval to determine the freshness of price feeds. The heartbeat is currently set to a hardcoded value of 1800 seconds (30 minutes).
This approach does not account for varying heartbeat intervals for different asset pairs, leading to possible issues with stale or overly frequent price validations.
## Vulnerability Detail
The following snippet from `getAssetPrice` demonstrates the fixed heartbeat interval check:

```solidity

  function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
@>>    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
chainlink oracle could have different heartbeat interval for different asset pairs, and in this case, taking a fixed heartbeat interval of 1800 seconds will cause the price validation to be invalid for asset pairs with different heartbeat intervals.

## Impact
- **Denial of Service (DoS):** For heartbeats greater than 1800 seconds, many pool actions may fail due to perceived stale prices. This impacts assets with heartbeats up to 24 hours.
- **Stale Prices:** For assets with heartbeats less than 30 minutes, the contract may use outdated prices, leading to incorrect valuations and potential financial losses.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163
## Tool used

Manual Review

## Recommendation
- **Make the Heartbeat Configurable:** Consider using asset-specific heartbeat (e.g., ETH/USD has 1 hour heartbeat) and check against (block.timestamp - updatedAt).
For checking heartbeat intervals for various pairs, refer to Chainlink's price feed documentation: [Chainlink Price Feeds](https://docs.chain.link/data-feeds/price-feeds/addresses/?network=bnb-chain&page=1).