Kind Admiral Mongoose

Medium

# PoolGetters:getAssetPrice uses the same heartbeat for all feeds, which can cause the Chainlink oracle to revert most of the time

### Summary

When users borrow and liquidate in Pool.sol, the logic retrieves the asset price from Chainlink, and the function requires that block.timestamp <= updatedAt + 1800; otherwise, it considers the price stale. According to the details, only standard ERC20 tokens, USDC, and BNB are in scope. However, USDC/USD has a large heartbeat, it can cause the functions to revert most of the time.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161

The getAssetPrice function  require(block.timestamp <= updatedAt + 1800, 'Stale Price');

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L168

When users borrow, the validateBorrow function determines the BaseCurrency value from getAssetPrice

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L145-L146

When users liquidate, the executeLiquidationCall function determines the collateralAsset and debtAsset value from getAssetPrice



### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

The attached picture below shows the USDC/USD last update time was 22 hours ago, which is larger than the required 1800 seconds (30 mins).

https://data.chain.link/feeds/arbitrum/mainnet/usdc-usd

<img width="600" alt="Screenshot_usdc_usd" src="https://github.com/user-attachments/assets/c05e1b6a-af27-4102-8915-9b7f8b253e34">


### Impact

Cause the functions borrow and liquidate to revert most of the time, users are not able to borrow/liquidate

### PoC

Borrow: 

```solidity
  function test_borrowLastUpdate22hrs() public {
    _mintAndApprove(alice, tokenA, 20, address(pool));

    // Set the reserve factor to 1000 bp (10%)
    poolFactory.setReserveFactor(10_000);

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 20, 0);

    // Set new timestamp
    vm.warp(block.timestamp + 22 hours);

    // Borrow
    pool.borrowSimple(address(tokenA), alice, 1, 0);
  }
```

Result:
```solidity
[FAIL. Reason: revert: Stale Price] test_borrowLastUpdate22hrs() (gas: 391372)
```

 Liquidate:
```Solidity
  function test_liquidateLastUpdate22hrs() external {
    _generateLiquidationCondition();

    (, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);
    console.log("Alice's totalDebtBase:", totalDebtBase);

    // Set new timestamp
    vm.warp(block.timestamp + 22 hours);

    vm.startPrank(bob);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 0.000002 ether);
  }
```

Result:
```solidity
Alices totalDebtBase: ***********
[FAIL. Reason: revert: Stale Price] test_liquidateLastUpdate22hrs() (gas: 1021866)
```


### Mitigation

Use different heartbeat periods