Modern Mauve Badger

High

# The `getAssetPrice` Staleness check will result in doS for a lot of  oracles

### Summary

The protocol relies on Chainlink for price feeds and implements a `1800`-second timeout to ensure the price data remains fresh. If the last price update exceeds `1800` seconds, the protocol triggers a revert. This scenario is common because Chainlink typically updates the price feed every 24 hours unless there is a significant deviation beyond the oracle's defined threshold.


### Root Cause

1. The timeout check does not align with Chainlink's update interval.
2. The same timeout period is applied to all Chainlink oracles.
[Link Of code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163).


### Internal pre-conditions

_No response_

### External pre-conditions

The last Chainlink price update occurred more than `1800` seconds ago.


### Attack Path

1. Assume that the Chainlink price has not been updated for 40 minutes.
2. User Bob is eligible for liquidation.
3. <PERSON> calls the `liquidate` function to liquidate Bob.
4. The liquidation will fail because the Chainlink price stale check will cause a revert.


### Impact

This will affect operations such as borrowing and liquidation, as they cannot be executed if `1800` seconds have passed since the oracle's price was last updated.

### PoC

```solidity
function test_StalePrice() external {
uint256 now = 1725715605; // current time in sec fetch from Etherscan
uint256 updatedAt = 1725694343; // at time of wrting this POC lastUpdate time of BNB/USD contract address : `******************************************` and roundId : `73786976294838206975`
console.log(1725694343+1800 , now);
require(now <= updatedAt + 1800, 'Stale Price');
}

```
Add above test to any foundry test file and run with command : `forge test --mt test_StalePrice`


### Mitigation

Use an appropriate stale price check because Chainlink provides different update intervals for each oracle. The current price will only be valid for 30 minutes; after that, it will start reverting until there is another update from Chainlink.
