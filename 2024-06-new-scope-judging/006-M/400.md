Rare Brunette Rabbit

Medium

# PoolGetters::getAssetPrice uses default heartbeat for all the reserves

### Summary

The getAssetPrice function in the PoolGetters contract relies on a default Chainlink heartbeat for all the reserves in the pool. This poses a significant risk to the accuracy of the asset price feeds, as different assets might require customized heartbeat settings based on their volatility, liquidity, and market conditions. The use of a default heartbeat across all reserves can lead to outdated or incorrect price data for specific assets, especially during periods of high volatility or market fluctuations.

### Root Cause

```soldidity
function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
 @>   require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L158C3-L163C4

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The protocol will always revert for reserves with a higher heartbeat than the hardcoded value in the getAssetPrice function.

### PoC

_No response_

### Mitigation

_No response_