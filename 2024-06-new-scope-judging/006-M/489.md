Modern Garnet Cyborg

Medium

# Strict price freshness check will block important state operations

### Summary

Strict oracle price freshness check will cause important protocol operations to revert.

### Root Cause

In [`PoolGetters.sol:161`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161) oracle price freshness check is set to 30 mins which may be too strict for most of the price feed. As a result, common core operations will often revert when they should not.

### Internal pre-conditions

_No response_

### External pre-conditions

- Price feed oracle hearbeat is larger than 30 minutes. For example check price feed for [ETH/USD]( https://data.chain.link/feeds/ethereum/mainnet/eth-usd)
- Price feed oracle price change threshold has not been triggered in the meantime.

### Attack Path

When price oracle happens more than 30 minutes ago and less than 1 hour ago core system operation will start failing. For example [LiquidationLogic.executeLiquidationCall()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L145) will revert when calling `IPool(params.pool).getAssetPrice(params.collateralAsset)`. As a result, system will not be able to operate properly in these circumstances. 

### Impact

_No response_

### PoC

_No response_

### Mitigation

Relax the Stale Price check to better accommodate the update mechanics of the price oracle. Consider adjusting the time window to align with the price update frequency based on the deviation threshold and heartbeat interval. For example, using a longer time window (e.g., 1 hour or more) may provide a better balance between ensuring data freshness and avoiding unnecessary reverts.

Proposed modification:

```diff
function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
-   require(block.timestamp <= updatedAt + 1800, 'Stale Price');
+   require(block.timestamp <= updatedAt + acceptableTimeWindow, 'Stale Price');
    return uint256(price);
}
```

Where acceptableTimeWindow should be set to a value that reflects the heartbeat interval or a reasonable multiple thereof to prevent unnecessary staleness rejections.