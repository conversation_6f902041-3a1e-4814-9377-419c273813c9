Scruffy Dijon Chinchilla

Medium

# `ZEROLEND` protocol can consume stale price data or cant operate on some EVM chains

### Summary

In the contest README it is explained that ZEROLEND will be deployed on Ethereum mainnet and Linea primarily followed by EVM-compatible networks.

> **On what chains are the smart contracts going to be deployed?** 
Ethereum and Linea primarily. And after that any EVM-compatible network.
> 

Each chain has a different heartbeat for each asset (even though the same asset, i.e. `ETH / USD`). But the `ZEROLEND` protocol uses the same heartbeat for each chain, which is `1800` (`30 minutes`). This can cause the protocol to use stale price or not work on some chains because the `getAssetPrice` function will revert due to stale price error.

### Root Cause

In [PoolGetters.sol:161](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L161) using same heartbeat (`1800 second` /  `30 minutes`)

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

`ZEROLEND` protocol can consume stale price data or cant operate on some EVM chains

### PoC

Let's use `ETH/USD` as an example asset, heartbeat for each chains :

1. Ethereum, the oracle will update price feed every [1 hour](https://data.chain.link/feeds/ethereum/mainnet/eth-usd)
2. Linea, the oracle will update price feed every [24 hour](https://data.chain.link/feeds/linea/mainnet/eth-usd)
3. Optimism, the oracle will update price feed every [20 minutes](https://data.chain.link/feeds/optimism/mainnet/eth-usd)

By using heartbeat = `1800 s / 30 minutes` for all chains, this will cause problems :

1. On Ethereum, `getAssetPrice` function will revert with `‘Stale Price’` message because price feed will update every 1 hours
2. On Linea, `getAssetPrice` function will revert with `‘Stale Price’` message because price feed will update every 24 hours
3. On Optimism, `getAssetPrice` function will consume stale price data because price feed will update every 20 minutes

### Mitigation

Consider adding heartbeat mapping for each asset and add a setter function for each stale period of each asset