Joyous Cedar Tortoise

High

# Inconsistent rounding of rayMul() leads to failed repayment of tokens

### Summary

_No response_

### Root Cause

Within [NFTPositionManagerSetters._repay()](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L123-L125), the following check exists to ensure that the correct amount is repaid:

```solidity
if (previousDebtBalance - currentDebtBalance != repaid.assets) {
		revert NFTErrorsLib.BalanceMisMatch();
}
```
The issue is that `previousDebtBalance` and `currentDebtBalance`  are calculated using `rayMul()`

However, `rayMul()` rounds to the nearest integer, and this means that `previousDebtBalance` may have been rounded differently to `currentDebtBalance`, since the rounding direction depends on the number.

As a result, `previousDebtBalance - currentDebtBalance` can be off by 1 relative to `repaid.assets`

This causes the check to revert, so repayment fails.

### Internal pre-conditions

The PositionBalanceConfiguration.getDebtBalance()  calculation should be fixed, to do the same calculation as ReserveSuppliesConfiguration.getDebtBalance(). This issue was reported in a separate submission

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Users cant repay so collateral is stuck

### PoC

First, fix the unrelated issue in PositionBalanceConfiguration.getDebtBalance(), by replacing it with the calculation in ReserveSupplyConfiguration.getDebtBalance():
```diff
function getDebtBalance(DataTypes.PositionBalance storage self, uint256 index) internal view returns (uint256 debt) {
    uint256 increase = self.debtShares.rayMul(index) - self.debtShares.rayMul(self.lastDebtLiquidtyIndex);

-   return self.debtShares + increase;
+   return self.debtShares.rayMul(index);
}
```

<details><summary>Optionally add these console logs in NFTPositionManagerSetters._repay()</summary>
```diff
+console.log("previousDebtBalance: %e", previousDebtBalance);
+console.log("currentDebtBalance:  %e", currentDebtBalance);
+console.log("repaid.assets:       %e", repaid.assets);

if (previousDebtBalance - currentDebtBalance != repaid.assets) {
    revert NFTErrorsLib.BalanceMisMatch();
}
```
</details>

<details><summary>Foundry test</summary>

```solidity
function testJ_repayFails() external {
        testShouldSupplyAlice();
        uint256 repayAmount = 10 ether;
        uint256 borrowAmount = 20 ether;
        DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
        INFTPositionManager.AssetOperationParams memory params =
            INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 1, data);

        vm.startPrank(alice);
        nftPositionManager.borrow(params);
        assertEq(tokenA.balanceOf(address(pool)), 30 ether);
        assertEq(tokenA.balanceOf(alice), 70 ether);

        vm.warp(block.timestamp + 500000);
        pool.forceUpdateReserves();
        params.amount = repayAmount;
        vm.expectRevert();
        nftPositionManager.repay(params);
        vm.stopPrank();
    }
```
</details>

<details><summary>Foundry test</summary>

```bash
previousDebtBalance: 2.0018899864077625575e19
currentDebtBalance:  1.0018899864077625576e19
repaid.assets:       1e19
```
</details>



### Mitigation

Change the check to this:
```solidity
if (diff(previousDebtBalance - currentDebtBalance, repaid.assets) > 1) {
		revert NFTErrorsLib.BalanceMisMatch();
}

function diff(uint256 a, uint256 b) internal pure returns(uint256 c) {
	c = a > b ? a - b : b - a
}
```
This ensures that if the rounding direction of the two debt balances is different, the repayment can still occur succesfully.