Decent Walnut Squirrel

Medium

# The protocol allows small debt of position that can create bad debt.

## Summary
The ZeroLend protocol allows small debt of position. With a small borrowing position, there is no incentive for a liquidator to liquidate the position, as the liquidation profit may not cover the liquidation cost(gas). As a result, small liquidatable positions will not be liquidated, leaving bad debt to protocol.

## Vulnerability Detail
`BorrowLogic.sol#executeBorrow()` function which is called when borrow is as follows.
```solidity
  function executeBorrow(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteBorrowParams memory params
  ) public returns (DataTypes.SharesType memory borrowed) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);

    reserve.updateState(params.reserveFactor, cache);

    ValidationLogic.validateBorrow(
      _balances,
      reservesData,
      reservesList,
      DataTypes.ValidateBorrowParams({
        cache: cache,
        userConfig: userConfig,
        asset: params.asset,
        position: params.position,
        amount: params.amount,
        reservesCount: params.reservesCount,
        pool: params.pool
      })
    );

    // mint debt tokens
    DataTypes.PositionBalance storage b = _balances[params.asset][params.position];
    bool isFirstBorrowing;
    (isFirstBorrowing, borrowed.shares) = b.borrowDebt(totalSupplies, params.amount, cache.nextBorrowIndex);
    cache.nextDebtShares = totalSupplies.debtShares;

    // if first borrowing, flag that
    if (isFirstBorrowing) userConfig.setBorrowing(reserve.id, true);

    ...

    IERC20(params.asset).safeTransfer(params.destination, params.amount);

    emit PoolEventsLib.Borrow(params.asset, params.user, params.position, params.amount, reserve.borrowRate);

    borrowed.assets = params.amount;
  }
```
As we can see above, there is no check of minimum debt amount of a position.   
And small borrowing amount can happen through repaying and liquidating, too.

## Impact
Small debt positions can be not liquidated, leaving bad debt to protocol.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51

## Tool used

Manual Review

## Recommendation
To mitigate this problem, implement the minimum borrowing amount check to limit the minimum borrowing size of position when borrowing, repaying and liquidating.