Powerful Licorice Elephant

Medium

# The protocol allows borrowing small amounts that can create bad debt.

## Summary
as  there is no check for borrowing minimum amounts, Attackers can borrow multiple small amounts of positions which may not be profitable during liquidation. As there is also no check to keep minimum debt during liquidation so  liquidators may keep small debt by paying almost  full debt amounts of a position , so that those small debts may not become profitable for other liquidators.

## Vulnerability Detail
the protocol allows users to borrow small amounts.
See function borrow, there is no check for borrowing minimum amounts.
See function repay, when full debt is not repaid, there is no check to keep minimum debt.
See liquidate function, when liquidating, debt is repaid, if full debt is not repaid during liquidation, then there is also no check to keep minimum debt.


With a small borrowing amount(for a position debt asset) ,there is no incentive for the liquidator to liquidate the small debt amounts. As the liquidation profit may not cover the liquidation cost(gas). As a result, small liquidable position’s debt amount will not be liquidated , leaving bad debt to the protocol. 

## Impact
liquidators may keep small debt by paying almost  full debt amounts of a position , so that those small debt may not become profitable for other liquidators. Attackers can borrow multiple small amounts of positions which may not be profitable during liquidation.


## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51
## Tool used

Manual Review

## Recommendation
Fixed a minimum borrowing amounts.

