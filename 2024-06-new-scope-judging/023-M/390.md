Brave Ruby Dinosaur

Medium

# The protocol allows borrowing small positions that can create bad debt.

## Summary
In the `BorrowLogic` library, the `executeBorrow` function lacks a check for the minimum borrow amount, which allows for the creation of small debt positions that can lead to insolvency. When a small borrowing position is created, there is little incentive for a liquidator to liquidate it, as the potential liquidation profit may not cover the associated costs (such as gas fees). As a result, small liquidatable positions may remain unliquidated, resulting in bad debt for the protocol.

## Vulnerability Detail
During the liquidation process of small debts, the liquidator may receive less than the cost incurred for executing the liquidation, making it financially unviable to liquidate such positions.
## Code Snippet

```solidity
function executeBorrow(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteBorrowParams memory params
  ) public returns (DataTypes.SharesType memory borrowed) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);

    reserve.updateState(params.reserveFactor, cache);

    ValidationLogic.validateBorrow(
      _balances,
      reservesData,
      reservesList,
      DataTypes.ValidateBorrowParams({
        cache: cache,
        userConfig: userConfig,
        asset: params.asset,
        position: params.position,
        amount: params.amount,
        reservesCount: params.reservesCount,
        pool: params.pool
      })
    );

    // mint debt tokens
    DataTypes.PositionBalance storage b = _balances[params.asset][params.position];
    bool isFirstBorrowing;
    (isFirstBorrowing, borrowed.shares) = b.borrowDebt(totalSupplies, params.amount, cache.nextBorrowIndex);
    cache.nextDebtShares = totalSupplies.debtShares;

    // if first borrowing, flag that
    if (isFirstBorrowing) userConfig.setBorrowing(reserve.id, true);

    reserve.updateInterestRates(
      totalSupplies,
      cache,
      params.asset,
      IPool(params.pool).getReserveFactor(),
      0,
      params.amount,
      params.position,
      params.data.interestRateData
    );

    IERC20(params.asset).safeTransfer(params.destination, params.amount);

    emit PoolEventsLib.Borrow(params.asset, params.user, params.position, params.amount, reserve.borrowRate);

    borrowed.assets = params.amount;
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51
## Tool used

Manual Review

## Recommendation

Include the check for minmal borrowable amount.

