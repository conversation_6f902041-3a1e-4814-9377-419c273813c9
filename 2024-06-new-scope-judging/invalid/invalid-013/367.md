Big Mandarin Bison

Medium

# Use safeTransfer instead of transfer

## Summary
The reward distribution mechanism uses `transfer` in sending ERC20 rewards to the users. The `transfer()` method does not however handle some `weirdERC20token` that do not revert on failure.  
## Vulnerability Detail
The use of the `transfer` method in the protocol will not handle silent reverts properly
## Impact
Silent failures of transfers will affect token accounting in contracts within the protocol.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L93
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125
## Tool used

Manual Review

## Recommendation
Use OpenZeppelin’s `safeTransfer`/`safeTransferFrom` consistently to handle all such wierd tokens that do not return bool.