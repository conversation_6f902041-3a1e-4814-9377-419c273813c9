Special Velvet Toad

Medium

# Protocol is incompatible with BNB

### Summary

Protocol is incompatible with BNB due to outdated token transfer method.

### Root Cause

[Link 1](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L93)
[Link 2](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L126)
```solidity
function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
    _updateReward(tokenId, _assetHash);
    reward = rewards[tokenId][_assetHash];
    if (reward > 0) {
      rewards[tokenId][_assetHash] = 0;
      rewardsToken.transfer(ownerOf(tokenId), reward);       <<<
      emit RewardPaid(tokenId, ownerOf(tokenId), reward);
    }
  }

function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
    rewardsToken.transferFrom(msg.sender, address(this), reward);       <<<
    //...
  }
```

### Internal pre-conditions

Admin initializes `NFTRewardsDistributor.sol` with `rewardsToken` as BNB.
>Are there any types of [weird tokens](https://github.com/d-xo/weird-erc20) you want to integrate?
Only standard ERC20 tokens + USDC and BNB are in-scope.

### External pre-conditions

None

### Attack Path

None

### Impact

Some tokens do not return a bool (e.g. BNB) on ERC20 methods, and this will lead `getReward()` and `notifyRewardAmount()` to a revert, as it expects a boolean return:
```solidity
function transfer(address to, uint256 value) public virtual returns (bool) {
        address owner = _msgSender();
        _transfer(owner, to, value);
        return true;
    }
```

### PoC

_No response_

### Mitigation

Consider using `safeTransfer()` instead of `transfer()`.