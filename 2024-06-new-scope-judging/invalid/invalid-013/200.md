Joyous Cedar Tortoise

Medium

# BNB cannot be used as a reward token in NFTRewardsDistributor because it does not implement the IERC20 interface

## Summary

## Vulnerability Detail
The `NFTRewardsDistributor` uses `IERC20` to interact with the `rewardsToken` contract. However if the `rewardsToken` is BNB, it is not compatible with the `IERC20` interface, since it does not return a bool on transfer.

This means that when calling `getReward()`, and it calls:

```solidity
rewardsToken.transfer(ownerOf(tokenId), reward);
```

It will revert when decoding the return data from calling the BNB contract.

## Impact
Using BNB as the reward token will lead to a non functional contract

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L93

## Tool used

Manual Review

## Recommendation
Use the SafeERC20 library within NFTRewardsDistributor, just like how it is used in other contracts in this protocol. 