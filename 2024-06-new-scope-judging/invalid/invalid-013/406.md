<PERSON><PERSON><PERSON> <PERSON>jon Chinchilla

Medium

# `rewardsToken.transfer()` and `transferFrom()` will always revert for token that not return boolean value

### Summary

Based on README there is `BNB` as a token in scope and there is no clear explanation of what tokens can be rewards. In addition, there is a comment on the codebase in the `NFTRewardsDistributor` contract that mentions multiple rewards.

> *@notice Accounting contract to manage multiple staking distributions with multiple rewards*
> 

In this case, if `BNB` is used as a rewards token, the `rewardsToken.transfer()` and `rewardsToken.transferFrom()` functions will always fail because `BNB` does not have a boolean return but `IERC20` will try to parse bool.

Note

This could also happen if `USDT` is used as a rewards token considering that `USDT` is a familiar and widely adopted token.

### Root Cause

In [NFTRewardsDistributor.sol:93](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L93) use `transfer()` 

In [NFTRewardsDistributor.sol:126](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L126) use `transferFrom()`

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

`rewardsToken.transfer()` and `rewardsToken.transferFrom()` will always revert for token that not return boolean value

### PoC

1. In the `__NFTRewardsDistributor_init()` function `rewardsToken` use `IERC20` , this way the `transfer()` and `transferFrom()` functions will try to parse bool

```solidity
  function __NFTRewardsDistributor_init(
    uint256 maxBoostRequirement_,
    address staking_,
    uint256 rewardsDuration_,
    address rewardsToken_
  ) internal onlyInitializing {
    maxBoostRequirement = maxBoostRequirement_;
    stakingToken = IVotes(staking_);
    rewardsToken = IERC20(rewardsToken_);
    rewardsDuration = rewardsDuration_;
  }
```

2. `transfer()` and `transferFrom()` will always revert for `BNB`, `USDT` as `rewardsToken`

### Mitigation

Use `safeTransferFrom()` instead of `transfer()` and `transferFrom()`