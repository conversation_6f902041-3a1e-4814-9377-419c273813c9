Able Concrete Jellyfish

Medium

# Unvalidated Asset Address in Reserve Configuration Functions

## Summary
The `setReserveBorrowing` function, along with other reserve configuration functions in the `PoolConfigurator` contract, lacks input validation for the asset address parameter. This omission can lead to unintended operations on invalid or incorrect asset addresses, potentially affecting the integrity and security of the lending pool's configuration.

## Vulnerability Detail
```solidity
  function setReserveBorrowing(IPool pool, address asset, bool enabled) external onlyPoolAdmin(pool) {
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    config.setBorrowingEnabled(enabled);
    pool.setReserveConfiguration(asset, address(0), address(0), config);
    emit ReserveBorrowing(asset, enabled);
  }
```
Scenario:
- Call `setReserveBorrowing` with `asset` set to `address(0)`.
- The function will attempt to modify the configuration for a non-existent reserve, which could lead to unexpected behavior or errors.

## Impact
Could lead to system malfunctions or disruptions in lending operations.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L59-L64

## Tool used

Manual Review

## Recommendation
Implement input validation to ensure that the asset address is valid and non-zero before proceeding with any configuration changes. 
```diff
  function setReserveBorrowing(IPool pool, address asset, bool enabled) external onlyPoolAdmin(pool) {
+   require(asset != address(0), "Invalid asset address");
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    config.setBorrowingEnabled(enabled);
    pool.setReserveConfiguration(asset, address(0), address(0), config);
    emit ReserveBorrowing(asset, enabled);
  }
```