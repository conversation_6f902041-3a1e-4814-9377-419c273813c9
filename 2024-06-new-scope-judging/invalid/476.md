Expert Arctic Mallard

High

# Lack of Flashloan Repayment Validation in `FlashLoanLogic`

### Summary

In `FlashLoanLogic.sol` at line [65](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L65), the `executeFlashLoanSimple` function does not validate whether the repayment of the flashloan is successful before proceeding.

### Root Cause

In `FlashLoanLogic.sol` at line [65](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L65), the function `executeFlashLoanSimple` transfers the loan amount to the receiver and calls `executeOperation` without checking if the repayment has been properly received. The `_handleFlashLoanRepayment` function assumes that repayment will always succeed, but there is no mechanism to confirm this.

### Internal pre-conditions

1. The contract must be called by a user or an external contract.
2. There should be an ongoing flashloan transaction.

### External pre-conditions

1. The contract must have received the flashloan amount plus the premium.

### Attack Path

1. An attacker initiates a flashloan using the `executeFlashLoanSimple` function.
2. The attacker manipulates the `executeOperation` function to fail or provide incorrect repayment.
3. Since there is no validation on repayment, the function `_handleFlashLoanRepayment` is called, assuming repayment was successful.
4. The protocol suffers financial loss if the repayment is not properly handled.

### Impact

Failure to validate the repayment of the flashloan could result in financial loss for the protocol if the amount borrowed and the associated premium are not returned correctly.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "forge-std/Test.sol";
import "../FlashLoanLogic.sol";

contract TestRepaymentValidation is Test {
    FlashLoanLogic flashLoanLogic;

    function setUp() public {
        flashLoanLogic = new FlashLoanLogic();
    }

    function testRepaymentFailure() public {
        // Simulate a flashloan operation where repayment fails
        flashLoanLogic.executeFlashLoanSimple(...); // Set up a condition where repayment fails
    }
}
```

### Mitigation

Implement a validation mechanism to confirm that the repayment of the flashloan, including the premium, is received correctly. Ensure that `_handleFlashLoanRepayment` verifies the repayment status before proceeding.