Cheerful Blue Meerkat

Medium

# Received E<PERSON> will remain stuck in the NFTPositionManager contract

### Summary

`NFTPositionManager` contract accepts <PERSON><PERSON> through few entry points which are not consumed during the flow will remain stuck in the `NFTPositionManager` contract.

Below are the entry points

a) receive() function
b) borrowETH() function is marked as payable and hence can potentially accept Ether which is not consumed during the flow
c) withdrawETH() function

### Root Cause

In `NFTPositionManager.sol`, below functions accept ether, but these functions dont consume E<PERSON> during the call flow and hence if <PERSON><PERSON> was sent will be stuck in the `NFTPositionManager` contract.

a) receive() function
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L53-L55

b) borrowETH() function
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L88-L96

c) withdrawETH() function
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L105-L113



### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

As these are functions, the caller can by mistake pass ether to these functions which are then locked in the contract. There is no provision to withdraw such ether from the contract by the contract owner as well.

This should be prevented.

### PoC

_No response_

### Mitigation

a) Remove payable operator from borrowETH() function
b) Remove payable operator from withdrawETH() function
c) Restrict the address/addresses from where Ether can be received in receive() function.