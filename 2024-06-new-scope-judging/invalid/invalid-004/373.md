Curly Pineapple Armadillo

Medium

# Reward distribution is lost if users are yet to supply assets to `NFTPositionManager`

### Summary

Reward distributions in `NFTRewardsDistributor` will be lost if no user has supplied assets to `NFTPositionManager`. This occurs as rewards, distributed while `totalSupply == 0`, are not redistributed once new reward periods begin.
It is important to note that sweeping undistributed rewards from previous reward periods will not be an efficient fix as current reward distributions will also be swept.

### Root Cause

- In [`notifyRewardAmount`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125) rewards distributed whilst `totalSupply` was 0 are not included as leftover when computing the next reward rate.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. 3 ETH are distributed as rewards at a reward rate of 1 ETH per day.
2. 1 day passes but `totalSupply` is still 0.
3. At the beginning of the second day, a user supplies assets.
4. Once the 3 days are over the supplier is rewarded 2 ETH, and the 1 ETH distributed in the first day remains in the contract (this happens because of this [part of the code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L78-L80)).
5. A new reward period begins, but the 1 ETH of undistributed rewards is not included when calculating the new reward rate.

### Impact

Rewards may remain stuck in the `NFTPositionManager` contract.

### PoC

_No response_

### Mitigation

Include undistributed rewards in upcoming reward periods (similar to how the [`leftover`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L134-L136) variable is used to redistribute rewards when a reward period is ended before its `periodFinish`).