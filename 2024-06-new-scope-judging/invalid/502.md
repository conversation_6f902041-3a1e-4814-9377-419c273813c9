Expert Arctic Mallard

High

# Supply Cap Bypass Due to Incorrect Calculation in `validateSupply`

### Summary

In `ValidationLogic.sol` at line [70](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L70), the `validateSupply` function performs a check to enforce the supply cap but contains a vulnerability due to an incorrect calculation, which could allow malicious actors to bypass the cap.

### Root Cause

In `ValidationLogic.sol` at line [70](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L70), the function `validateSupply` checks if the total supply, including accrued shares, does not exceed the supply cap. However, the calculation for the total supply (`totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)`) combined with `rayMul(cache.nextLiquidityIndex)` might be incorrect or subject to rounding issues. This flaw could allow users to supply more tokens than the intended cap.

### Internal pre-conditions

1. The contract must be interacted with by a user or an external contract.
2. There should be an ongoing supply action.

### External pre-conditions

1. The contract must have an active supply cap configuration.
2. The external oracle providing asset price data must be updated to reflect accurate pricing, ensuring the cap calculations are valid.
3. The gas price should be within a reasonable range to prevent exploitation due to high transaction costs.

### Attack Path

1. An attacker initiates a supply action that would normally exceed the supply cap.
2. Due to the faulty calculation in `validateSupply`, the supply cap might not be correctly enforced.
3. The attacker successfully supplies more tokens than allowed, leading to a breach of the cap.

### Impact

This vulnerability could lead to unintended supply of tokens beyond the predefined cap, potentially causing issues with liquidity, token distribution, and economic stability of the protocol. The supply cap is a critical control for maintaining system integrity and preventing inflation.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "forge-std/Test.sol";
import "../ValidationLogic.sol";

contract TestSupplyCapBypass is Test {
    ValidationLogic validationLogic;

    function setUp() public {
        validationLogic = new ValidationLogic();
    }

    function testSupplyCapBypass() public {
        // Set up conditions to bypass supply cap
        // Call validateSupply with parameters that would trigger the bypass
        validationLogic.validateSupply(...); // Trigger bypass
    }
}
```

### Mitigation

Review and correct the supply cap calculation in the `validateSupply` function. Ensure that the calculation for `totalSupplies` and the application of `rayMul` are accurate and that the supply cap is strictly enforced to prevent any possible bypass.