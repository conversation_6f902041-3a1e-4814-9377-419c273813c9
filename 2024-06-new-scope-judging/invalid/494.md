Massive Glass Crane

Medium

# Pool.sol :: repay() doesnt allow an approved address to repay onBehalfOf the owner deviating from the doc.

### Summary

functions like withdraw() and borrow() allows to define a target address where the assets need to be sent finally.

But the same property is not applied to repay() and hence an approved address cant repay on the behalf of Owner. 

If the owner decided to transfer the borrowed amount to an external contract , hoping the borrowed amount can be repaid using the same contract  by providing the to address as the owner itself , the protocol doesnt allow. Even though it is specifically mentioned as a feature in the [natspec](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/interfaces/pool/IPoolSetters.sol#L110-L112).


### Root Cause

repay() doesnt have the input parameter to define the `to` address where `to` denotes the actual owner of the position.

This doesnt allow an approved address to repay the borrowed amount onBehalfOf the owner.

But in the natspec protocol is expecting the ame feature where a user can repay the owners position on behalf of the owner like borrow().[doc](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/interfaces/pool/IPoolSetters.sol#L110-L112).

[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L115-L122)
```solidity
  function repay(
    address asset,
    uint256 amount,
    uint256 index,
    DataTypes.ExtraData memory data
  ) public returns (DataTypes.SharesType memory) {
    return _repay(asset, amount, msg.sender.getPositionId(index), data);
  }
  ```
  here only the owner of the position can repay his debt.
### Internal pre-conditions
_No response_


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Deviating from the natspec provided , the protocol doesnt allow an approved address to repay a position onbehalfOf its owner. If the owner decided to transfer the amount he borrows to an external COntract owned by him expecting he could repay his position using the same contract , he is denied from executing the transaction and will be liquidated eventually.

### PoC


### Mitigation
```solidity
  function repay(
    address asset,
    uint256 amount,
    address to,
    uint256 index,
    DataTypes.ExtraData memory data
  ) public returns (DataTypes.SharesType memory) {
    return _repay(asset, amount, to.getPositionId(index), data);
  }
  ```
