Chilly Cherry Deer

High

# The lack of check if the user is already using any reserves as collateral in  `validateUseAsCollateral` potential for users to over-leverage their positions could lead to liquidations and loss of funds

### Summary

The current implementation of the `validateUseAsCollateral` function focuses on the reserve's LTV ratio, `neglecting to account for whether the user is already using any other reserves as collateral`,  lead to situations where users over-leverage their positions by setting reserves as collateral based solely on LTV, without considering their existing collateral usage.

- [ValidationLogic.sol#L286-L288](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L286-L288)

```solidity
function validateUseAsCollateral(DataTypes.ReserveConfigurationMap memory reserveConfig) internal pure returns (bool) {
    return reserveConfig.getLtv() > 0;
  }
```
- The function returns true if `reserveConfig.getLtv() > 0`, indicating that the reserve can be used as collateral.
- `The function does not check if the user is already using any reserves as collateral, which could be relevant for assessing the user's overall collateral strategy.`

Example:
1. A user tries to set a reserve as collateral.
2. The `validateUseAsCollateral` function checks only the LTV ratio.
3. Now collateral is set, so the reserve is allowed as collateral based solely on its LTV, without considering the user's existing collateral usage.
4. Leads users to over-leverage their positions could lead to liquidations and loss of funds.

### Root Cause

In [ValidationLogic.sol#L286-L288](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L286-L288) the `validateUseAsCollateral` function only checks if the reserve's Loan-to-Value (LTV) ratio is greater than zero, without considering whether the user is already using any reserves as collateral.


### Impact

- The lack of check if the user is already using any reserves as collateral in  `validateUseAsCollateral` potential for users to over-leverage their positions could lead to liquidations and loss of funds.


### Mitigation

- add check if the user is already using any reserves as collateral. Here's a possible approach:
```solidity
  function validateUseAsCollateral(
     // @audit Add this line 
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.ReserveConfigurationMap memory reserveConfig
  ) internal view returns (bool) {
   // Check if the reserve's LTV is greater than zero
    if (reserveConfig.getLtv() == 0) {
      return false;
    }
     // Check if the user is already using any reserves as collateral
  // @audit add this check 
    if (!userConfig.isUsingAsCollateralAny()) {
      return true;
    }
```
- Now, The function will not only check if the reserve's Loan-to-Value (LTV) ratio is greater than zero but will also consider whether the user is already using any reserves as collateral, prevent users to over-leverage their positions.
- `isUsingAsCollateralAny()` logic is in `UserConfiguration.sol` : [UserConfiguration.sol#L121-L123](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/UserConfiguration.sol#L121-L123)
 
 ```solidity
 /**
   * @notice Checks if a user has been supplying any reserve as collateral
   * @param self The configuration object
   * @return True if the user has been supplying as collateral any reserve, false otherwise
   */
  function isUsingAsCollateralAny(DataTypes.UserConfigurationMap memory self) internal pure returns (bool) {
    return self.data & COLLATERAL_MASK != 0;
  }
```
