Cheerful Flaxen Hedgehog

High

# Malicious RateStrategy Attack

### Summary

Anyone can create a Pool with an  malicious RateStrategy contract as `RateStrategyAddress`.

### Root Cause

Anyone can create a Pool with an arbitrary contract as `RateStrategyAddress`.

These conditions allow the following attack:
1.malicious actor creates a Pool and mints some shares

2.malicious actor  [set](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L105) malicious reserve interest rate strategy address,

3.users mints some shares,
The malicious actor calls forceUpdateReserve on the Pool and the malicious  interest rate strategy returns that a lot of tokens in interest were accrued
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L118

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L127

4.The malicious actor withdraws all funds in the Pool with the few shares minted in the beginning and the users of the Pool lose everything

### Internal pre-conditions

_No response_

### External pre-conditions

malicious actor  set malicious reserve interest rate strategy address

### Attack Path

_No response_

### Impact

Users suffer losses due to malicious RateStrategy 

### PoC

_No response_

### Mitigation

Do not allow arbitrary RateStrategy contract as RateStrategyAddress