Expert Arctic Mallard

High

# Attacker will exploit the reentrancy vulnerability in `_burnCollateralTokens`, affecting the protocol and its users.

### Summary

The reentrancy risk in `_burnCollateralTokens` will cause a potential loss of funds for the protocol as an attacker can exploit the transfer function to re-enter and manipulate the state before updates are finalized.


### Root Cause

In `LiquidationLogic.sol` at line [174](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L174), the `_burnCollateralTokens` function performs state changes and fund transfers without a proper reentrancy guard.

### Internal pre-conditions

1. The liquidator needs to call `executeLiquidationCall` and execute the `_burnCollateralTokens` function.
2. The collateral reserve and user balance must be updated correctly prior to the `safeTransfer` call.

### External pre-conditions

1. `msg.sender` needs to be a malicious contract that can exploit the reentrancy vulnerability.

### Attack Path

1. The attacker calls `executeLiquidationCall` and successfully executes `_burnCollateralTokens`.
2. The state is updated, and then the collateral tokens are transferred to `msg.sender`.
3. The attacker’s contract re-enters the function, exploiting the timing of the transfer to manipulate the state or perform unauthorized actions.

### Impact

The protocol suffers a potential loss of collateral tokens due to reentrancy attacks. The attacker gains unauthorized access to additional funds or can disrupt the liquidation process.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract Malicious {
    address public target;
    IERC20 public collateralToken;

    constructor(address _target, address _collateralToken) {
        target = _target;
        collateralToken = IERC20(_collateralToken);
    }

    function attack() public {
        // Code to exploit the reentrancy vulnerability
        // Call the target contract's liquidation function
    }

    fallback() external {
        // Re-enter the target contract to exploit the vulnerability
    }
}
```

### Mitigation

Move external calls (e.g., `safeTransfer`) to the end of the function after all internal state updates to prevent reentrancy attacks.