Joyous Rainbow Shark

Medium

# `CuratedVault::maxDeposit()` breaks ERC4626 complaiance as it can return a value that would revert if used for a `deposit()` call

### Summary

The readme specifies that the Curated Vault contract should follow the ERC4626 standard. However there are two scenarios where `maxDeposit()` can return a value greater than what would be accepted as a deposit. 

### Root Cause

**Root cause 1**
The Curated Vault's `supplyQueue` is an ordered array of pools which dictate the order in which vault deposits are deposited into the underlying pools. `supplyQueue` can be set by an 'allocator' through the `CuratedVault::setSupplyQueue()` function, which has no checks that the new supply queue does not contain duplicate pools. This fact was acknowledged by the devs in a [relavent natspec comment](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/interfaces/vaults/ICuratedVaultBase.sol#L165-L167). 

The issue is in order for the vault to be ERC4626 compliant, the `maxDeposit()` function cannot return a value that would revert if used in a `deposit()` call in the same transaction (https://eips.ethereum.org/EIPS/eip-4626#maxdeposit). However `maxDeposit()` makes a call to `_maxDeposit()` which iterates through `supplyQueue` cumulatively sums the assets suppliable and returns this value as 'max deposit'. Note if `supplyQueue` contained a duplicate pool, their 'suppliable' amount would be counted twice.


```javascript
  function _maxDeposit() internal view returns (uint256 totalSuppliable) {
@>  for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      uint256 supplyShares = pool.supplyShares(asset(), positionId);
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());

      // `supplyAssets` needs to be rounded up for `totalSuppliable` to be rounded down.
      uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);
@>    totalSuppliable += supplyCap.zeroFloorSub(supplyAssets);
    }
  }
```

**Root cause 2**
`_maxDeposit()` considers the `supplyCap` for this pool set by the curated vault but does not consider the underlying pool's supply cap. As a result, there are circumstances where the underlying pools are nearing their supply cap but the maximum deposit value will be based on the `supplyCap` mapping on the curated vault contract only. This is another way `maxDeposit()` may return more assets than would actually be accepted in a deposit.

**Root cause 3**
Finally, `_maxDeposit()` does not check whether the reserve asset that would be supplied to a pool in the `supplyQueue` is frozen. If the curated vault attempts to supply to a vault that has the supply asset frozen, the deposit will [revert](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L78-L79).

### Internal pre-conditions

Scenario 1
- Duplicate pools in `supplyQueue`
- The duplicated pool cannot be at the cap

Scenario 2
- The the sum of the difference between the vault's `supplyCap` and vault's assets in the underlying pools exceeds the difference between the underlying pool cap and total liquidity in the pools.

Scenario 3
- One or more of the pools in `supplyQueue` have the reserve asset frozen.

### External pre-conditions

_No response_

### Attack Path

1. A user wishes to deposit the maximum funds into a curated vault which is performing well. 
2. They call make a call to `deposit` and set `assets` to the value returned by a prior call to `maxDeposit()`.
3. Due to one of the reasons described above, their deposit reverts

### Impact

1. Internal integrations (such as Pool hooks) and external integrations will be broken.
2. A failure of compliance with ERC4626 which contradicts the contest readme.

Please note the impact of this issue also exists in the `CuratedVault::maxMint()` function which utilizes that same `_maxDeposit()` function.

### PoC

_No response_

### Mitigation

- Do not allow duplicate pools to be added to the `supplyQueue`, there doesn't seem to be a valid reason for an allocator to duplicate pools
- In `CuratedVaultGetters::_maxDeposit()` add the minimum of the available supply Cap in the underlying pool and the available `supplyCap` on the curated vault contract to the `totalSuppliable` variable.