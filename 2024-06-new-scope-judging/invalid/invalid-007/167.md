Abundant Garnet Aphid

Medium

# CuratedVault has no correct implementations of the core ERC-4626  functions

## Summary
See the report

## Vulnerability Detail
Since the protocol intends to support ERC4626 implementation, it should support all the functions that are expected to support every function that are expected to support like `maxDeposit` , `maxWithdraw` etc.
For more information check the official website  https://eips.ethereum.org/EIPS/eip-4626#methods

## Impact
There are no ERC-4626 compliant implementations of the  functions.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L43

## Tool used
Manual Review

## Recommendation
Consider implementing the whole ERC4626 functions