Sneaky Hazelnut Lizard

Medium

# Value obtained from Maxdeposit/Maxmint Value will revert when used for making deposit into the curated vault violating The compliance with ERC4626.

### Summary

Curated Vaults are known to be ERC4626 fully compliant. 
The CuratorVault should follow the ERC4626 standard. -
https://audits.sherlock.xyz/contests/466#:~:text=The%20CuratorVault%20should%20follow%20the%20ERC4626%20standard.

Also check the documentation. - https://docs-one.zerolend.xyz/features/curated-and-cross-chain-vaults#:~:text=Curated%20Vaults%20are%20non%2Dcustodial%2C%20ERC4626%2Dcompliant%20vaults%20that%20enable%20users%20to%20deposit%20a

Zerolend's `_maxDeposit` and `_maxMint` functions overestimate the values returned for the maximum amount of assets that can be deposited into the curated vault, leading to reverts during the actual deposit process. The issue arises because 

1. the pool's supply cap is not considered in the `_maxDeposit` function, resulting in inaccurate values when the user tries to deposit based on the returned maximums. 

2.  Additionally, when a pool is frozen, the maximum deposit for that pool should be zero, but the current implementation does not account for this. These issues violate the ERC4626 specification and result in transaction failures.

### Root Cause

### **maxDeposit**
Maximum amount of the underlying asset that can be deposited into the Vault for the receiver, through a deposit call.

MUST return the maximum amount of assets deposit would allow to be deposited for receiver and not cause a revert, which MUST NOT be higher than the actual maximum that would be accepted (it should underestimate if necessary). This assumes that the user has infinite assets, i.e.


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L130-L145

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L76-L88

Without considering the FROZEN AND POOL CAP factors, we revert here

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L142
 

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

The **MaxDeposit** and **MaxMint** functions in the curated vault should accurately return the maximum amount of assets that can be deposited into the vault without causing a revert during the deposit. However, two critical issues arise:

1. **Frozen Pools**: The current implementation does not consider whether a pool is frozen, which would prevent deposits from being made. According to the ERC4626 standard, if a pool is frozen, the max deposit amount should be returned as zero. However, in Zerolend, frozen pools are still included in the calculation, leading to incorrect values and potential reverts when users attempt deposits.

2. **Supply Cap Ignored**: The supply cap of the main pool where the deposit is being made is not factored into the `maxDeposit` calculation. This can result in overestimated values where the maximum deposit for the curated vault is considered, but not the pool’s supply cap. Consequently, users may attempt to deposit an amount that exceeds the pool's supply cap, causing the transaction to revert.

Example of the problematic logic in the `_maxDeposit` function:

```solidity
for (uint256 i; i < supplyQueue.length; ++i) {
  IPool pool = supplyQueue[i];

  uint256 supplyCap = config[pool].cap;
  if (supplyCap == 0) continue;

  uint256 supplyShares = pool.supplyShares(asset(), positionId);
  (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());

 @audit >>>>  uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);
  @audit >>>>   totalSuppliable += supplyCap.zeroFloorSub(supplyAssets);
}
```

In this implementation:
- The function does not consider whether the pool is **frozen**, and therefore unable to accept new deposits.
- The **supply cap** of the underlying pool is not checked, which can lead to an overestimation of the maximum depositable amount.

---

### Impact

- **Transaction Reverts**: Users attempting to deposit the maximum returned by `maxDeposit` or `maxMint` can face transaction reverts if the pool's supply cap has been reached, or if the pool is frozen and unable to accept deposits.
- **ERC4626 Compliance Violation**: According to ERC4626 standards, the maxDeposit function must return the maximum amount of assets that can be transferred without reverting. The current implementation violates this standard by returning overestimated values.

```solidity
  /* LIQUIDITY ALLOCATION */

  /// @dev Supplies `assets` to ZeroLend.
  function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      pool.forceUpdateReserve(asset());

      uint256 supplyShares = pool.supplyShares(asset(), positionId);

      // `supplyAssets` needs to be rounded up for `toSupply` to be rounded down.
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
      uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

      uint256 toSupply = UtilsLib.min(supplyCap.zeroFloorSub(supplyAssets), assets);

      if (toSupply > 0) {
        // Using try/catch to skip markets that revert.
        try pool.supplySimple(asset(), address(this), toSupply, 0) {
          assets -= toSupply;
        } catch {}
      }

      if (assets == 0) return;
    }

    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
  }
```

### PoC

_No response_

### Mitigation

To resolve this issue, the following modifications should be made to the `maxDeposit` function and related logic:

1. **Consider the Pool’s Supply Cap**: The function should be modified to return the **minimum** value between the curated vault's remaining depositable assets and the main pool's supply cap. This ensures that deposits do not exceed the supply cap of the underlying pool.

   Example of mitigation:

 ```solidity
++    uint256 supplyCap2 = cache.reserveConfiguration.getSupplyCap();

++    uint256 MainPoolRemaining = (supplyCap2 * (10 ** cache.reserveConfiguration.getDecimals()).zeroFloorSub((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex))
   
++    uint256 poolSupplyCapRemaining = supplyCap.zeroFloorSub(supplyAssets);
++   totalSuppliable += Math.min(poolSupplyCapRemaining, MainPoolRemaining);
  ```

2. **Frozen Pool Check**: Add a check for whether the pool is frozen. If the pool is frozen, the function should return zero for that pool, as no deposits can be made into a frozen pool.

   Example of mitigation:
 
```solidity
++   (bool isFrozen,) = cache.reserveConfiguration.getFlags();
++   if (isFrozen) {
++       continue;  // Skip this pool, as no deposits are allowed
++  }
   ```

3. **Ensure Accurate `maxDeposit` Return Values**: The returned `maxDeposit` value should accurately represent the maximum amount of assets that can be deposited into the pool without causing a revert. If a pool’s cap is reached, the function should return zero for that pool.

By implementing the mitigations, the `maxDeposit` function will now return accurate values that respect both the curated vault cap and the pool’s supply cap, while also considering whether the pool is frozen. This ensures compliance with ERC4626 and prevents reverts during deposit attempts.

---