Joyous Rainbow Shark

Medium

# `CuratedVaultSetters::_supplyPool()` does not consider the pool cap of the underlying pool, which may cause `deposit()` to revert or lead to an unintended reordering of `supplyQueue`

### Summary

The curated vault's `_supplyPool()` function deposits assets into the underlying pools in `supplyQueue`. Whilst it considers the curated vault's cap for a given pool, it does not consider the underlying pool's internal cap. As a result, some `CuratedVault::deposit()` transactions will revert due to running out of pools to deposit to, or the liquidity will be allocated to the `supplyQueue` in a different order. 

### Root Cause

`CuratedVaultSetters::_supplyPool()` is [called](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L38) in the deposit flow of the curated vault. As shown below, it attempts to deposit the minimum of `assets` and `supplyCap - supplyAssets` (which is the 'available curated pool cap' for this pool). 

```javascript
  function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      pool.forceUpdateReserve(asset());

      uint256 supplyShares = pool.supplyShares(asset(), positionId);

      // `supplyAssets` needs to be rounded up for `toSupply` to be rounded down.
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
@>    uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

@>    uint256 toSupply = UtilsLib.min(supplyCap.zeroFloorSub(supplyAssets), assets);

      if (toSupply > 0) {
        // Using try/catch to skip markets that revert.
        try pool.supplySimple(asset(), address(this), toSupply, 0) {
          assets -= toSupply;
        } catch {}
      }

      if (assets == 0) return;
    }

    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
  }
```

However, the function does not consider an underlying pool's `supplyCap`. Underlying pools have their own `supplyCap` which will cause `supply()` calls to revert if they would put the pool over it's `supplyCap`:

```javascript
  function validateSupply(
    DataTypes.ReserveCache memory cache,
    DataTypes.ReserveData storage reserve,
    DataTypes.ExecuteSupplyParams memory params,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal view {
    ... SKIP!...

    uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();

    require(
      supplyCap == 0
@>      || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
  }
```

Also note that the `pool::supplySimple()` call in `_supplyPool()` is wrapped in a [try/catch block](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L134-L136), so if a `pool::supplySimple()` call were to revert, it will just continue to the next pool.

### Internal pre-conditions

- A `CuratedVault::deposit()` call needs to be within the limits of the curated vault's cap (`config[pool].cap`) but exceed the limits of the underlying pool's `supplyCap`.

### External pre-conditions

_No response_

### Attack Path

1. A curated pool has two pools in the `supplyQueue`.
2. The first underlying pool has an internal `supplyCap` of 100e18 and is currently at 99e18.
3. The first underlying pool has an internal `supplyCap` of 100e18 and is currently at 99e18.
4. A user calls `deposit()` on the curated vault with a value of 2e18.
5. The value does not exceed the curated vault's `config[pool].cap` for either pool.
6. The underlying call to `Pool::supplySimple()` will silently revert on both pools, and the entire transaction will revert due to [running out of available pools to supply the assets to](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L142)
7. As a result, no assets are deposits, despite the underlying pools having capacity to accept the 2e18 deposit between them.

### Impact

**Deposit Reverts**
- If a deposit would be able to be deposited across two or more underlying pools in the `supplyQueue`, but is too large to be added to any one of these underlying pools, the deposit will completely revert, despite the underlying pools having capacity to accept the deposit.

**Inefficient reorder of `supplyQueue`**
- If a deposit amount is within the limits of the curated pool's `config[pool].cap`, but would exceed the limits an underlying pool in `supplyQueue`. Then the silent revert would skip this pool and attempt to deposit it's liquidity to the next pool in the queue. This is an undesired/inefficient reordering of the `supplyQueue` as a simple check on the cap of the underlying pool would reveal some amount that would be accepted by the underlying pool.

### PoC

_No response_

### Mitigation

Create an external getter for a pool's supply cap, similar to `ReserveConfiguration::getSupplyCap()` the next function should also scale the supply cap by the reserve token's decimals. 

Then, add an extra check in `CuratedVaultSetters::_supplyPool()` as shown below.


```diff
  function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      pool.forceUpdateReserve(asset());

      uint256 supplyShares = pool.supplyShares(asset(), positionId);

      // `supplyAssets` needs to be rounded up for `toSupply` to be rounded down.
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
      uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

      uint256 toSupply = UtilsLib.min(supplyCap.zeroFloorSub(supplyAssets), assets);

+     toSupply = UtilsLib.min(toSupply, pool.getSupplyCap(pool.getConfiguration(asset())) - totalSupplyAssets );

      if (toSupply > 0) {
        // Using try/catch to skip markets that revert.
        try pool.supplySimple(asset(), address(this), toSupply, 0) {
          assets -= toSupply;
        } catch {}
      }

      if (assets == 0) return;
    }

    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
  }
```