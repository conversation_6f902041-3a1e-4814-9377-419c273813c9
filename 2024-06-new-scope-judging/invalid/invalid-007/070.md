Steep Aqua Yak

Medium

# CuratedVault is not compliant with ERC4626 standard

## Summary
The functions `CuratedVault#maxDeposit()` and `CuratedVault#maxWithdraw()` are not compliant with ERC4626 standard, which will cause deposit and withdraw flows reverted

## Vulnerability Detail
The function `maxDeposit()` returns a value that will be reverted when depositing with that amount. [The root cause is because the function does not account for interest accruals from pools
](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L131-L145), causing returned value is higher than expected

With function `maxWithdraw`, [the function `_maxWithdraw` and `_simulateWithdraw`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L79-L115) does not account for interest accruals from Pools, causing returned value is lower than expected

So far, using the returned value from `maxDeposit()` and `maxWithdraw()` as parameter, function `deposit()` and `withdraw()` will get reverted.

#### PoC
Add this test to the test file `test/vaults/ERC4626Test.sol`:

```solidity
  function test_maxDeposit_Fail() public {
    _setCap(allMarkets[0], 1000);
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    loanToken.mint(supplier, 1000 ether);
    collateralToken.mint(borrower, 200 ether);

    vm.startPrank(supplier);
    loanToken.approve(address(vault), 1000 ether);
    uint256 share = vault.deposit(995, supplier);

    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 10 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 500, 0);

    skip(30 days);
    vm.startPrank(supplier);
    // allMarkets[0].forceUpdateReserve(vault.asset()); // uncomment this will succeed
    uint256 amount = vault.maxDeposit(supplier);
    vault.deposit(amount, supplier);
  }
...
  function test_maxWithdraw_fail() public {
    _setCap(allMarkets[0], 5000);
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    address supplier2 = makeAddr("supplier2");

    loanToken.mint(supplier, 1000 ether);
    loanToken.mint(supplier2, 1000 ether);
    collateralToken.mint(borrower, 200 ether);

    vm.startPrank(supplier);
    loanToken.approve(address(vault), 1000 ether);
    uint256 share = vault.deposit(1000, supplier);

    vm.startPrank(supplier2);
    loanToken.approve(address(vault), 1000 ether);
    vault.deposit(1000, supplier2);


    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 10 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 1200, 0);

    skip(365 days);
    vm.startPrank(supplier);
    // allMarkets[0].forceUpdateReserve(vault.asset()); // uncomment this will succeed
    uint256 amount = vault.maxWithdraw(supplier);
    vault.withdraw(amount, supplier, supplier);
  }

```

Run the tests and the console shows:
```bash
Failing tests:
Encountered 2 failing test in test/forge/core/vaults/ERC4626Test.sol:ERC4626Test
[FAIL. Reason: AllCapsReached()] test_maxDeposit_Fail() (gas: 1018226)
[FAIL. Reason: NotEnoughLiquidity()] test_maxWithdraw_fail() (gas: 1258989)
```

## Impact
Not compliant with ERC4626 standard

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L131-L145
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L79-L115

## Tool used

Manual Review

## Recommendation
Account for interest accrued from pools in the functions