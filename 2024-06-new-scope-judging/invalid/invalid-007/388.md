Clever Ebony Halibut

Medium

# Non-Compliant `maxDeposit` And `maxMint` Functions in CuratedVault Implementation

## Summary

The `maxDeposit` function in the `CuratedVault` contract does not comply with the ERC-4626 standard. It fails to account for limitations imposed by underlying pools, potentially returning a value that exceeds the actual maximum deposit amount allowed.

## Vulnerability Detail

- sherlock rules :

  > The protocol team can use the README (and only the README) to define language that indicates the codebase's restrictions and/or expected functionality. Issues that break these statements, irrespective of whether the impact is low/unknown, will be assigned Medium severity. High severity will be applied only if the issue falls into the High severity category in the judging guidelines.

- from the readme :

  > The CuratorVault should follow the ERC4626 standard.

The `maxDeposit` function doesn't comply with `ERC-4626` which is a mentioned requirement.
- According to the eip specification:

  > maxDeposit MUST return the maximum amount of assets deposit would allow to be deposited for receiver and not cause a revert ....

- However the function can return an amount that cause the deposit function to fail ,the `maxDeposit` function in `CuratedVault` only considers internal caps:

```js
function _maxDeposit() internal view returns (uint256 totalSuppliable) {
for (uint256 i; i < supplyQueue.length; ++i) {
IPool pool = supplyQueue[i];

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) continue;

        uint256 supplyShares = pool.supplyShares(asset(), positionId);
        (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());

        // `supplyAssets` needs to be rounded up for `totalSuppliable` to be rounded down.
        uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);
        totalSuppliable += supplyCap.zeroFloorSub(supplyAssets);
    }

}
```

The issue arises when the underlying pools have their own caps reached or the asset is frozen in them , in this case the `CuratedVault` can return an amount that cause a revert when attempt to be deposited.since in the deposit function , the contract will attempt to supply the deposited funds to the pools using `_supplyPool` and if there is a remaining balance at end the transactin will rever :

```js
  function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
  // some code to supply to pools
    }

  >>    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
}
```

## Impact

Failure to comply with the ERC-4626 specification which is an expected requirement for the `CuratedVault` contract.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L51-L53

## Tool used

Manual Review

## Recommendation

Modify the `maxDeposit` implementation to account for the caps and limitations of the underlying pools, ensuring it returns a non-reverting maximum deposit amount in compliance with the ERC-4626 standard.