Sneaky Hazelnut Lizard

Medium

# CuratedVault Deposit function will revert in some cases even the Main POOL is not frozen and Supply cap is not reached because of an Incorrect Deposit Calculation.

### Summary

The deposit function in Zerolend's Curated Vault fails in some cases even when all main pools are neither frozen nor have reached their supply caps. The current implementation of the deposit function does not account for the supply cap of the main pool properly when attempting to deposit funds. This issue causes the function to overestimate the amount that can be deposited into a pool, leading to transaction reverts when a portion of the assets cannot be deposited due to supply cap limits. This behaviour breaks the compliance with ERC4626, because even for deposits of _amounts far_ **below the max deposits** , the transaction gets reverted.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L112-L143

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L70-L87


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L130

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L134

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L83-L88

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path


The **Curated Vault Deposit** function incorrectly attempts to supply assets to pools without accurately considering the **main pool's supply cap**. While the function checks the individual supply caps of pools, the comparison logic is flawed, leading to scenarios where the function attempts to deposit more than a pool can handle. As a result, deposits revert even though the pools are not frozen, and their supply caps have not been fully reached. 

The root cause of this issue is that the current logic tries to supply assets based on the **curated vault's** supply caps without considering the **remaining capacity** in the main pools. Even after fixing the bug in `maxDeposit` calculation (as previously reported), this issue persists in the deposit function itself.

#### Example Scenario:

Consider four pools with the following cap and supply constraints:

| Pool     | Main Pool Cap | Curated Vault Cap | Assets to Deposit | Present Supply  |
|---------|-----------------|---------------------|---------------------|------------------|
| Pool 1  | 20                    | 50                         | 100                        | 20              | fails
| Pool 2  | 30                    | 70                         |                               | 30              | fails
| Pool 3  | 40                    | 60                         |                               | 40              | fails
| Pool 4  | 50                    | 80                         |                               | 50              | fails

The curated vault calculates the deposit limits based on its own caps, resulting in the following steps:
- **Pool 1**: Min(50, 100) = 50, but the actual supply cap is **20**, In the try and catch leading to a revert because the vault tries to deposit more than the pool can accept.
- **Pool 2**: Min(70, 100) = 70, but the actual supply cap is **30**, causing another revert.
- **Pool 3**: Min(60, 100) = 60, but the actual supply cap is **40**, leading to another revert.
- **Pool 4**: Min(80, 100) = 80, but the actual supply cap is **50**, leading to yet another revert.


BASED ON A BUG REPORTED ON MAXDEPOSIT, Thus, even though the `maxDeposit` function was corrected to return actual Max deposit of 140 (instead of the wrong  260), A deposit of just 100 far below the MAX will revert, this still causes a revert because the deposit function attempts to deposit into pools beyond their capacity without a proper check.

#### Code Example (Problematic Logic):

```solidity
function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
        IPool pool = supplyQueue[i];

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) continue;

        pool.forceUpdateReserve(asset());

        uint256 supplyShares = pool.supplyShares(asset(), positionId);

        (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
        uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

@audit>> we are not checking the main supply pool cap>>         uint256 toSupply = UtilsLib.min(supplyCap.zeroFloorSub(supplyAssets), assets);

        if (toSupply > 0) {
            try pool.supplySimple(asset(), address(this), toSupply, 0) {
                assets -= toSupply;
            } catch {}
        }

        if (assets == 0) return;
    }

@audit>> reverts>>     if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
}
```

In this implementation, the function tries to supply assets to each pool but does not correctly account for the **remaining supply cap** of the main pool. As a result, deposits that exceed the pool's cap revert the transaction.

```solidity
  function validateSupply(
    DataTypes.ReserveCache memory cache,
    DataTypes.ReserveData storage reserve,
    DataTypes.ExecuteSupplyParams memory params,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal view {

   require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    (bool isFrozen,) = cache.reserveConfiguration.getFlags();
    require(!isFrozen, PoolErrorsLib.RESERVE_FROZEN);

    uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();

    require(
      supplyCap == 0
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
```

### Impact

- **Transaction Reverts**: Users attempting to deposit into the Curated Vault will experience reverts even when the pools are not frozen and have not reached their supply caps. This causes significant issues, particularly for large depositors.
- **ERC4626 Compliance Violation**: According to the ERC4626 standard, VALUES below max deposits must not revert if the pool is operational and has not reached its supply cap. The current implementation violates this by causing unnecessary reverts for a value extremely low compared to the MAX.

### PoC

_No response_

### Mitigation

To resolve this issue, the deposit function should be updated to accurately check the **remaining capacity** of the main pool before attempting to supply assets. The function should also ensure that deposits are distributed across pools in such a way that avoids exceeding any pool's cap, thereby preventing reverts.

#### Suggested Fix:

1. **Check Main Pool Supply Cap**: Before supplying assets to any pool, the function should compare the curated vault's depositable assets with the main pool’s remaining supply cap. Only the amount that does not exceed the pool's cap should be supplied.
   
2. **Update the Supply Logic**: Instead of directly subtracting from the `assets` variable after each failed attempt, the logic should only proceed when there is sufficient capacity in the main pool. This ensures that assets are not prematurely deducted.

#### Example of Corrected Logic:
```solidity
function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
        IPool pool = supplyQueue[i];

++   (bool isFrozen,) = cache.reserveConfiguration.getFlags();
++   if (isFrozen) {
++       continue;  // Skip this pool, as no deposits are allowed
++  }

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) continue;

        pool.forceUpdateReserve(asset());

        uint256 supplyShares = pool.supplyShares(asset(), positionId);
        (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
        uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

        // Check remaining capacity in the pool.
        uint256 poolCapRemaining = supplyCap.zeroFloorSub(supplyAssets);
        uint256 toSupply = UtilsLib.min(poolCapRemaining, assets);

++    uint256 supplyCap2 = cache.reserveConfiguration.getSupplyCap();

++    uint256 MainPoolRemaining = (supplyCap2 * (10 ** cache.reserveConfiguration.getDecimals()).zeroFloorSub((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex))

++  uint256 toSupply += Math.min(toSupply, MainPoolRemaining);

        if (toSupply > 0) {
            try pool.supplySimple(asset(), address(this), toSupply, 0) {
                assets -= toSupply;
            } catch {}
        }

        // Stop if all assets have been supplied
        if (assets == 0) return;
    }

    // Revert only if no assets could be supplied due to all caps being reached
    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
}
```