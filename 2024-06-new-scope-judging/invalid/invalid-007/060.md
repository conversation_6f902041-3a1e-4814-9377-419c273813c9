Fresh Silver Owl

Medium

# `maxDeposit` doesn't comply with ERC-4626

## Summary

Currently, some ERC4626 functions in `CuratedVault` do not comply with EIP4626 standard.

## Vulnerability Detail

According to [EIP4626](https://eips.ethereum.org/EIPS/eip-4626), 

> maxDeposit MUST return the maximum amount of assets deposit would allow to be deposited for receiver and not cause a revert, which MUST NOT be higher than the actual maximum that would be accepted (it should underestimate if necessary).

However, current `maxMint` may return a value higher than the actual max mint due to duplicate markets in the supplyQueue.

```solidity
  /// @dev Returns the maximum amount of assets that the vault can supply on ZeroLend.
  function _maxDeposit() internal view returns (uint256 totalSuppliable) {
    for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      uint256 supplyShares = pool.supplyShares(asset(), positionId);
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());

      // `supplyAssets` needs to be rounded up for `totalSuppliable` to be rounded down.
      uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);
      totalSuppliable += supplyCap.zeroFloorSub(supplyAssets);
    }
  }
```

## Impact

Failure to comply with the specification which is a mentioned necessity.

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L51

## Tool used

Manual Review

## Recommendation

maxDeposit may underestimate.