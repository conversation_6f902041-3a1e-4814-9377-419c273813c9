Sneaky Hazelnut Lizard

Medium

# Based on the README and in the DOC it was stated that Curated Vaults are ERC4626 compliant but MaxRedeem and Maxwithdrawal Values will cause a revert breaking the EIP4626 rule

### Summary

The CuratorVault should follow the ERC4626 standard. - 
 https://audits.sherlock.xyz/contests/466#:~:text=The%20CuratorVault%20should%20follow%20the%20ERC4626%20standard.

Also check the documentation. - https://docs-one.zerolend.xyz/features/curated-and-cross-chain-vaults#:~:text=Curated%20Vaults%20are%20non%2Dcustodial%2C%20ERC4626%2Dcompliant%20vaults%20that%20enable%20users%20to%20deposit%20a

Zerolend's current implementation of the `maxWithdraw`/ MaxRedeem function leads to a **Denial of Service (DOS)** when users (whales) attempt to withdraw the maximum liquidity available. Although there is sufficient liquidity in the pool to cover the user's request, the withdrawal reverts due to the simultaneous withdrawal and transfer of funds to the treasury. This behavior violates the EIP-4626 standard, which mandates that the `maxWithdraw` and  `maxRedeem` function should return the maximum amount that can be withdrawn without causing a revert, underestimating if necessary.  



### Root Cause

###  **maxWithdraw**

Maximum amount of the underlying asset that can be withdrawn from the owner balance in the Vault, through a withdraw call.

MUST return the maximum amount of assets that could be transferred from owner through withdraw and not cause a revert, which MUST NOT be higher than the actual maximum that would be accepted (it should underestimate if necessary).

### ** maxRedeem**
Maximum amount of Vault shares that can be redeemed from the owner balance in the Vault, through a redeem call.

MUST return the maximum amount of shares that could be transferred from owner through redeem and not cause a revert, which MUST NOT be higher than the actual maximum that would be accepted (it should underestimate if necessary).

MUST factor in both global and user-specific limits, like if redemption is entirely disabled (even temporarily) it MUST return 0.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L81-L88

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L69-L75

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L117-L128

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L62-L83

AFTER A USER WITHDRAWS THE AVAILABLE LIQUIDITY THE POOL REVERTS HERE 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L84

We attempt another transfer when the vault is empty causing a reversal.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L98

The max redeem and withdraw should reflect the treasury asset to be withdrawn.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. query the maxwithdraw/maxredeem function
2. try to withdraw the max values
3. withdrawal function reverts.

### Impact

Withdrawal function reverts when user tries to withdraw the max value returned by the Max withdrawal/redeem functions thus breaking the EIP4626 rule for a complaint/standard vault

### PoC

_No response_

### Mitigation

To mitigate this vulnerability, the `maxWithdraw` function must be adjusted to subtract the treasury's accrued shares (converted to assets) before returning the maximum value. This ensures the user cannot withdraw more than what is actually available, considering both their share and the treasury's share.

Here’s how the mitigation could be implemented:

1. **Recalculate the `maxWithdraw` function** to subtract the treasury’s shares (converted to assets) from the user's maximum withdrawal amount:

 ```solidity
 /// @dev Returns the withdrawable amount of assets from the market defined by `pool`, given the market's
  /// total supply and borrow assets and the vault's assets supplied.
  function _withdrawable(
    IPool pool,
    uint256 totalSupplyAssets,
    uint256 totalBorrowAssets,
    uint256 supplyAssets
  ) internal view returns (uint256) {
    // Inside a flashloan callback, liquidity on the pool may be limited to the singleton's balance.
    uint256 availableLiquidity = UtilsLib.min(totalSupplyAssets - totalBorrowAssets, IERC20(asset()).balanceOf(address(pool)));

++  DataTypes.ReserveData storage reserve = reservesData[asset];

++     uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;
++    uint256 treasuryAccruedAssets = accruedToTreasuryShares.rayMul(getNormalizedIncome());

++   if (treasuryAccruedAssets > availableLiquidity ) return 0;
       
++    return ((UtilsLib.min(supplyAssets, availableLiquidity)  - treasuryAccruedAssets );
  }
      
    ```
