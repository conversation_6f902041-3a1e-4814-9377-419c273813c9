Rural Eggshell Sheep

Medium

# supplyAssets is rounded down

## Summary
supplyAssets is rounded down instead of up.
## Vulnerability Detail
In `CuratedVaultSettersWhen` when calculating, assets its rounded down.

```solidity
// `supplyAssets` needs to be rounded up for `toSupply` to be rounded down.
(uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
 assets = shares.toAssetsDown(totalSupplyAssets, totalSupplyShares);
```
so when reallocating , supplyAssets could be greater than expected amount. 
## Impact
Will round off towards higher output token amount in favour of the withdrawer
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L60-L69
## Tool used

Manual Review

## Recommendation
use ``toAssetsUp`` instead of ``toAssetsDown``