Shambolic Orchid Crow

Medium

# _SetupRole() funtion is not declared in inherited contract, leading to the failure of pool roles setup.

### Summary

In the **PoolConfigurator** contract, initRoles function initializes different roles for a given pool, It takes arrays of addresses for different types of admins. The _**__setupRole_**_ function was called in three for loops to assign different roles to addresses, it was meant to indirectly inherit from **AccessControlEnumerable** or **AccessControl** contract. However, this function is not declared in  **AccessControlEnumerable** or **AccessControl** contract, instead it is declared and deprecated in AccessControlUpgradeable contract, which is not inherited by **PoolConfigurator** contract and its dependencies, thus rendering the roles assignment in _setupRole function invalid.




### Root Cause

Here is the related code:
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L40-L49


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

When compiling this contract, the error "DeclarationError: Undeclared identifier" will occur due to the incorrect declaration or import  library.

_No response_

### PoC

_No response_

### Mitigation

Using _grantRole in the initRoles function instead of _setupRole, _grantRole function is accessible from the inherited AccessControl.sol contract.  So, it should be like this: 
          _grantRole(getRoleFromPool(pool, POOL_ADMIN_ROLE), admins[i]);
          _grantRole(getRoleFromPool(pool, EMERGENCY_ADMIN_ROLE), emergencyAdmins[i]);
          _grantRole(getRoleFromPool(pool, RISK_ADMIN_ROLE), riskAdmins[i]);