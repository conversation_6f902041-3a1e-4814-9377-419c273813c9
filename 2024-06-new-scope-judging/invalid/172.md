Rural Eggshell Sheep

Medium

# possible precision loss in LiquidationLogic._calculateAvailableCollateralToLiquidate() function when calculating vars.maxCollateralToLiquidate because of division before multiplication

## Summary
_calculateAvailableCollateralToLiquidate() divides before multiplying when calculating maxCollateralToLiquidate.


## Vulnerability Detail
```solidity
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);
```

There will be precision loss when calculating the ``vars.maxCollateralToLiquidate`` because solidity truncates values when dividing and dividing before multiplying causes precision loss.
## Impact
Precision Loss .
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L352-L354
## Tool used

Manual Review

## Recommendation

mulitply before division 
```solidity
 vars.baseCollateral = vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit;

    // Apply percent multiplication before dividing
    vars.maxCollateralToLiquidate = (vars.baseCollateral.percentMul(liquidationBonus)) / (vars.collateralPrice * vars.debtAssetUnit);

```