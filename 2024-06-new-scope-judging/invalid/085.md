Able Concrete Jellyfish

Medium

# Inadequate Input Validation in executeInitReserve Leading to Potential DoS

## Summary
The `executeInitReserve` function in the `PoolLogic` library lacks comprehensive input validation for critical parameters. This oversight can lead to logical errors and potential Denial of Service (DoS) attacks, compromising the integrity and availability of the contract's functionality.

## Vulnerability Detail
The `executeInitReserve` function does not adequately validate input parameters, specifically `params.asset`, `params.interestRateStrategyAddress`, and `params.oracle`. These parameters are critical to the correct operation of the contract, and improper handling can lead to serious problems.
```solidity
  function executeInitReserve(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.InitReserveParams memory params
  ) external {
@=>    require(Address.isContract(params.asset), PoolErrorsLib.NOT_CONTRACT);
    require(Address.isContract(params.interestRateStrategyAddress), PoolErrorsLib.NOT_CONTRACT);
    require(Address.isContract(params.oracle), PoolErrorsLib.NOT_CONTRACT);

    DataTypes.ReserveConfigurationMap memory config;
    config.setLtv(params.configuration.ltv);
    config.setLiquidationThreshold(params.configuration.liquidationThreshold);
    config.setLiquidationBonus(params.configuration.liquidationBonus);
    config.setDecimals(params.configuration.decimals);
    config.setFrozen(params.configuration.frozen);
    config.setBorrowingEnabled(params.configuration.borrowable);
    config.setBorrowCap(params.configuration.borrowCap);
    config.setSupplyCap(params.configuration.supplyCap);

    setReserveConfiguration(reservesData, params.asset, params.interestRateStrategyAddress, params.oracle, config);

    reservesData[params.asset].init(params.interestRateStrategyAddress);

    bool reserveAlreadyAdded = reservesData[params.asset].id != 0 || reservesList[0] == params.asset;
    require(!reserveAlreadyAdded, PoolErrorsLib.RESERVE_ALREADY_ADDED);

    reservesData[params.asset].id = params.reservesCount;
    reservesList[params.reservesCount] = params.asset;

    emit PoolEventsLib.ReserveInitialized(params.asset, params.interestRateStrategyAddress, params.oracle);
  }
```


## Impact
Incorrect initialization of reserves can lead to misconfigured financial parameters, affecting the entire lending protocol.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L46-L76

## Tool used

Manual Review

## Recommendation
Implement comprehensive input validation to ensure that all critical parameters are valid before proceeding with backup initialization.
```diff
  function executeInitReserve(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.InitReserveParams memory params
  ) external {
    // Validate that the asset, interest rate strategy, and oracle addresses are not zero
+   require(params.asset != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);
+   require(params.interestRateStrategyAddress != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);
+   require(params.oracle != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);

    require(Address.isContract(params.asset), PoolErrorsLib.NOT_CONTRACT);
    require(Address.isContract(params.interestRateStrategyAddress), PoolErrorsLib.NOT_CONTRACT);
    require(Address.isContract(params.oracle), PoolErrorsLib.NOT_CONTRACT);

    DataTypes.ReserveConfigurationMap memory config;
    config.setLtv(params.configuration.ltv);
    config.setLiquidationThreshold(params.configuration.liquidationThreshold);
    config.setLiquidationBonus(params.configuration.liquidationBonus);
    config.setDecimals(params.configuration.decimals);
    config.setFrozen(params.configuration.frozen);
    config.setBorrowingEnabled(params.configuration.borrowable);
    config.setBorrowCap(params.configuration.borrowCap);
    config.setSupplyCap(params.configuration.supplyCap);

    setReserveConfiguration(reservesData, params.asset, params.interestRateStrategyAddress, params.oracle, config);

    reservesData[params.asset].init(params.interestRateStrategyAddress);

    bool reserveAlreadyAdded = reservesData[params.asset].id != 0 || reservesList[0] == params.asset;
    require(!reserveAlreadyAdded, PoolErrorsLib.RESERVE_ALREADY_ADDED);

    reservesData[params.asset].id = params.reservesCount;
    reservesList[params.reservesCount] = params.asset;

    emit PoolEventsLib.ReserveInitialized(params.asset, params.interestRateStrategyAddress, params.oracle);
  }
```