Able Concrete Jellyfish

High

# Miscalculated Asset Reallocation Leading to Inflationary Exploit

## Summary
The `reallocate` function in the `CuratedVault` contract fails to ensure that the total assets withdrawn match the total assets supplied. This miscalculation can be exploited to create an imbalance in the contract's accounting, potentially leading to an inflationary scenario where more tokens are minted than backed by actual assets.

## Vulnerability Detail
```solidity
  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    uint256 totalSupplied;
    uint256 totalWithdrawn;

    for (uint256 i; i < allocations.length; ++i) {
      MarketAllocation memory allocation = allocations[i];
      IPool pool = allocation.market;

      (uint256 supplyAssets, uint256 supplyShares) = _accruedSupplyBalance(pool);
      uint256 toWithdraw = supplyAssets.zeroFloorSub(allocation.assets);

      if (toWithdraw > 0) {
        if (!config[pool].enabled) revert CuratedErrorsLib.MarketNotEnabled(pool);

        // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
        uint256 shares;
        if (allocation.assets == 0) {
          shares = supplyShares;
          toWithdraw = 0;
        }

        DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
        emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
        totalWithdrawn += burnt.assets;
      } else {
        uint256 suppliedAssets =
          allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets);

        if (suppliedAssets == 0) continue;

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) revert CuratedErrorsLib.UnauthorizedMarket(pool);

        if (supplyAssets + suppliedAssets > supplyCap) revert CuratedErrorsLib.SupplyCapExceeded(pool);

        // The market's loan asset is guaranteed to be the vault's asset because it has a non-zero supply cap.
        IERC20(asset()).forceApprove(address(pool), type(uint256).max);
        DataTypes.SharesType memory minted = pool.supplySimple(asset(), address(this), suppliedAssets, 0);
        emit CuratedEventsLib.ReallocateSupply(_msgSender(), pool, minted.assets, minted.shares);
        totalSupplied += suppliedAssets;
      }
    }

@=> if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();
  }
```
Scenario:
Initial Conditions:
1. Contract Setup:
- The `CuratedVault` contract has multiple pools (`IPool`) that are set with supply caps (`supplyCap`).
- Each pool has a set amount of assets that are supplied and withdrawn based on previous allocations.
2. `reallocate` function:
- This function aims to reset the allocation of assets among pools based on the `MarketAllocation[]` input.

Exploits:
1. Input Manipulation:
- The attacker structures the `MarketAllocation[]` input so that the new allocation exceeds the supposed supply cap.
- The attacker exploits a condition where `supplyAssets` is calculated incorrectly, for example, due to unexpected donations that are not detected by the contract logic.
2. Logic Error:
- In the allocation loop, the attacker ensures that `toWithdraw` is calculated to be smaller than it should be, or that `allocation.assets` is greater than `supplyAssets` without proper validation.
- The attacker exploits a condition where `totalSupplied` can exceed `totalWithdrawn` without being detected by the final check.
3. Attack Execution:
- The attacker calls the `reallocate` function with manipulated input.
- Due to errors in the calculation of `supplyAssets` and `toWithdraw`, the attacker manages to make `totalSupplied` larger than `totalWithdrawn`.
- The final check `if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();` fails to detect this mismatch due to the incorrect calculation logic.

## Impact
- An attacker can claim or mint more tokens from the contract than they should, causing inflation.
- This imbalance results in more tokens in circulation than are backed by the actual asset, reducing the value of the token and harming other users.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L232-L276

## Tool used

Manual Review

## Recommendation
Add strict validation that ensures that the total assets withdrawn are equal to the total assets supplied.
```diff
  function reallocate(MarketAllocation[] calldata allocations) external onlyAllocator {
    uint256 totalSupplied;
    uint256 totalWithdrawn;

    for (uint256 i; i < allocations.length; ++i) {
      MarketAllocation memory allocation = allocations[i];
      IPool pool = allocation.market;

      (uint256 supplyAssets, uint256 supplyShares) = _accruedSupplyBalance(pool);
      uint256 toWithdraw = supplyAssets.zeroFloorSub(allocation.assets);

      if (toWithdraw > 0) {
        if (!config[pool].enabled) revert CuratedErrorsLib.MarketNotEnabled(pool);

        // Guarantees that unknown frontrunning donations can be withdrawn, in order to disable a market.
        uint256 shares;
        if (allocation.assets == 0) {
          shares = supplyShares;
          toWithdraw = 0;
        }

        DataTypes.SharesType memory burnt = pool.withdrawSimple(asset(), address(this), toWithdraw, 0);
        emit CuratedEventsLib.ReallocateWithdraw(_msgSender(), pool, burnt.assets, burnt.shares);
        totalWithdrawn += burnt.assets;
      } else {
        uint256 suppliedAssets =
          allocation.assets == type(uint256).max ? totalWithdrawn.zeroFloorSub(totalSupplied) : allocation.assets.zeroFloorSub(supplyAssets);

        if (suppliedAssets == 0) continue;

        uint256 supplyCap = config[pool].cap;
        if (supplyCap == 0) revert CuratedErrorsLib.UnauthorizedMarket(pool);

        if (supplyAssets + suppliedAssets > supplyCap) revert CuratedErrorsLib.SupplyCapExceeded(pool);

        // The market's loan asset is guaranteed to be the vault's asset because it has a non-zero supply cap.
        IERC20(asset()).forceApprove(address(pool), type(uint256).max);
        DataTypes.SharesType memory minted = pool.supplySimple(asset(), address(this), suppliedAssets, 0);
        emit CuratedEventsLib.ReallocateSupply(_msgSender(), pool, minted.assets, minted.shares);
        totalSupplied += suppliedAssets;
      }
    }

-   if (totalWithdrawn != totalSupplied) revert CuratedErrorsLib.InconsistentReallocation();
+   require(totalWithdrawn == totalSupplied, "CuratedVault: Inconsistent reallocation");
  }
```