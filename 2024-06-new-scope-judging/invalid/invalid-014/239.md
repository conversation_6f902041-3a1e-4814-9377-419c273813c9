Joyous Cedar Tortoise

High

# A malicious vault creator can add a malicious pool to steal depositor funds

## Summary
A malicious pool owner can call `submitCap()` via a curator account, and then call `acceptCap()` after the timelock. They do this for a `pool` that is a malicious contract rather than an actual Zerolend pool. Then, funds can be reallocated to the `pool`, and the deposited funds can later be pulled out of the malicious `pool`.

## Vulnerability Detail
Root cause: Within `_setCap()`, `PoolFactory.isPool()` is not called to check if the pool was created via the ZeroLend pool factory or not.

## Impact
### Attack path:
1. Malicious vault owner calls `submitCap()` and `acceptCap()` for the malicious pool
2. They then call `reallocate()` to reallocate funds from all other pools into this malicious pool
3. Then they withdraw the funds from the malicious pool, through a custom function that transfers tokens out.

**Impact:** all depositor funds can be stolen by a malicious pool owner

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/vaults/CuratedVault.sol#L148
## Tool used
Manual Review

## Recommendation
Use `PoolFactory.isPool()` to ensure that pools added to a vault are actually Zerolend pools.