Deep Seaweed Haddock

Medium

# The malicious pools created by malicious attackers will destroy the vault.

### Summary

- The vault accepts the pool by `submitCap` and  `acceptCap`  but doesn't check the pool was created by the  legal pool factory. This leads to the malicious pool creator can do so many bad things based on their intention, such as withdrawing the asset the user deposited.

### Root Cause

In `CuratedVault.sol:149` https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L149
It doesn't check the pool was created by the legal pool factory.

Another point is that third parties may have created the vault. It's inconvenient to inform them to check the pool, which was created by the legal pool factory when there are so many pools. 


### Internal pre-conditions

The  admin with  CURATOR_ROL<PERSON> directly inputs the malicious pool when calling `submitCap.`  https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L148C12-L148C21. 

### External pre-conditions

1. A malicious attacker creates a pool with his pool factory; the pool not only has current legal pool functions, but also the attacker can add new functions, such as withdrawing the assets. Meanwhile, the attacker controls the illegal pool factory config functions and PoolManager. such as `setTreasury in PoolFactory`

### Attack Path

1. The attacker created his pool factory and the malicious pool contract by the pool factory.  The malicious pool contract can add a malicious function as below.
```solidity
function withdrawAsset(address asset) external onlyOwner {
    IERC20(asset).safeTransfer(msg.sender, IERC20(asset).balanceOf(address(this)));
  }
```

2. The admin with  CURATOR_ROLE mistakenly inputs the malicious pool address when calling the `submitCap function in CuratedVault contract` As the code doesn't check that the pool is legal. 

3. The attacker can increase the borrow interests for the underlying related vault contract, attracting the vault and selecting the malicious pool.

4. When the users deposit their assets in this vault, the related malicious pool receives the assets. Then the attacker can withdraw the users's assets or do more things such as adjust pool params, increase lending or borrowing interests, attract more victims.




### Impact

1. The vault accepting the malicious pool can make the user lose all their asset(the vault's underlying Token)
2. The attacker can do so many campaigns, attracting many victims and expanding their profit.

### PoC

_No response_

### Mitigation

The  admin with  CURATOR_ROLE should check the pool by the legal pool factory or modify the CuratedVault contract adding the modifier that check the pool is legal.

```solidity
CuratedVaultStorage add 
  /// @notice The pool factory contract that is used to create pools.
  IPoolFactory public factory;

// CuratedVault contract 
// when initialize. should set factory address
function initialize(
    address[] memory _admins,
    address[] memory _curators,
    address[] memory _guardians,
    address[] memory _allocators,
    uint256 _initialTimelock,
    address _asset,
    address _factory,
    string memory _name,
    string memory _symbol
  ) external initializer {
    .... 
    factory = IPoolFactory(_factory);
  }

// add CuratedErrorsLib.NotPoo()
modifier isPool(address pool) {
    if (!factory.isPool(pool)) {
      revert CuratedErrorsLib.NotPool();
    }
    _;
  }

// add isPool(address(pool)) 
function submitCap(IPool pool, uint256 newSupplyCap) external isPool(address(pool)) onlyCuratorRole {
```

