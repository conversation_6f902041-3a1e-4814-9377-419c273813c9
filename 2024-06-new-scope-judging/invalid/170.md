Rural Eggshell Sheep

Medium

# unfair reward distribution .

### Summary

user will receive the same amount of rewards as the user who has been supplying from the beginning and users who supplied just before periodFinish  . 

  rewards are always calculated  using the total `rewardPerToken` without deducting how much `rewardPerToken` has already been accumulated before they stake.
 which means a user who hasn't been staking from the beginning will be rewarded the same as a user who has been staking since he bteginning. 


### Root Cause


In `NFTRewardsDistributor:L`,
The issue here is that the contract does not account for the already accumulated `rewardPerTokenStored` from the past when calculating the new staker's `userRewardPerTokenPaid` and calculates it using the total `rewardPerToken` :

```solidity
 function rewardPerToken(bytes32 _assetHash) public view returns (uint256) {
    if (_totalSupply[_assetHash] == 0) {
      return rewardPerTokenStored[_assetHash];
    }
    return rewardPerTokenStored[_assetHash].add(
      lastTimeRewardApplicable(_assetHash).sub(lastUpdateTime[_assetHash]).mul(rewardRate[_assetHash]).mul(1e18).div(
        _totalSupply[_assetHash]
      )
    );
  }
    rewardPerTokenStored[_assetHash] = rewardPerToken(_assetHash);
    userRewardPerTokenPaid[_tokenId][_assetHash] = rewardPerTokenStored[_assetHash];
```


### Internal pre-conditions

1. admin with role REWARDS_ALLOCATOR_ROLE calls notifyRewardAmount to  pool with some reward amount .

### External pre-conditions

_No response_

### Attack Path

1. alice supplies tokens from the beginning.
2. assume 1 weeks has passed
3. bob supplies token.
4. if alice and bob claim reward from getReward they both rewards will calculated using the same rewardPerToken.

### Impact

unfair reward distribution

### PoC

_No response_

### Mitigation

solution from rareskills
[rareskills](https://www.rareskills.io/post/staking-algorithm#:~:text=What%20if%20someone,at%20block%2020.).
To prevent this from happening, we have a variable for Bob we call the “reward debt.” The moment he deposits, we set the reward debt to be the deposited balance times the reward per token accumulator. That will prevent him from claiming a reward right away since at that moment, the rewards due to him would be zero (current rewards minus reward debt).

We have a separate variable for Bob call “reward debt” or “rewards already issued” and assign it to that hypothetical reward amount. At block 10, the accumulated reward per token was 100, and Bob’s deposit was 100, so his reward debt is 10,000.

If Bob claims rewards at block 20, we subtract the 15,000 reward by the reward debt of 10,000. Bob will only be able to claim 5,000 reward at block 20.
