Dizzy Raspberry Pig

Medium

# Lack of upper bound check for reserveIndex in userConfiguration::setBorrowing will lead to guranteed overflow if reserveIndex is 128 or greater.

### Summary

The missing check of whether reserveIndex is within the MAX_RESERVE_COUNT range in `UserConfiguration::setBorrowing` will lead to guaranteed overflow in the unchecked block when reserveIndex is greater than or equal to 128.

This will result in incorrect borrow status updates, which will affect users and protocol alike.

### Root Cause

In `UserConfiguration.sol::setBorrowing()` function, there is a missing check on the value of reserveIndex being within the range of MAX_RESERVE_COUNT.  # see the code below.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/UserConfiguration.sol#L37-L46

### Internal pre-conditions

1. Whenever, reserveIndex is >= 128, guaranteed overflow occurs in `userConfiguration.sol::setBorrowing()` function

### External pre-conditions

_No response_

### Attack Path

**### Vulnerability Analysis**

1. When reserveIndex = 128,  the bit calculation literally becomes `uint256 bit = 1 << 256`
2.  In solidity, the uint256 type has a **fixed width of 256 bits** exactly. Therefore, Shifting a 1 by 256 bits results in a value of 0 because all bits are pushed beyond the 256-bit boundary. Essentially, 1 << 256 is equivalent to 0

3. The consequences of bit = 0 on self.data within the `userConfiguration::setBorrowing()` is analysed below:
  + When the result of the bitwise shift is 0, and the borrowing flag is true, the following happens:
      The bitwise OR with 0 has no effect on self.data. This means that no bit will be set, and the borrowing flag for reserveIndex 128 will not be correctly updated to true. Essentially, the borrowing operation fails silently

  + When the result of the bitwise shift is 0, and the borrowing flag is false, the following happens:
      Bitwise AND with the complement of 0 (~0 is 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF, i.e., all bits set to 1) also has no effect. As a result, no bit will be cleared. If the user was borrowing, the function will fail to clear the borrowing flag, and the system will incorrectly think that the user is still borrowing from reserveIndex 128

4.  This vulnerable function is called within the `BorrowLogic.sol::executeBorrow()` . This effectively means that Zerolend would not register this user's borrow status when reserveId = 128 # see below.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L86

5. And since in `PoolLogic.sol::executeInitReserve()` function, there is no check that reserveId of a reserve should not be 128 or more, it is extremely likely that a particular reserve can be initialized with  a reserveId >= 128 by the initializer. # see below:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L69-L73

### Impact

1.The guaranteed overflow will lead to incorrect borrow status updates for reserveIndex >= 128.

2. Based on the fact that there's no check in `UserConfiguration::setBorrowing`, a user could potentially borrow from reserveIndex = 128, as there is no validation to prevent this. This will create an inconsistent system state because the system allows borrowing for reserveIndex=128. However, functions like `setUsingAsCollateral` and `isUsingAsCollateralOrBorrowing` will revert when handling reserveIndex = 128, meaning collateral cannot be set and the system cannot check whether the user is borrowing or using collateral for that reserve.

3. If the user has borrowed from reserveIndex = 128, it will not be possible for the system to reliably check their borrowing status because the `isUsingAsCollateralOrBorrowing` function will revert for that index. And since the system relies on checking the user's status across multiple reserves for liquidations, malicious users who borrow from reserveIndex 128 will potentially evade liquidations

### PoC

For the PoC of the vulnerability, I am gonna showcase from AAVE's codebase from which most of Zerolend's codebase is forked from that AAVE makes the require check in its `setBorrowing` function
https://github.com/aave-dao/aave-v3-origin/blob/6948864fc7e74b2b29fadfe2007998992060f84b/src/core/contracts/protocol/libraries/configuration/UserConfiguration.sol#L27-L41

```solidity
  function setBorrowing(
    DataTypes.UserConfigurationMap storage self,
    uint256 reserveIndex,
    bool borrowing
  ) internal {
    unchecked {
     [require(reserveIndex < ReserveConfiguration.MAX_RESERVES_COUNT, Errors.INVALID_RESERVE_INDEX);](https://github.com/aave-dao/aave-v3-origin/blob/6948864fc7e74b2b29fadfe2007998992060f84b/src/core/contracts/protocol/libraries/configuration/UserConfiguration.sol#L33)
      uint256 bit = 1 << (reserveIndex << 1);
      if (borrowing) {
        self.data |= bit;
      } else {
        self.data &= ~bit;
      }
    }
}
```

### Mitigation

Ensure that the reserveIndex passed to setBorrowing is within the MAX_RESERVES_COUNT range, and is less than 128. 
In `UserConfiguration::setBorrowing()`, add
```solidity
  +    require(reserveIndex < ReserveConfiguration.MAX_RESERVES_COUNT, Errors.INVALID_RESERVE_INDEX);
```