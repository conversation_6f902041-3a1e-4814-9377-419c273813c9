Mythical Alabaster Snake

Medium

# If an oracle becomes stale or deprecated, there is no way to reconfigure it

The poolConfigurator doesnt have any methods for changing the address of an assets oracle and assets cant be reinitialized, meaning if an oracle becomes stale or deprecated, borrow, withdraw and liquidate are dependent on the oracle, the latter 2 especially as users funds would be locked forever and bad positions wont be liquidated

recommendation : poolConfigurator should have a method to reconfigure oracle