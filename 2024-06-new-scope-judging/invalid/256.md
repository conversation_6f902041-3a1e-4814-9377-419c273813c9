Sneaky Hazelnut Lizard

Medium

# Inability to Disable Flashloan without disabling the Supply/deposit function.

### Summary

In Zerolend, the contract does not allow flash loans to be disabled individually without affecting other critical functions like supplying or depositing/borrowing assets. This introduces a security risk as flash loans are a frequent target for arbitrage and exploitation. Aave, in contrast, provides individual control over flash loans, allowing them to be disabled in emergencies without impacting other operations. Implementing a similar function in Zerolend would help prevent flash loan abuse while maintaining the usability of other features.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L191-L198

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L134-L137

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L78-L79

From the above it is seen that we cannot isolate the flashloan function in cases where it needs to be disabled 

A<PERSON> may toggle or disable flash loans primarily for security reasons. Flash loans, while powerful, can be exploited if there are vulnerabilities in the protocol or in other protocols interacting with Aave. [By disabling flash loans, Aave can prevent potential exploits and protect user funds during periods of heightened risk or when vulnerabilities are discovered](https://docs.aave.com/faq/flash-loans)[1](https://docs.aave.com/faq/flash-loans)[2](https://docs.aave.com/developers/guides/flash-loans).

 Aave has disabled flash loans in the past. [For example, during the DeFi boom in 2020, several protocols experienced flash loan attacks, leading Aave to temporarily disable this feature to safeguard the ecosystem](https://docs.aave.com/developers/guides/flash-loans)[2](https://docs.aave.com/developers/guides/flash-loans).

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/ValidationLogic.sol#L475-L480

 ```solidity
function validateFlashloanSimple(DataTypes.ReserveData storage reserve) internal view {
    DataTypes.ReserveConfigurationMap memory configuration = reserve.configuration;
    require(!configuration.getPaused(), Errors.RESERVE_PAUSED);
    require(configuration.getActive(), Errors.RESERVE_INACTIVE);
    require(configuration.getFlashLoanEnabled(), Errors.FLASHLOAN_DISABLED);
  }
 ```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path


Zerolend lacks the ability to disable or pause flash loans independently. The current implementation only checks if the entire pool is frozen, which prevents flash loans but also impacts supply, borrow and deposit functionality, limiting operational flexibility.

The Zerolend implementation is as follows:

```solidity
DataTypes.ReserveConfigurationMap memory configuration = reserve.configuration;
require(!configuration.getFrozen(), PoolErrorsLib.RESERVE_FROZEN);
```

This implementation restricts the disabling of individual functions. If the reserve is frozen, it disables multiple functionalities, including flash loans and deposits, which can harm liquidity and operational efficiency.

In contrast, Aave allows flash loans to be disabled individually by checking for flash loan enablement and pausing status separately:

```solidity
require(!configuration.getPaused(), Errors.RESERVE_PAUSED);
require(configuration.getActive(), Errors.RESERVE_INACTIVE);
require(configuration.getFlashLoanEnabled(), Errors.FLASHLOAN_DISABLED);
```

This flexibility enables Aave to respond to flash loan vulnerabilities while still allowing deposits and other functions to continue.

### Impact

1.  Flash loans have been exploited in the past, resulting in significant losses. The inability to disable flash loans independently increases the risk of potential arbitrage or flash loan attacks.
2.  Without the ability to pause flash loans individually, admins are forced to freeze the entire pool to mitigate risk, which unnecessarily disrupts regular supply and deposit / borrowing operations.

### PoC

_No response_

### Mitigation

1. **Implement Flash Loan Toggle**: Add a function to enable or disable flash loans independently in the reserve configuration . This allows the admin to react swiftly to vulnerabilities or potential exploits without freezing other operations in the pool.

   - Suggested code:
  
  ```solidity
   /**
   * @notice Validates a flashloan action.
   * @param reserve The state of the reserve
   */
  function validateFlashloanSimple(DataTypes.ReserveData storage reserve) internal view {
    DataTypes.ReserveConfigurationMap memory configuration = reserve.configuration;
    require(!configuration.getFrozen(), PoolErrorsLib.RESERVE_FROZEN);

++   require(configuration.getFlashLoanEnabled(), Errors.FLASHLOAN_DISABLED);
  }
 ```


2. **Review Pool Configuration**: Ensure that the configuration allows for granular control over various features, including supply, borrowing, and flash loans, to allow flexibility during emergencies.


---

