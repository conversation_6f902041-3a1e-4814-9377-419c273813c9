Atomic Currant Mule

Medium

# missing check of timelock change pending state in submitCap in curatedVault.sol

### Summary

SubmitCap and submitMarketRemoval function are used for changing the supply cap and set maeket removal time of a vault . The  modifying uses a two steps precudures. However, the vault should not permit such change if there is a timelock pending submit.

### Root Cause

In https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L148-L160 , the vault performs cap submitting. Howerve, cap submitting should not be allowed during timelock pending process.
in https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L163-L172, the vault submit maeket removal time change. This should not be allowed during timelock pending process.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

_No response_

### PoC

_No response_

### Mitigation

Consider add the following code in submitCap function:
```solidity
function submitCap(){
    if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.AlreadyPending();
    if (config[pool].removableAt != 0) revert CuratedErrorsLib.PendingRemoval(); 
+  if (pendingTimelock.validAt !=0)  revert "submitCap not allowed during pending timelock"
...
}
```
similar to the submitMarketRemoval function