Expert Arctic Mallard

High

# Insufficient Validation of Reserve Factor in `executeBorrow`

### Summary

In `BorrowLogic.sol` at line [51](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51), the `executeBorrow` function mints assets accrued through the reserve factor to the treasury without sufficient validation of accrued shares.

### Root Cause

In `BorrowLogic.sol` at line [51](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L51), the function `executeBorrow` transfers assets to the treasury based on the reserve factor but does not adequately validate the total amount of accrued shares before minting. This lack of validation could lead to inconsistencies and potentially allow users to exploit the reserve factor to withdraw more assets than intended.

### Internal pre-conditions

1. The contract must be interacted with by a user or an external contract.
2. There should be an ongoing borrowing action.

### External pre-conditions

1. The reserve factor configuration must be active and properly set.

### Attack Path

1. Initiate Borrowing Action: An attacker initiates a borrowing action leveraging the reserve factor.
2. Exploit Validation Flaws: Due to insufficient validation of accrued shares and reserve factor, the attacker gains excessive borrowing power.
3. Gain Excess Assets: The attacker exploits the vulnerability to borrow and withdraw more assets than allowed, obtaining excess funds.

### Impact

This vulnerability could result in unintended or excessive asset withdrawals due to improper validation of the reserve factor, leading to potential economic instability and misuse of the borrowing feature.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "forge-std/Test.sol";
import "../BorrowLogic.sol";

contract TestInsufficientValidation is Test {
    BorrowLogic borrowLogic;

    function setUp() public {
        borrowLogic = new BorrowLogic();
    }

    function testInsufficientValidation() public {
        // Set up conditions to exploit insufficient validation
        // Call executeBorrow with parameters that would trigger the exploit
        borrowLogic.executeBorrow(...); // Trigger insufficient validation
    }
}
```

### Mitigation

Review and enhance the validation logic in the `executeBorrow` function. Ensure that the reserve factor and accrued shares are accurately validated before transferring assets to the treasury to prevent potential misuse.