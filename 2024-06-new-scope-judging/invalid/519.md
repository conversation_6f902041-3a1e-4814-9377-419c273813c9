Low Lemon Dove

High

# debtshare first  need to be converted in to the desired amount

### Summary

in liqudationlogic      line 352 debtshare is not convereted but  directly converted into the base collateral amount while it need to first need to be converted into the debt asset  then after that in line  356  it is checked against  userCollateralBalance which is a different type  

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L352

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

wrong calulaction     

### PoC

_No response_

### Mitigation

_No response_