Chilly Cherry Deer

High

# Unauthorized user can cause state inconsistencies affecting reserve

### Summary

The lack of access control and input validation will cause state inconsistencies for the reserve system as unauthorized users will call `forceUpdateReserve` with invalid asset addresses, leading to errors and unexpected behavior.
- [Pool.sol#L161-L165](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L161-L165)

- For example,  the reserve data is expected to contain specific values for interest calculation, accessing uninitialized data could result in incorrect interest rates being applied.

- if it is designed to for user, then there should be incentives and validation to call `forceUpdateReserve`
- Overall, its breaking the core invariants of protocol where state change in reserve can impact incorrect update of indexes.

### Root Cause

The `forceUpdateReserve` function is intended to update the state of a specific reserve. This includes recalculating interest and other parameters based on the latest data.
```solidity
  function forceUpdateReserve(address asset) public {
    DataTypes.ReserveData storage reserve = _reserves[asset];
    DataTypes.ReserveCache memory cache = reserve.cache(_totalSupplies[asset]);
    reserve.updateState(this.getReserveFactor(), cache);
  }
```
- The function is marked as public, allowing any user to call it without any restrictions or access control mechanisms.
- The function does not validate whether the asset exists in the `_reserves`   before attempting to update it.

### Impact

- Any user can call `forceUpdateReserve`, leading to potential misuse and unnecessary state updates.
- The reserve system suffers from potential state inconsistencies.
- Inaccurate reserve data can affect the protocol's overall functionality, including incorrect interest rates caculation. This can lead to users receiving incorrect interest funds & being charged incorrect rates, affecting their financial outcomes-loss of funds.


### Mitigation

- add access control modifier
- Add input validation: Ensure that the asset exists in the `_reserves` before proceeding with updates. This prevents operations on uninitialized storage.
- Implement incentives for users to call the function, such as rewarding them with tokens for performing updates.
- modification should only happen in these two function for this specific issue.
  - https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L161
  - https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L168