Able Concrete Jellyfish

Medium

# Open Invocation of forceUpdateReserve Enabling Potential Operational Disruption

## Summary
The `forceUpdateReserve` function in the Pool contract has no access restrictions, allowing any user to call it. This vulnerability can cause unnecessary updates to the reserve state, resulting in a potential denial of service (DoS) condition due to excessive state changes.

## Vulnerability Detail
The `forceUpdateReserve` function is publicly accessible and can be called by any address.
```solidity
@=>function forceUpdateReserve(address asset) public {
     DataTypes.ReserveData storage reserve = _reserves[asset];
     DataTypes.ReserveCache memory cache = reserve.cache(_totalSupplies[asset]);
     reserve.updateState(this.getReserveFactor(), cache);
  }
```
An attacker could call the `forceUpdateReserve` function and perform unnecessary state updates.

## Impact
Excessive updates can disrupt the normal functioning of the protocol, potentially leading to a denial of service for legitimate users.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L161-L165

## Tool used

Manual Review

## Recommendation
- Implement access controls to limit who can call the `forceUpdateReserve` function.
- Consider adding logic to ensure updates are only performed when necessary.
```diff
// Add an access control modifier
+modifier onlyAuthorized() {
+    require(msg.sender == owner || msg.sender == address(_factory.configurator()), "Not authorized");
+    _;
}

function forceUpdateReserve(address asset) public onlyAuthorized {
    DataTypes.ReserveData storage reserve = _reserves[asset];
    DataTypes.ReserveCache memory cache = reserve.cache(_totalSupplies[asset]);

    // Add a condition to check if an update is necessary
+   if (shouldUpdateReserve(reserve, cache)) {
        reserve.updateState(this.getReserveFactor(), cache);
    }
}

// Example function to determine if an update is necessary
+function shouldUpdateReserve(DataTypes.ReserveData storage reserve, DataTypes.ReserveCache memory cache) internal view returns (bool) {
    // Implement logic to determine if the reserve state needs updating
    // For example, check if certain parameters have changed significantly
+    return true; // Placeholder logic
}
```