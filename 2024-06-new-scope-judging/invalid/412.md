Clever Ebony Halibut

Medium

# nft position manager doesn't implement the logic to handle NFTPositionOperator

## Summary
The `NFTPositionManager` contract demonstrates a flaw where the `operator` field within the `Position` struct is defined but never implemented. This results in expected functionality, such as operator management of user positions, being non-existent.
## Vulnerability Detail
Positions are tracked in NFTPositionManager tracks positiosn using the `Position` struct, which includes the `pool` and the `operator`.
```solidity
  /**
   * @notice Structure representing a user's position, including assets, pool, and operator.
   * @param pool The address of the pool associated with the position.
   * @param operator The address of the operator managing the position.
   */
  struct Position {
    address pool;
    address operator;//@audit-issue this is never used
  }
```

The problem however is that the operator tracked in the _postions state variable could never be set by users and is nevert checked when checking for the authorization of user actions
When checking if the msg.sender is authorized to execute an operation on a position the NFTPOsitionMAnager checks using the followng function.

```solidity
  function _isAuthorizedForToken(uint256 tokenId) internal view {
    if (!_isApprovedOrOwner(msg.sender, tokenId)) revert NFTErrorsLib.NotTokenIdOwner();
  }
```
The _isAuthorizedForToken() fucntion checks using the inherited ERC721 functions from the openzepplin contract but fails to check for the postion operator

## Impact
- core fucntionality is not implemented
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/interfaces/INFTPositionManager.sol#L68-L71
## Tool used

Manual Review

## Recommendation
We recommend adding the funcitonality to allow the owner of the POsition to set operators on the position level using the missing functionality, the check should also be augmented to check for that 

```solidity
  function _isAuthorizedForToken(uint256 tokenId) internal view {
    if (!_isApprovedOrOwner(msg.sender, tokenId) && !(msg.sender == _positions[tokenId].operator) ) revert NFTErrorsLib.NotTokenIdOwner();
  }
```