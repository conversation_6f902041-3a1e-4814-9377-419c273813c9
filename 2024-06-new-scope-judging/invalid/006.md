Able Concrete Jellyfish

Medium

# Admin-Only Ether Withdrawal Leading to Potential Locked Ether

## Summary
The `NFTPositionManager` contract allows <PERSON><PERSON> to be sent via the `receive()` function but lacks a public mechanism for non-admin users to withdraw it, potentially resulting in locked Ether. The existing `withdraw` and `withdrawETH` functions are designed for asset operations related to NFT positions and cannot be used to withdraw Ether sent directly to the contract.

## Vulnerability Detail
1. Ether Reception: The contract includes a `receive()` function:
```solidity
receive() external payable {
    // nothing
}
```
This function allows the contract to accept Ether without any restrictions or additional logic.

2. Restricted Withdrawal: The `sweep` function is the only method to withdraw Ether, but it is restricted to users with the `DEFAULT_ADMIN_ROLE`:
```solidity
function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
        uint256 bal = address(this).balance;
        payable(msg.sender).transfer(bal);
    } else {
        IERC20Upgradeable erc20 = IERC20Upgradeable(token);
        erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
}
```
This means non-admin users cannot withdraw Ether they mistakenly send to the contract.

## Impact
Non-admin users cannot retrieve Ether sent to the contract, leading to potential loss of funds if Ether is sent unintentionally or due to errors in transaction handling. The `withdraw` and `withdrawETH` functions are not applicable for retrieving Ether sent directly to the contract, as they are intended for managing assets associated with NFT positions.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L53-L55

## Tool used

Manual Review

## Recommendation
1. Modify the `receive()` function to revert any Ether sent to the contract, preventing accidental deposits:
```diff
receive() external payable {
+   revert("Ether not accepted");
}
```
2. If receiving Ether is necessary, implement a public withdrawal mechanism allowing users to retrieve their Ether:
```diff
function withdrawEther() external {
    uint256 balance = address(this).balance;
    require(balance > 0, "No Ether to withdraw");
    payable(msg.sender).transfer(balance);
}
```