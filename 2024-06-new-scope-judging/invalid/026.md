Dazzling Sand Griffin

Medium

# CuratedVault lacks `getPositionId()` method, this affects the crafting of `posID` when interacting with pool

## Summary
`posID` isn't crafted properly when curatedVault interacts with pool
## Vulnerability Detail
`Pool.supplySimple()` expects the `to` param to have `getPositionId()` method, because it uses it to craft `posID`
```solidity
function supplySimple(address asset, address to, uint256 amount, uint256 index) public returns (DataTypes.SharesType memory) {
    return _supply(asset, amount, to.getPositionId(index), DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }

```
The issue here now is that curatedVaults doesn't have `getPositionId()` method, nor does it inherit TokenConfiguration.sol which has the method.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/TokenConfiguration.sol#L17-L18

```solidity
  function getPositionId(address user, uint256 index) internal pure returns (bytes32) {
    return keccak256(abi.encodePacked(user, 'index', index));
```

so when `CuratedVaultSetters._supplyPool()` attempts to supply to pool via `supplySimple()`, curatedVault doesn't have `getPositionId()` method so the `posID` isn't crafted properly.
```solidity
    if (toSupply > 0) {
        // Using try/catch to skip markets that revert.
        try pool.supplySimple(asset(), address(this), toSupply, 0) {//@audit 
          assets -= toSupply;
        } catch {}
      }

```

The position of the supplyShares gotten [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L124) via `positionId` will be different from the position that deposits are done on, since the `posID` will be different when we deposit to the pool via supplySimple() because CuratedVault lacks `getPositionId()` method  the `posID` isn't crafted properly so it will be different from [positionId](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L80)
```solidity
 positionId = keccak256(abi.encodePacked(address(this), 'index', uint256(0)));
```


## Impact
supplyShares in  `CuratedVaultSetters._supplyPool()`  is calculated on a different position and deposits and withdraws are done on a different position.

CuratedVault lacks `getPositionId()` method, this affects the crafting of `posID` making [positionId](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L80) != posID where deposits and withdrawals are done on

## Code Snippet

## Tool used

Manual Review

## Recommendation

Add `getPositionId` method to curatedVaults
```solidity
  function getPositionId(address user, uint256 index) internal pure returns (bytes32) {
    return keccak256(abi.encodePacked(user, 'index', index));
  }
```