Polite Garnet Swift

Medium

# duplicate entries is possible in supply queue in CuratedVaultGetters


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L38

in the function `supplyQueueLength`

```solidity
function supplyQueueLength() external view returns (uint256) {
  return supplyQueue.length;
}
```
 the `supplyQueue` array may contain duplicate entries. and that could lead to issues in other parts of the system that rely on the supply queue, such as:

1. Overestimation of available supply capacity
2. Potential for double-counting when iterating through the queue
3. Inconsistencies between the reported queue length and the actual number of unique markets

Because The `setSupplyQueue` function in CuratedVaultSetters.sol allows setting the supply queue without checking for duplicates

```solidity
function setSupplyQueue(IPool[] calldata newSupplyQueue) external onlyAllocator {
  uint256 length = newSupplyQueue.length;
  if (length > MAX_QUEUE_LENGTH) revert CuratedErrorsLib.MaxQueueLengthExceeded();
  for (uint256 i; i < length; ++i) {
    IERC20(asset()).forceApprove(address(newSupplyQueue[i]), type(uint256).max);
    if (config[newSupplyQueue[i]].cap == 0) revert CuratedErrorsLib.UnauthorizedMarket(newSupplyQueue[i]);
  }
  supplyQueue = newSupplyQueue;
  emit CuratedEventsLib.SetSupplyQueue(_msgSender(), newSupplyQueue);
}
```

This function doesn't check for duplicates, allowing the same market to be added multiple times


### Impact


The presence of duplicates in the supply queue can lead to several issues:

a) Inflated deposit limits:
The `_maxDeposit` function in CuratedVaultGetters.sol iterates through the supply queue:

```solidity
function _maxDeposit() internal view returns (uint256 totalSuppliable) {
  for (uint256 i; i < supplyQueue.length; ++i) {
    IPool pool = supplyQueue[i];
    uint256 supplyCap = config[pool].cap;
    if (supplyCap == 0) continue;
    // ... (calculation logic)
    totalSuppliable += supplyCap.zeroFloorSub(supplyAssets);
  }
}
```

If a market appears multiple times, its available capacity will be counted multiple times, inflating the total suppliable amount.

### PoC


1. An allocator sets the supply queue with duplicates:
   ```solidity
   IPool[] memory newQueue = new IPool[](3);
   newQueue[0] = IPool(address(0x1));
   newQueue[1] = IPool(address(0x2));
   newQueue[2] = IPool(address(0x1)); // Duplicate
   vault.setSupplyQueue(newQueue);
   ```

2. A user attempts to deposit a large amount:
   ```solidity
   uint256 depositAmount = 1000000 * 1e18; // 1 million tokens
   vault.deposit(depositAmount, user);
   ```

3. The `_maxDeposit` function will count the capacity of the duplicate market twice, potentially allowing a deposit larger than the actual available capacity.

4. The `_supplyPool` function will attempt to supply to the duplicate market twice, potentially leading to unexpected behavior or reverts.

This bug can be triggered because there are no safeguards against duplicates in the `setSupplyQueue` function, and the subsequent logic assumes a unique set of markets in the queue.


### mitigation

1. Implement duplicate checking in `setSupplyQueue`.
2. Consider using a set data structure for the supply queue.
3. Add de-duplication logic in functions that iterate through the queue.
