Polite Spruce Duck

Medium

# Debt and interest continue to accrue even when asset/protocol is paused


## Summary

Debt is unfairly still accrued even when the pausable `BIT` has been set.

## Vulnerability Detail

Like in AAVE, there is a logic to indicate [which assets are paused](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveConfiguration.sol#L44), issue however is that debt continue to accrue even when the asset is paused.

To explain more, the debt calculation only depends on the timestamp elapse, now before calling every function, to sync the timestamp the below is called:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87-L95

```solidity
  function updateState(DataTypes.ReserveData storage self, uint256 _reserveFactor, DataTypes.ReserveCache memory _cache) internal {
    // If time didn't pass since last stored timestamp, skip state update
    if (self.lastUpdateTimestamp == uint40(block.timestamp)) return;

    _updateIndexes(self, _cache);
    _accrueToTreasury(_reserveFactor, self, _cache);

    self.lastUpdateTimestamp = uint40(block.timestamp);
  }
```

And then [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L69-L80) we get the value for the debt, from which we can see how the updated timestamp is being used:

```solidity
  function getNormalizedDebt(DataTypes.ReserveData storage reserve) internal view returns (uint256) {
    uint40 timestamp = reserve.lastUpdateTimestamp;

    //solium-disable-next-line
    if (timestamp == block.timestamp) {
      //if the index was updated in the same block, no need to perform any calculation
      return reserve.borrowIndex;
    } else {
      return MathUtils.calculateCompoundedInterest(reserve.borrowRate, timestamp).rayMul(reserve.borrowIndex);
    }
  }

```

Now after an unpause, any subsequent [borrow](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L62)/ supply, [liquidation](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L108)/flashloan logic would [always trigger `reserve.updateState()` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L164)which would then accrue even more debts for users.

## Impact

Unfair accural of debts for users when asset/protocol is paused, considering they can't do anything atp.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/ReserveConfiguration.sol#L44
- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87-L95

## Tool used

Manual Review

## Recommendation

Do not accrue more debts when the protocol is paused.
