Basic Midnight Tortoise

Medium

# Add default constructor that calls `_disableInitializers()` to `Pool' upgradable contracts.

## Summary

`Pool` uses upgradable contracts.  
	`Pool`  -> `PoolSetters` -> `PoolRentrancyGuard` -> `Initializable`
Calling the `initialize()` function directly on the implementation contract behind a proxy is dangerous. 
In such case, if the implementation calls self-destruct or performs delegate calls it’s possible to delete the implementation leaving the contract bricked. 

## Vulnerability Detail

Upgradable contracts are an essential protocol feature, allowing for flexible updates and maintenance. 
Contracts should include a default constructor calling `_disableInitializers()` function: 
(https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable/blob/master/contracts/proxy/utils/Initializable.sol#L192) of `Initializable.sol`.

## Impact

When implementing upgradable contracts, ensuring that the `initialize()` function is not accidentally called directly on the implementation contract behind the proxy is crucial. 

If this occurs and the implementation contract contains self-destruct or delegate calls, it can result in the unintended deletion of the implementation contract.

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L37

## Tool used

Manual Review

## Recommendation

Please insert Pool#`constructor()` as follows.

```diff
contract Pool is PoolSetters {
	...
	
++	constructor() {
++		_disableInitializers();
++	}
	
	...
}
```
