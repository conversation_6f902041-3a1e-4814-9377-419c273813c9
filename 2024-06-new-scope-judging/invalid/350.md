Chilly Cherry Deer

Medium

# Lack of price oracle check before `amountInBaseCurrency`  leading to incorrect calculation of the user's borrowing capacity

### Summary

The lack of validation for oracle data in `validateBorrow` will cause an over-borrowing for the protocol as users will borrow more than their collateral should allow based on inflated asset prices.
```solidity
 vars.amountInBaseCurrency = IPool(params.pool).getAssetPrice(params.asset) * params.amount;
```
- [ValidationLogic.sol: Lines 124-168](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L124-L168)




### Root Cause

In `validateBorrow` function, the logic `vars.amountInBaseCurrency = IPool(params.pool).getAssetPrice(params.asset) * params.amount;` lacks validation of the price data fetched from the oracle. This absence of validation can lead to incorrect calculations as the oracle can provides stale prices. This is important to check as per current protocol supports more than two assets and in cross way.


### Impact

`leads to over-borrowing then protocol potentially being under-collateralized due to over-borrowing`

##### Example showing current logic impact
- asset: ETH ; True Market Price: 2,000 USDC per ETH, 
- Oracle Price (Unvalidated/stale price): 2,500 USDC per ETH
- Collateral: 10 ETH
- Loan-to-Value (LTV) Ratio: 50%
- 1. Correct Calculation:
    - `Collateral Value: 10 ETH * 2,000 USDC = 20,000 USDC`
    - `Maximum Borrowing Capacity: 20,000 USDC  * 50% LTV = 10,000 USDC`
    
- 2. Calculation with Unvalidated Oracle Price:
    - `Collateral Value: 10 ETH * 2,500 USDC = 25,000 USDC`
    - ` Maximum Borrowing Capacity: 25,000 USDC * 50% LTV = 12,500 USDC`
    
Impact
- The protocol assumes the inflated oracle price is accurate, leading to a higher perceived collateral value. This results in the user being allowed to borrow 12,500 USDC instead of the correct 10,000 USDC, causing an over-borrowing of 2,500 USDC.


### Mitigation

- Implement validation of price oracle before using it in `amountInBaseCurrency`.
- also you can validate parallel that `is borrow allowed` for that price oracle in same statement.