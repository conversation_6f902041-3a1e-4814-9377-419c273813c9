Brave Ruby Dinosaur

Medium

# `CuratedVault` withdrawals can be DoS'ed using the function `skim`

## Summary
`CuratedVault` withdrawals can be DoS'ed using the `skim` function. Whenever a withdrawal is triggered from the `CuratedVault`, malicious actors can front-run that transaction with another transaction that calls the `skim` function with the asset token address as input. As a result, there will be no asset tokens left in the vault to withdraw. which eventually leads to a DoS attack. 

## Vulnerability Detail
An attacker can use the `skim` function to execute a DoS attack on asset withdrawals in the `CuratedVault` by calling the `skim` function with the asset token address as input. The attacker can front-run the withdraw/redeem function with a `skim` function call, transferring the asset tokens out of the vault to a skim recipient. This will eventually cause the withdrawal transaction to fail due to insufficient funds in the vault.

Consider a scenario where an attacker monitors all withdrawal transactions happening in the vault and then tries to front-run them by calling the `skim` function with the asset token address as input. This can lead to a denial of service (DoS) on withdrawals within the vault.

## Impact
asset token withdrawls are forever DOS'ed in the vault. 
## Code Snippet
```solidity
@> function skim(address token) external {
     if (skimRecipient == address(0)) revert CuratedErrorsLib.ZeroAddress();
     uint256 amount = IERC20(token).balanceOf(address(this));
@>   IERC20(token).safeTransfer(skimRecipient, amount);
     emit CuratedEventsLib.Skim(_msgSender(), token, amount);
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L312
## Tool used
Manual Review

## Recommendation
check the `token` address is not equal to the `asset` token address 
```solidity
require(token != asset, "Cannot skim the asset token"); 
```