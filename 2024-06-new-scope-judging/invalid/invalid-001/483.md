Happy Midnight Griffin

High

# getReward function will always return zero resulting the user to not receive any rewards.

### Summary

getReward function has error in code that causes the user to not receive any rewards.

### Root Cause

Let us discuss first the [getReward](https://github.com/sherlock-audit/2024-06-new-scope-bluenights004/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L88-L96) function , as you can see below, in line 90, the reward to be paid to user is being taken from mapping of rewards. This rewards mapping is being updated in line 89 _updateReward.

```Solidity
 File: NFTRewardsDistributor.sol
88:   function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
89:     _updateReward(tokenId, _assetHash);
90:     reward = rewards[tokenId][_assetHash];
91:     if (reward > 0) {
92:       rewards[tokenId][_assetHash] = 0;
93:       rewardsToken.transfer(ownerOf(tokenId), reward);
94:       emit RewardPaid(tokenId, ownerOf(tokenId), reward);
95:     }
96:   }
```
The code below is the detail of _updateReward, please notice in line 153 that the lastUpdateTime is being updated by lastTimeRewardApplicable, the value of this can be always the current blocktimestamp as long as it is lesser than the periodFinish. periodFinish pertains to the ending period of reward duration time. One thing also to notice on this line 153, is the lastUpdateTime will be compared to lastTimeRewardApplicable later in earned function which will call the rewardPertoken to find the difference. This will be discuss further in later part which will be the main cause of the bug.

 Please notice also the line 155, we will discuss the earned function later as this is very important because this is the source of reward amount to be distributed to user.

```Solidity
File: NFTRewardsDistributor.sol
151:   function _updateReward(uint256 _tokenId, bytes32 _assetHash) internal {
152:     rewardPerTokenStored[_assetHash] = rewardPerToken(_assetHash);
153:     lastUpdateTime[_assetHash] = lastTimeRewardApplicable(_assetHash); //@audit this is a bug 
154:     if (_tokenId != 0) {
155:       rewards[_tokenId][_assetHash] = earned(_tokenId, _assetHash);
156:       userRewardPerTokenPaid[_tokenId][_assetHash] = rewardPerTokenStored[_assetHash];
157:     }
158:   }
159: 
```
```Solidity
File: NFTRewardsDistributor.sol
73:   function lastTimeRewardApplicable(bytes32 _assetHash) public view returns (uint256) {
74:     return block.timestamp < periodFinish[_assetHash] ? block.timestamp : periodFinish[_assetHash];
75:   }
76: 
```
Then next, we need to check the function rewardPertoken, you will notice here the line 82, in which there should be difference between lastTimeRewardApplicable and lastUpdatetime so it can add update to the mapping rewardPerTokenStored. If there is no difference, this variable mapping will remain the same from last execution.
```Solidity
File: NFTRewardsDistributor.sol
77:   function rewardPerToken(bytes32 _assetHash) public view returns (uint256) {
78:     if (_totalSupply[_assetHash] == 0) {
79:       return rewardPerTokenStored[_assetHash];
80:     }
81:     return rewardPerTokenStored[_assetHash].add(
82:       lastTimeRewardApplicable(_assetHash).sub(lastUpdateTime[_assetHash]).mul(rewardRate[_assetHash]).mul(1e18).div(
83:         _totalSupply[_assetHash]
84:       )
85:     );
86:   }
```
Then let's discuss the earned function, this is being called in line 155 under _updateReward function, you will notice that this can only update the rewards mapping if there is a difference between the rewardPerToken and userRewardPerTokenPaid. However upon checking, these two figure will always be the same and thus no difference will be added to rewards mapping.
```Solidity
File: NFTRewardsDistributor.sol
098:   function earned(uint256 tokenId, bytes32 _assetHash) public view returns (uint256) {
099:     return _balances[tokenId][_assetHash].mul(rewardPerToken(_assetHash).sub(userRewardPerTokenPaid[tokenId][_assetHash])).div(1e18).add(
100:       rewards[tokenId][_assetHash]
101:     );
102:   }
```
Why do i say the rewardPerToken and userRewardpertokepaid will always be the same? Because of the following
1. The rewardPerToken remain the same compare to the last execution because the lastUpdatetime and lastTimeRewardApplicable is the same.
2. The userRewardperTokenPaid is equal with rewardPerToken. In other words, rewardPerToken is not moving or updating.

Since the earned function can't produce any update to rewards mapping, rewards mapping will always be zero.



### Internal pre-conditions

1. The borrower or depositor has deposited funds or open a loan.

### External pre-conditions

_No response_

### Attack Path

There is no attack path as this can happen in normal reward claiming scenario.

### Impact

Any borrower or depositor won't be able to claim their rewards.

### PoC

_No response_

### Mitigation
This part of code should be modified in order not to produce zero result to rewards mapping.
```Solidity
153:     lastUpdateTime[_assetHash] = lastTimeRewardApplicable(_assetHash); 
```