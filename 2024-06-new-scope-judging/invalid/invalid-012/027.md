Dazzling Sand Griffin

Medium

# Anyone can create pools not only pool Manager

## Summary
`PoolFactory.createPool()` isn't restricted to only poolManager

## Vulnerability Detail
```solidity
 /// @inheritdoc IPoolFactory
  function createPool(DataTypes.InitPoolParams memory params) external returns (IPool pool) {
   

    // create the pool
    if (params.revokeProxy) {
      pool = IPool(address(new RevokableBeaconProxy(address(this), address(this))));
      IRevokableBeaconProxy(address(pool)).revokeBeacon();
    } else {
      if (params.proxyAdmin == address(0)) params.proxyAdmin = msg.sender;
      pool = IPool(address(new RevokableBeaconProxy(address(this), params.proxyAdmin)));
    }

    emit PoolCreated(pool, pools.length, msg.sender, params);

    // track the pool
    pools.push(pool);
    isPool[address(pool)] = true;

    // init the pool and give roles to the user
    pool.initialize(params);
    configurator.initRoles(IPool(address(pool)), params.admins, params.emergencyAdmins, params.riskAdmins);
  }

```

According to the diagram in zerolend github official repo,  creation of pools is supposed to be only designated to poolManager
https://github.com/zerolend/zerolend-one/blob/master/data-flow.png

The issue here is that anyone can create pools not only pool Manager

## Impact
Anyone can create pools not only pool Manager
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L69
## Tool used

Manual Review

## Recommendation
Put a check to ensure only pool Manager is able to create pools in pool factory