Dazzling Sand Griffin

Medium

# Anyone can create CuratedVaults not only Risk Manager

## Summary
creating of curatedVaults in curatedVaultFactory.sol isn't restricted to only the Risk manager.
## Vulnerability Detail
According to the doc in zerolend official github repo, creation of Curated Vaults is assigned to only Risk Manager
https://github.com/zerolend/zerolend-one/blob/master/data-flow.png

The issue here is that the createVault() function isn't restricted so that only the Risk Manager can access it.
```solidity
 function createVault(InitVaultParams memory p) external returns (ICuratedVault vault) {// @audit lacks access control
    // create the vault
    vault = ICuratedVault(address(new RevokableBeaconProxy{salt: p.salt}(address(this), p.proxyAdmin)));
    emit CuratedEventsLib.VaultCreated(vault, vaults.length, p, msg.sender);

    vault.initialize(p.admins, p.curators, p.guardians, p.allocators, p.timelock, p.asset, p.name, p.symbol);

    // track the vault
    vaults.push(vault);
    isVault[address(vault)] = true;
  }

```
## Impact
Anyone can create CuratedVaults not only Risk Manager
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultFactory.sol#L48
## Tool used

Manual Review

## Recommendation
restrict createVault() to only  Risk manager