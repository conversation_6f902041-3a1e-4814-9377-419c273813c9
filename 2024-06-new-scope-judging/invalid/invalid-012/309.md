Furry Plastic Lemur

High

# Unrestricted Pool Creation Leads to Liquidity Manipulation

## Summary

The PoolFactory contract allows anyone to create a new pool without restrictions .

## Vulnerability Detail

The createPool function in the PoolFactory contract lacks access control, allowing anyone to create a new pool without restrictions. This could lead to unauthorized pool creation, potentially resulting unintended consequences.

## Impact

Unintended Pool Creation 

an attacker can exploit the unrestricted pool creation vulnerability to create malicious pools potentially leading  manipulation of liquidity

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L69-L68

## Tool used

Manual Review

## Recommendation

Add the onlyOwner modifier to restrict pool creation to only the contract owner.


function createPool(DataTypes.InitPoolParams memory params) external onlyOwner {
    // Validate user input
    require(params.revokeProxy, "Proxy revocation required");
    require(params.proxyAdmin != address(0), "Proxy admin required");
    
    // Create pool with restricted access
    IPool pool = IPool(address(new RevokableBeaconProxy(address(this), params.proxyAdmin)));
    IRevokableBeaconProxy(address(pool)).revokeBeacon();
    
    // Emit event
    emit PoolCreated(pool, pools.length, msg.sender, params);
    
    // Track pool
    pools.push(pool);
    isPool[address(pool)] = true;
    
    // Initialize pool with restricted access
    pool.initialize(params);
    configurator.initRoles(IPool(address(pool)), params.admins, params.emergencyAdmins, params.riskAdmins);
}