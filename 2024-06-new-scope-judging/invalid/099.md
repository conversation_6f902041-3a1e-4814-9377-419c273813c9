Chilly Cherry Deer

Medium

# The premature conversion of large calculation results to `uint128` without ensuring they fit within the data type's limits, will cause incorrect financial calculations for the protocol's users

### Summary

The premature conversion of large calculation results to `uint128` without ensuring they fit within the data type's limits, will cause incorrect financial calculations for the protocol's users. Incorrect liquidity indices can lead to erroneous calculations of interest, potentially causing significant financial discrepancies.

```solidity
function _updateIndexes(DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (_cache.currLiquidityRate != 0) {
      uint256 cumulatedLiquidityInterest = MathUtils.calculateLinearInterest(_cache.currLiquidityRate, _cache.reserveLastUpdateTimestamp);
// @audit-issue
// 1. `rayMul` Operation: This line multiplies `cumulatedLiquidityInterest` by `_cache.currLiquidityIndex` using fixed-point arithmetic to maintain precision.
// 2. Conversion to uint128: The result of the multiplication is immediately converted to uint128.
      _cache.nextLiquidityIndex = cumulatedLiquidityInterest.rayMul(_cache.currLiquidityIndex).toUint128();
// 3. It should be coverted in this line of code
      _reserve.liquidityIndex = _cache.nextLiquidityIndex;
    }

    if (_cache.currDebtShares != 0) {
      uint256 cumulatedBorrowInterest = MathUtils.calculateCompoundedInterest(_cache.currBorrowRate, _cache.reserveLastUpdateTimestamp);
// @audit-issue 
      _cache.nextBorrowIndex = cumulatedBorrowInterest.rayMul(_cache.currBorrowIndex).toUint128();
      _reserve.borrowIndex = _cache.nextBorrowIndex;
    }
  }
```
#### Issue: The conversion to uint128 is applied immediately after the multiplication
- When  large result is converted to uint128, it will be truncated to fit within the uint128 range, losing the most significant bits. This truncation results in a drastically smaller and incorrect value for the index.

- uint128: This is an unsigned integer type in Solidity that can store values from 0 to (2^{128} - 1).
- uint256: This type can store much larger values, up to (2^{256} - 1).
- Maximum Value for uint128 : 2^128 - 1 ≈ 3.4 × 10^38
- cumulatedLiquidityInterest: uint256
- _cache.currLiquidityIndex: uint128
- `cumulatedLiquidityInterest.rayMul(_cache.currLiquidityIndex)` : multiplication involves a uint256 and a uint128. In Solidity, arithmetic operations between these types are performed using the larger type (uint256 in this case), which helps prevent overflow during the multiplication itself.
- Issue: After the multiplication, the result is converted to uint128. 
- Formula: cumulatedLiquidityInterest*currLiquidityIndex / 10**27 


### Root Cause

- calculations involving large numbers are directly converted to uint128 without ensuring that the results fit within the maximum value that uint128 can store ((2^{128} - 1)). 

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L226
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L236

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Incorrect liquidity indices can lead to erroneous calculations of interest, potentially causing significant financial discrepancies.

### PoC

_No response_

### Mitigation

_No response_