Chilly Cherry Deer

Medium

# Missing reserve status checks in `validateLiquidationCall` lead liquidations to proceed on reserves that are inactive, paused, or frozen.

### Summary

The `validateLiquidationCall` function does not check the active, paused, or frozen status of reserves, this lead to liquidations proceed on reserves that are inactive, paused, or frozen.
- [validateLiquidationCall function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L214-L229)

```solidity
  function validateLiquidationCall(
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ValidateLiquidationCallParams memory params
  ) internal view {
    ValidateLiquidationCallLocalVars memory vars;
   
    // @audit-issue missing collateralReserve - active & paused checks
    // this lead to liquidations proceed on reserves that are inactive, paused 

    require(params.healthFactor < HEALTH_FACTOR_LIQUIDATION_THRESHOLD, 
    PoolErrorsLib.HEALTH_FACTOR_NOT_BELOW_THRESHOLD);

    vars.isCollateralEnabled =
      collateralReserve.configuration.getLiquidationThreshold() != 0 && userConfig.isUsingAsCollateral(collateralReserve.id);

    //if collateral isn't enabled as collateral by user, it cannot be liquidated
    require(vars.isCollateralEnabled, PoolErrorsLib.COLLATERAL_CANNOT_BE_LIQUIDATED);
    require(params.totalDebt != 0, PoolErrorsLib.SPECIFIED_CURRENCY_NOT_BORROWED_BY_USER);
  }
```
-  Current logic : The function checks the user's health factor, collateral enablement, and outstanding debt.
- Missing status checks:
  - Active status: Ensures reserves are operational and capable of handling liquidations.
  - Paused status: Prevents actions on reserves undergoing maintenance or risk mitigation.
  - given struct in code:
```solidity
struct ValidateLiquidationCallLocalVars {
    bool collateralReserveActive;
    bool collateralReservePaused;
    bool principalReserveActive;
    bool principalReservePaused;
    bool isCollateralEnabled;
  }
```

### Root Cause

In the [validateLiquidationCall function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L214-L229), there is a missing check for the reserve's active, paused status. This oversight could allow liquidations to proceed on reserves that are not operationally.


### Impact

The protocol suffers potential operational disruptions and financial instability as liquidations may occur on reserves that are inactive, or paused. This increases the risk of executing liquidations under inappropriate conditions, which could affect the protocol's stability.

### Mitigation

- Add status check of active and pause before checking `healthFactor` and other checks. Possible ways:
```solidity
    require(vars.collateralReserveActive , Errors.RESERVE_INACTIVE);
    require(!vars.collateralReservePaused, Errors.RESERVE_PAUSED);
```
- Also add `principalReserveActive`, `principalReservePause`, in above require statement using `&& `