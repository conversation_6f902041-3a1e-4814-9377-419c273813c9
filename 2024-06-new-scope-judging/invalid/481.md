Low Lemon Dove

High

# wrong caluclation leads to not   properly  function and revert

### Summary

in **LiquidationLogic**  when **bonusCollateral** is calculated  it tries to subtract  the liquidation bonus from vars.bonusCollateral  and  liquidation bonus is calculated by percentage   but the  thing is that   **liquidationBonus** is greeter than PERCENTAGE_FACTOR so the returned amount will be greater than vars.bonupsCollateral so due to that it will underflow and revert        `vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);`


### Root Cause

in LiquidationLogic:367 ` vars.collateralAmount.percentDiv(liquidationBonus`) will be greater than    vars.collateralAmount  which will undeflow and revert  

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L367

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

the function will not properly work and revert     

### PoC

_No response_

### Mitigation

_No response_