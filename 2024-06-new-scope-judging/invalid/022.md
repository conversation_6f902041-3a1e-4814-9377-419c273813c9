Blunt Brunette Snail

Medium

# Creation of multiple PTokens with identical names and symbols

**Description**
``PointTokenVault::deployPToken``  is vulnerable to an attack where an existing PToken can be duplicated, creating a new token with an identical name and symbol but a different `_pointsId`. This vulnerability arises from how the contract uses `_pointsId` as a unique identifier for tokens, without additional checks on the uniqueness of the name and symbol combinations.

Vulnerable function:
https://github.com/sherlock-audit/2024-07-sense-points-marketplace-0xRaz-b/blob/fcbab4d473f9d87a5f99c6efc3233ea755d8072d/point-tokenization-vault/contracts/PointTokenVault.sol#L259C1-L267C6


The core of the issue lies in the fact that different `_pointsId` values can result in the same name and symbol when unpacked, due to unused bits in the bytes32 value. This allows an attacker to create a new token that appears identical to an existing one.

An attacker can exploit this vulnerability as follows:

1. Identify an existing PToken in the system.
2. Create a new `_pointsId` by slightly modifying the bits of the original `_pointsId`, ensuring it unpacks to the same name and symbol.
3. Deploy a new PToken using this modified `_pointsId`.

You can paste that code in `PointTokenVault.t.sol`:

```javascript
function test_duplicateExistingToken() public {
    // Deploy original token
    bytes32 originalPointsId = LibString.packTwo("Existing Token", "EXT");
    PToken originalToken = pointTokenVault.deployPToken(originalPointsId);

    // Create and deploy duplicate token
    bytes32 duplicatePointsId = originalPointsId | bytes32(uint256(1));
    PToken duplicateToken = pointTokenVault.deployPToken(duplicatePointsId);

    // Verify duplication
    assertEq(originalToken.name(), duplicateToken.name());
    assertEq(originalToken.symbol(), duplicateToken.symbol());
    assertTrue(address(originalToken) != address(duplicateToken));
}
```

**Impact**

It can lead to significant user confusion, potentially resulting in financial losses if users interact with the wrong token. Malicious actors could exploit this to create fraudulent duplicates of legitimate tokens, facilitating scams and eroding trust in the platform.

**Recommended Mitigation:**

Implement a name and symbol uniqueness check in the `PointTokenVault::deployPToken`.

Here are the needed modifications :

```javascript
+   mapping(bytes32 => bool) private nameSymbolExists; // Need to be added as a state variable 


    function deployPToken(bytes32 _pointsId) public returns (PToken pToken) {
        if (address(pTokens[_pointsId]) != address(0)) {
            revert PTokenAlreadyDeployed();
        }

        (string memory name, string memory symbol) = LibString.unpackTwo(_pointsId); // Assume the points id was created using LibString.packTwo.
+       bytes32 nameSymbolHash = keccak256(abi.encodePacked(name, symbol));
+       require(!nameSymbolExists[nameSymbolHash], "Token with this name and symbol already exists");
+       nameSymbolExists[nameSymbolHash] = true;

        pToken = new PToken{salt: _pointsId}(name, symbol, 18);

        pTokens[_pointsId] = pToken;

        emit PTokenDeployed(_pointsId, address(pToken));
    }

```
