Great Jade Shetland

Medium

# Liquidity providers will not take profit from flash loan

## Summary

When a flash loan is taken there is a premium paid to the protocol, this premium is directly minted to the treasury which means liquidity providers will never get any profit. Unlike Aave, where part of the premium is distributed to liquidity providers.

## Vulnerability Detail

1. User takes a flash loan of amount X
2. User repays the flash loan with an amount of X + Y (where Y is the premium)
3. Repayment is handled in `FlashLoanLogic::_handleFlashLoanRepayment` and all the premium is minted to the treasury
```solidity
_reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
```

## Impact
No profit is given to liquidity providers on a flash loan, only minted to the treasury.

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116

## Tool used

Manual Review

## Recommendation

Split the flash loan premium into 2 parts, a part given to the LPs, and the other part is for the treasury.