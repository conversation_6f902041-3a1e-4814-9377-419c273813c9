Brave Ruby Dinosaur

Medium

# Incorrect Fee calculation for tokens with different decimals in `CuratedVaultGetters::_accruedFeeShares()`

## Summary

The `_accruedFeeShares()` function is designed to compute the fee shares (`feeShares`) to be minted and the new total assets (`newTotalAssets`) of the vault. However, the method used to calculate feeAssets assumes that the token being used has 18 decimals (1e18), which can lead to incorrect fee calculations when tokens with different decimal places, such as USDC (which has 6 decimals), are used. This discrepancy can cause significant financial errors, either overcharging or undercharging fees.

## Vulnerability Detail

The issue arises in the `_accruedFeeShares()` function, where the `feeAssets` are calculated by multiplying the `totalInterest` by the fee and then dividing by `1e18`. This calculation assumes that the underlying asset token has 18 decimals, which is typical for many tokens like ETH or DAI but not for others like USDC. If the vault is dealing with a token like USDC, which has 6 decimals, this calculation will not be accurate, leading to incorrect fee assessments. This could either cause an underestimation or overestimation of the fees to be minted, impacting the fee recipient and the vault's total asset calculation.

## Impact
Due to this type of fee calculation the feeAssets can be 0 for tokens like USDC which typically has 6 decimals, This will results in 0 fee collection even if the vault owner intended to collect fee for the vaults based on USDC as asset token. 

## Code Snippet

```solidity
  /// @dev Computes and returns the fee shares (`feeShares`) to mint and the new vault's total assets
  /// (`newTotalAssets`).
  function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
    newTotalAssets = totalAssets();

    uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets);
    if (totalInterest != 0 && fee != 0) {
      // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
      //@audit 
     @> uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
      // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
      // that total assets is already increased by the total interest (including the fee assets).
      feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
    }
  }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L191
## Tool used

Manual Review

## Recommendation

To address this vulnerability, the code should be adjusted to account for the decimals of the underlying asset token dynamically. Instead of assuming a fixed 1e18 decimals, the calculation should use the token's `decimals()` method to determine the appropriate scaling factor. This will ensure that the feeAssets are calculated correctly regardless of the token used in the vault.

```solidity

uint256 tokenDecimals = 10 ** IERC20(asset).decimals();
uint256 feeAssets = totalInterest.mulDiv(fee, tokenDecimals);

```