Steep Aqua Yak

Medium

# Borrower can not repay all position debt

## Summary
Incorrect integration with `Pool` contract causes `NFTPositionManager` can not support borrowers to repay all position debt

## Vulnerability Detail
The `Pool` contract allows borrowers to repay [all position debt by setting `params.amount = type(uint256).max`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L128-L131).
However, [it always fails if `type(uint256).max` is used as amount to repay in `NFTPositionManager.repay()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L69) function due to lack of balance because there will be almost impossible to have max uint256 balance amount

## Impact

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L128-L131

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L69

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L105-L140

## Tool used

Manual Review

## Recommendation
Adding to logic to update `params.amount` in function `NFTPositionManager.repay` when user opts to repay all debt, by:
1. Update pool reserve to accrue interest
2. Query position debt balance
3. Set `params.amount` equals to debt balance above