Fit Bone Eel

Medium

# When a user attempts to withdraw the maximum amount of ETH, an error occurs

### Summary

In the `SupplyLogic.executeWithdraw()` function, the protocol allows users to input the maximum amount. When `params.amount == type(uint256).max`, the protocol sets `params.amount` to the user's balance. 

```solidity
  uint256 balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex);

    // repay with max amount should clear off all debt
    if (params.amount == type(uint256).max) params.amount = balance;

```
The `NFTPositionManager.withdraw()` function also supports `params.amount == type(uint256).max`. 
```solidity
  function withdraw(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    _withdraw(params);
  }


```

However, `withdrawETH()` does not support the maximum amount input. Although `_withdraw()` can succeed, `weth.withdraw(params.amount)` will fail, causing inconsistency between `withdraw()` and `withdrawETH()`.

```solidity

  function withdrawETH(AssetOperationParams memory params) external payable {
    params.asset = address(weth);
    address dest = params.target;
    params.target = address(this);
    _withdraw(params);
    weth.withdraw(params.amount);
    (bool ok,) = payable(dest).call{value: params.amount}('');
    if (!ok) revert NFTErrorsLib.SendETHFailed(params.amount);
  }

```
Additionally, when the user calls `withdraw()` with `params.amount == type(uint256).max`, the log recorded in the `_withdraw()` function is incorrect, as it should log the `balance` instead of `params.amount`.
```solidity
    // update incentives
    uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
    _handleSupplies(address(pool), params.asset, params.tokenId, balance);

    emit NFTEventsLib.Withdraw(params.asset, params.amount, params.tokenId);
  }


```

### Root Cause

When withdrawing the maximum amount of ETH, the protocol does not handle the conversion.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L110

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

Calling `withdrawETH()` with `params.amount` set to `type(uint256).max` will result in an error.

### Impact

When calling `withdrawETH()` with `params.amount == type(uint256).max`, an error occurs.

### PoC

```solidity
  function withdrawETH(AssetOperationParams memory params) external payable {
    params.asset = address(weth);
    address dest = params.target;
    params.target = address(this);
    _withdraw(params);
    weth.withdraw(params.amount);
    (bool ok,) = payable(dest).call{value: params.amount}('');
    if (!ok) revert NFTErrorsLib.SendETHFailed(params.amount);
  }
```

### Mitigation

When withdrawing ETH, adjust `params.amount` if it is set to `type(uint256).max`.