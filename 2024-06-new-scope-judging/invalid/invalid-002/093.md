Rapid Onyx Meerkat

Medium

# borrower can't repay without leaving dust from interest via `NFTPositionManager`

### Summary

Borrowers can set params.amount to type(uint256).max to repay the loan without leaving any residual interest ("dust"). However, when repaying assets through the NFTPositionManager, borrowers must first transfer the specified params.amount to the NFTPositionManager. If the transferred amount exceeds the required repayment, the excess assets are returned to the borrower. Consequently, borrowers are unable to set params.amount to type(uint256).max, resulting in repayments that leave dust from the interest

### Root Cause

From the [BorrowLogic::executeRepay](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L117-L131)
```solidity
  function executeRepay(
    DataTypes.ReserveData storage reserve,
    DataTypes.PositionBalance storage balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.ExecuteRepayParams memory params
  ) external returns (DataTypes.SharesType memory payback) {
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);
    reserve.updateState(params.reserveFactor, cache);
    payback.assets = balances.getDebtBalance(cache.nextBorrowIndex);

    // Allows a user to max repay without leaving dust from interest.
    if (params.amount == type(uint256).max) {
      params.amount = payback.assets;   <@
    }
```
Borrower can set `params.amount` to repay without leaving dust from interest.

From [NFTPositionManager.sol::repay](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L116-L120) 
```solidity
  function repay(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    IERC20Upgradeable(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);
    _repay(params);
  }
```
we can see borrower have to first transfer requried asset to pool.


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

1.borrower can't repay without leaving dust from interest

### PoC

```solidity
  function testBorrowerCantRepayAll() public {
  //alice supply
  vm.warp(1641070800);
  uint256 mintAmount = 100 ether;
  DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
  INFTPositionManager.AssetOperationParams memory params =
    INFTPositionManager.AssetOperationParams(address(tokenA), alice, 2e18, 1, data);

  _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

  vm.startPrank(alice);
  nftPositionManager.mint(address(pool));
  nftPositionManager.supply(params);

  //alice borrow
  params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, 1e18, 1, data);
  nftPositionManager.borrow(params);

  //alice repay.
  params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, type(uint256).max, 1, data);
  nftPositionManager.repay(params);
}
```

out:
```shell
Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 5.04ms (1.17ms CPU time)

Ran 1 test suite in 147.73ms (5.04ms CPU time): 0 tests passed, 1 failed, 0 skipped (1 total tests)

Failing tests:
Encountered 1 failing test in test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[FAIL. Reason: revert: ERC20: insufficient allowance] testBorrowerCantRepayAll() (gas: 793736)
```

### Mitigation

transfer assets to pool directly instead of transfer it to `NFTPositionManager`