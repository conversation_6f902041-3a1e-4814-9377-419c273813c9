Acrobatic Rainbow Grasshopper

Medium

# Users might not be able to repay their debt fully using the NFT position manager

## Summary
Users might not be able to repay their debt fully using the NFT position manager
## Vulnerability Detail
Users can interact with the NFT position manager in order to earn rewards using a staking mechanism. The NFT position manager essentially manages positions in the `Pool` contract. Thus, users can do different operations that are possible in the `Pool` contract like supplying and borrowing. One of the lines we have in the repayment flow in the `Pool` contract is this one:
```solidity
    if (params.amount == type(uint256).max) {
      params.amount = payback.assets;
    }
```
The goal is for the users to be able to fully repay their loan as they have no way of predicting the amount of debt their position will have at the time of the transaction (as it grows with `block.timestamp`). In order to repay their loan using the position manager, they will call this function:
```solidity
  function repay(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    IERC20Upgradeable(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);
    _repay(params);
  }
```
The function transfers the provided amount to the NFT position manager contract and then it calls `_repay()`. Just based on the transfer, we can clearly see that the functionality where the user provides `type(uint256).max` in order to fully repay the loan is impossible as there is no way he has that amount of assets (if the pool asset is actually a legit token). In the `_repay()` function, we have this line:
```solidity
DataTypes.SharesType memory repaid = pool.repay(params.asset, params.amount, params.tokenId, params.data);
```
The line calls the `repay()` function on the pool with the provided amount and then we eventually go here:
```solidity
    payback.assets = balances.getDebtBalance(cache.nextBorrowIndex);

    // Allows a user to max repay without leaving dust from interest.
    if (params.amount == type(uint256).max) {
      params.amount = payback.assets;
    }
```
We already saw some of these lines. First, we get the debt and then we check whether the amount is `type(uint256).max` which as mentioned, is impossible if interacting with the position manager. Due to the way the rest of the function is coded, if the user has actually provided an amount >= the debt, we would actually be able to fully repay the debt. Then, we have this line back in the position manager:
```solidity
if (currentDebtBalance == 0 && repaid.assets < params.amount) {
      asset.safeTransfer(msg.sender, params.amount - repaid.assets);
    }
```
If the user has fully repaid the loan and has provided a bigger amount than what was repaid, we refund him. So the user could theoretically fully repay his debt by providing an amount >= the debt and then getting the excess (if >) back. Firstly, we can completely ignore the scenario where the user provides amount equal to the debt as it is pretty much impossible for that to happen due to the borrow index growing based on `block.timestamp`. That leaves two options, the user trying to fully repay his debt but providing amount lower than the debt and a scenario where he provides more than the debt. The first scenario makes it so he didn't fully repay his debt which is not what he actually wanted. The second scenario would work in a good amount of cases but here are a few issues that can occur
- The user is supposed to repay a certain amount of debt but he is forced to pay more funds than the debt, however he doesn't actually have those extra funds and he won't be able to transfer them in initially to then get the refund
- The user has a bit more than what the debt would be at the time of execution, thus he is trying to pay just a bit more than the debt, however due to the unknown time of the transaction (due to `block.timestamp`), he actually underpays and still has debt

Both scenarios are possible as the user is forced to pay more than his debt and he might not even have that extra amount as he is not actually supposed to pay it but he is forced to do so if he actually wants to repay his debt.
## Impact
Users might not be able to repay their debt fully using the NFT position manager
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L116-L120
## Tool used

Manual Review

## Recommendation
Handle the case where the user provides `type(uint256).max` as amount