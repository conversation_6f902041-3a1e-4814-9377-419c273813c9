Massive Glass Crane

High

# poolSorage.sol:: Missing the keyword abstract for PoolStorage abstract contract

### Summary

contract PoolStorage is declared as contract that needs to be deployed instead of `abstract` Contract.

### Root Cause

missig keyword `abstract` for PoolStorage Contract.
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolStorage.sol#L26
### Internal pre-conditions
_No response_


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

PoolStorage needs to be implemented seperately for each pools.Causing a great loss to protocol.

### PoC


### Mitigation

add `abstract` keyword
