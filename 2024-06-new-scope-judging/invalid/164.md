Rural Eggshell Sheep

Medium

# using Token ID and supply seperatly makes  the protocol vulnerable to L1 re-orgs.

### Summary

>On what chains are the smart contracts going to be deployed?
>
>Ethereum and Linea primarily. And after that any EVM-compatible network.

> [sponser said  : taking inspiration from Uniswap V3 Nftposition manager ](https://www.youtube.com/watch?v=KJsykgz99j4&t=462s)

In the [ZeroLend protocol](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L58-L79),  first mints Nft position and supply tokens in that position in different functions.

Where as, In [UniswapV3](https://github.com/Uniswap/v3-periphery/blob/main/contracts/NonfungiblePositionManager.sol#L128-L182) both the mint Nft position and supply tokens / addLiquidity is within the same function.So , Zerolend is suspectible from  L1 re-orgs.



### Root Cause

In ``NFTPositionManager:L58-L79``, both mint NFT position and supply is done seperatly.

### Internal pre-conditions

_No response_

### External pre-conditions

1. <PERSON>hain re-org should occur

### Attack Path


- Assume the following unfinalized blocks: 

* Block 1: Alice calls mint() to mint a position with id  10. 
* Block 2: Alice calls supply() with 1 ETH and passing in id = 10.
* Block 3: Bob calls mint() to mint a position : Its id is 11.
  
- An L1 re-org occurs, placing block 3 before block 1. The new order of execution is: 

* Block 3 is executed: Bob call mint() with id =10.
* Block 1 is executed: Alice calls mint() with id =11.
* Block 2 is executed: Alice calls supply() 1 ETH with id 10;

- Alice sends 1  ETH at id = 10 , instead of id =11.


### Impact

Loss of funds for honest users.

### PoC

_No response_

### Mitigation

make both mint and supply/ supplyETH in the same function as in [UniswapV3](https://github.com/Uniswap/v3-periphery/blob/main/contracts/NonfungiblePositionManager.sol#L128-L182).
and add a seperate function for increasing supply of the position.