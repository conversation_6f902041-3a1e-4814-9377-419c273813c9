Fresh Sangria Mink

Medium

# Lack of Support for fee-on-transfer tokens e.g USDC

## Summary

## Vulnerability Detail
According to the `Readme`, protocol intends to integrate USDC tokens,
> Only standard ERC20 tokens + USDC and BNB are in-scope.

`USDT` is fee-on-transfer tokens but code fails to account for these descrapencies in accounting as tokens are deposited, withdraw, borrowed or repayed e.g `CuratedVault.sol`, Vault intialized with USDT, shares are issued to the depositer not according to the actual assets sent to the vault, which are less than the amount transfer but the parameter provided in the function.

if token incurs fee on transfer, the actually transferred amount will be less than the provided parameter amount and the deposits will have a wrong state value. As Users withdraw their tokens the latter users will receive less than what they deposited and in some cases withdrawls can revert or protcol can reach insolvency. 
## Impact
Use of USDC tokens can lead to denial of service.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L69
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L118
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L137
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L93
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L126
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L81
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L146
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L99
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L158
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L188
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L192
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L229
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L98
## Tool used

Manual Review

## Recommendation
Use before and after balance to accurately reflect the true amount received, and update share price accordingly.