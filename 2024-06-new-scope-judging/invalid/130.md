Delightful Tin Nightingale

Medium

# Reorg issues in `PoolFactory` may lead to locked funds

### Summary

[PoolFactory is deploying a beacon proxy using create opcode](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolFactory.sol#L69-L89) and arbitrary arguments. If someone manages to exploit a reorg and use his arguments, he may be able to damage a victim, who has already used the address derived from original deployment. [Reorgs](https://abarbatei.xyz/blockchain-reorgs-for-managers-and-auditors) are commonly happening on Mainnet and Polygon. Protocol plans to deploy on Mainnet and it should be compatible with any other EVM chain.

### Root Cause

Using `create` opcode in a factory contract:
```solidity
    if (params.revokeProxy) {
      pool = IPool(address(new RevokableBeaconProxy(address(this), address(this))));
      IRevokableBeaconProxy(address(pool)).revokeBeacon();
    } else {
      if (params.proxyAdmin == address(0)) params.proxyAdmin = msg.sender;
      pool = IPool(address(new RevokableBeaconProxy(address(this), params.proxyAdmin)));
    }
``` 

### Internal pre-conditions

- Victim deploys pool through `PoolFactory` and after that calls `newPoolAddress.deposit` and deposit some funds

### External pre-conditions

- Reorg happening on-chain, has pool creation and deposit transactions in different blocks.

### Attack Path

1. Victim calls `PoolFactory.createPool` with WETH, DAI and WBTC. The beacon proxy is deployed on address `X`
2. Victim calls `X.supply` with $10K DAI
3. Reorg is happening and exploiter manages to put his transaction before `1.` and `2.` from above
4. Exploiter calls `PoolFactory.createPool` deploying new pool, which has the same tokens, 0% interest and 100% ltv for DAI against WETH borrowing.
5. Now on address `X` a malicious actor pool is deployed with malicious setup. 
6. When `2.` hits execution, malicious actor instantly borrows all $10K DAI against $10K WETH 
7. Victim's funds are locked in a different pool and are locked without accruing yield
8. Expoiter can blackmail the victim to pay him % of the locked funds, so he can repay the debt and unlock his funds. 

### Impact

Locked funds 

### PoC


### Mitigation

Use `create2` opcode when deploying new beacons in `PoolFactory` using `msg.sender` in the `salt`