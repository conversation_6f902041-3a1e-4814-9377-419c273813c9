Joyous Rainbow Shark

Medium

# Lack of guardrails in the system significantly increases the likelihood of bad debt occuring

### Summary

Whilst `PoolLogic::setReserveConfiguration()` does implement some sanity checks on `LTV`, `liquidationThreshold`, and `LiquidationBonus`, there is little protection against the occurance of bad debt within the system. 

### Root Cause

Upon pool creation, the  `Pool::initialize()` is [invoked](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L86) from the Pool Factory contract. This function [calls](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L42-L52) `PoolLogic::executeInitReserve()`, which [calls](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L65) `PoolLogic::setReserveConfiguration()`. 


In this function shown below:
1. There is a check that `liquidationThreshold` is greater than or equal to `LTV`. The issue is the closer these values are the closer an opened position can be to immediately liquidation.
2. There is a check that `liquidationBonus` is greater than 100%, however this should be much higher to ensure liquidating small positions remains profitable (especially on mainnet).


```javascript
  function setReserveConfiguration(
    mapping(address => DataTypes.ReserveData) storage _reserves,
    address asset,
    address rateStrategyAddress,
    address source,
    DataTypes.ReserveConfigurationMap memory config
  ) public {
    ... SKIP!...

@>  require(config.getLtv() <= config.getLiquidationThreshold(), PoolErrorsLib.INVALID_RESERVE_PARAMS); // 1

    if (config.getLiquidationThreshold() != 0) {
      // liquidation bonus must be bigger than 100.00%, otherwise the liquidator would receive less
      // collateral than needed to cover the debt
@>    require(config.getLiquidationBonus() > PercentageMath.PERCENTAGE_FACTOR, PoolErrorsLib.INVALID_RESERVE_PARAMS); // 2

      // if threshold * bonus is less than PERCENTAGE_FACTOR, it's guaranteed that at the moment
      // a loan is taken there is enough collateral available to cover the liquidation bonus
      require(
        config.getLiquidationThreshold().percentMul(config.getLiquidationBonus()) <= PercentageMath.PERCENTAGE_FACTOR,
        PoolErrorsLib.INVALID_RESERVE_PARAMS
      );

    ... SKIP!...
    }
  }
```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

1. Bad debt will be fragmented all over the protocol
2. A borrow can leave a user on the brink of liquidation, and importantly all of the debt cannot be liquidated profitably by the liquidators (ie. all collateral will be seized, but debt will be outstanding)
3. Small positions won't be liquidated, especially on mainnet

### PoC

_No response_

### Mitigation

- Implement a mandatory buffer between `liquidationThreshold` and `LTV` to eliminate the possibility of borrowers being liquidated upon borrowing, resulting in bad debt. The larger the required buffer the less bad debt the protocol will incur. 
- Change the `liquidationBonus` to a fixed + small variable component. This ensures there is incentive to liquidate small positions, especially on mainnet.