Big Admiral Dove

Medium

# Un-timelocked update of pool supply cap can cause instant DOS of deposit

## Summary

An unintended instant update of pool supply cap will make a pool frozen for deposit operations.

## Vulnerability Detail

`CuratedVault::submitCap()` function applies new cap value for a given pool. It can be instantly updated or timelocked according to the comparison between old cap and new one.

```solidity
  function submitCap(IPool pool, uint256 newSupplyCap) external onlyCuratorRole {
    if (pendingCap[pool].validAt != 0) revert CuratedErrorsLib.AlreadyPending();
    if (config[pool].removableAt != 0) revert CuratedErrorsLib.PendingRemoval();
    uint256 supplyCap = config[pool].cap;
    if (newSupplyCap == supplyCap) revert CuratedErrorsLib.AlreadySet();

    if (newSupplyCap < supplyCap) {
      _setCap(pool, newSupplyCap.toUint184());
    } else {
      pendingCap[pool].update(newSupplyCap.toUint184(), timelock);
      emit CuratedEventsLib.SubmitCap(_msgSender(), pool, newSupplyCap);
    }
  }

```

If the new cap is greater than the current one, it won't raise any problems. But if less, the pool might requires some time to adjust assets so that it can fit to the new cap, and that's why timelock functionality is adopted here.

But as can be seen in the code snippet, timelock is applied when the new cap is greater than old cap, which is completely unnecessary.

On the contrary, when the new cap is less than old cap, the new cap is instantly applied to the pool.

## Impact

The un-timelocked update of lower supply cap will instantly cause make a pool frozen for deposit operations.

## Code Snippet

[vaults/CuratedVault.sol:L154](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L154)

## Tool used

Manual Review

## Recommendation

Should change the issued line like the below:

```diff
  function submitCap(IPool pool, uint256 newSupplyCap) external onlyCuratorRole {
    ... ...
-   if (newSupplyCap < supplyCap) {
+   if (newSupplyCap > supplyCap) {
      _setCap(pool, newSupplyCap.toUint184());
    } else {
      pendingCap[pool].update(newSupplyCap.toUint184(), timelock);
      emit CuratedEventsLib.SubmitCap(_msgSender(), pool, newSupplyCap);
    }
  }
```
