Great Jade Shetland

Medium

# Allocators can add malicious contracts as supply queues in Curated Vaults, allowing them to steal deposited funds

### Summary

Curated vaults are vaults made for users to earn passive income by depositing funds into a vault, then they get deposited into different ZeroLend pools to earn interest. The creation of these vaults is permissionless, i.e. they can be created by anyone, and the set of different roles (curator/allocator) can also be anyone.

The allocator can add/remove supply queues (pool) from the vault using `CuratedVault::setSupplyQueue`, however, that function doesn't check if the passed supply queues are legit and known pool created from the pool factory, so an allocator can pass any contract to that function that mimics the pool's interface and the TX won't revert.

This allows a vault allocator to steal all future funds deposited into the vault.

### Root Cause

`CuratedVault::setSupplyQueue` doesn't check if the new supply queues are "known" pools, created from the pool factory.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L177-L189)

### Impact

Allocators can steal all users' deposited funds into curated vaults.

### PoC

1. An allocator of a curated vault called `CuratedVault::setSupplyQueue` while passing a malicious contract, that has a similar interface as the pool.
2. `setSupplyQueue` doesn't check the validity/honesty of those supply queues but rather just approves the max balance and sets them as the new supply queues.
3. Bob calls `CuratedVault::deposit` to deposit some funds into the curated vault -> pools
4. `CuratedVaultSetters::_supplyPool` is called and funds are deposited into each pool using:
```solidity
function _supplyPool(uint256 assets) internal {
  for (uint256 i; i < supplyQueue.length; ++i) {
    IPool pool = supplyQueue[i];

    ...

    if (toSupply > 0) {
      // Using try/catch to skip markets that revert.
      try pool.supplySimple(asset(), address(this), toSupply, 0) {
        assets -= toSupply;
      } catch {}
    }

    if (assets == 0) return;
  }

  ...
}
```
5. Funds end up in the allocator's malicious contact rather than a known/legit pool.

### Mitigation

Pass the pool factory to the Curated vault contract, and whenever the allocator wants to update the supply queues check if that passed contract is a "known" pool, by adding something like the following in `CuratedVault::setSupplyQueue`.
```solidity
require(factory.isPool(address(newSupplyQueue[i])), ...);
```