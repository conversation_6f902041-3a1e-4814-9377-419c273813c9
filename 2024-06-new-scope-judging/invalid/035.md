Acrobatic Rainbow Grasshopper

Medium

# No sequencer uptime check when fetching price from Chainlink

## Summary
No sequencer uptime check when fetching price from Chainlink
## Vulnerability Detail
The contest README specifies that the protocol will be deployed on all EVM-compatible chains.  To fetch a price from Chainlink, we use `PoolGetters::getAssetPrice()`:
```solidity
function getAssetPrice(address reserve) public view override returns (uint256) {
    (, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(_reserves[reserve].oracle).latestRoundData();
    require(price > 0, 'Invalid price');
    require(block.timestamp <= updatedAt + 1800, 'Stale Price');
    return uint256(price);
  }
```
As seen, there is no check for the sequencer. Such a check is extremely important for chains like Arbitrum which is a fully EVM-compatible chain.
## Impact
No sequencer uptime check when fetching price from Chainlink
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163
## Tool used

Manual Review

## Recommendation
Include checks for the sequencer