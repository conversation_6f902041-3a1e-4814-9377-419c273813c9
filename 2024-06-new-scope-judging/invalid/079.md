Deep Yellow Tiger

Medium

# Protocol is not fully compliant with ERC4626 spec.

### Summary

CuratedVault especially in `CuratedVaultGetters.sol` there are missing implementations of functions for `previewDeposit`, `previewWithdraw`, `previewMint` and `previewRedeem`.

EIP4626 states that the aforesaid methods must be implemented in order to be compliant with the given proposal. In order to: 
>  be optimized for integrators with a feature complete yet minimal interface.

### Root Cause

It can be clearly seen that we are missing these functions in the `CuratedVault` package (all of the abstract contracts and their inheritors).

Although being public in the provided by openzeppelin, that can be seen [here](https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable/blob/c2f21a6d80235b715358453cf32e76e01be15887/contracts/token/ERC20/extensions/ERC4626Upgradeable.sol#L174-L191) 

The official page of the EIP states that:
> Integrators of EIP-4626 Vaults should be aware of the difference between these view methods when integrating with this standard. Additionally, note that the amount of underlying assets a user may receive from redeeming their Vault shares (previewRedeem) can be significantly different than the amount that would be taken from them when minting the same quantity of shares (previewMint). The differences may be small (like if due to rounding error), or very significant (like if a Vault implements withdrawal or deposit fees, etc). Therefore integrators should always take care to use the preview function most relevant to their use case, and never assume they are interchangeable.

Meaning the ZeroLand protocol should have it's own implementation for these before-mentioned functions.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Protocols that try to integrate with  ZeroLend, expecting `CuratedVault` to be ERC-4626 compliant, will encounter multiple issues that may damage ZeroLand's position on the market.



### PoC

All official ERC-4626 requirements are on their [official page](https://eips.ethereum.org/EIPS/eip-4626). Non-compliant/missing methods are:

**previewDeposit**:
> Allows an on-chain or off-chain user to simulate the effects of their deposit at the current block, given current on-chain conditions.

**previewMint**:
> Allows an on-chain or off-chain user to simulate the effects of their mint at the current block, given current on-chain conditions.

**previewRedeem**:
> Allows an on-chain or off-chain user to simulate the effects of their redeemption at the current block, given current on-chain conditions.

**previewWithdraw**:
> Allows an on-chain or off-chain user to simulate the effects of their withdrawal at the current block, given current on-chain conditions.

> Note that any unfavorable discrepancy between convertToShares and previewWithdraw SHOULD be considered slippage in share price or some other type of condition, meaning the depositor will lose assets by depositing.

### Mitigation

Write an implementation to these functions that has all of the protocol factors included. Trying to keep the `preview*` simulation close to the real estimates (eg. what would be the withdrawn amount if an user call the `withdraw` function).