Fresh Sangria Mink

Medium

# Use `_safeMint` instead of `_mint`

## Summary

## Vulnerability Detail
`NFTPositionManager::mint` uses `_mint` of `ERC721Upgradeable` 
## Impact
Using `_mint` can mint ERC721 tokens to addresses which don't support ERC721 tokens, while `_safeMint` ensures that ERC721 tokens are only minted to addresses which support them.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L63
## Tool used

Manual Review

## Recommendation
Use `_safeMint` instead of `mint` with nonReentrant modifier