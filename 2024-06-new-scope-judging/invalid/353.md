Modern Mauve Badger

Medium

# User will self liquidate and take the bonus amount

### Summary

A user will be liquidated if their collateral is insufficient to cover the borrowed amount. However, there is no restriction preventing a user from liquidating themselves. The user facing liquidation can initiate self-liquidation, claim the bonus amount, and settle the required debt.


### Root Cause

No check to prevent the Self liquidation in [Liquidation Logic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94-L197).


### Internal pre-conditions

The user position does not cover the debt.


### External pre-conditions

The collateral price falls below the liquidation threshold.


### Attack Path

1. <PERSON> supplies 550 tokenA to the pool, while <PERSON> supplies 750 tokenB to the same pool.
2. <PERSON> borrows 100 tokenB from the pool.
3. After some time, the price of tokenA decreases, and Alice's collateral falls below the liquidation threshold.
4. <PERSON> calls the `liquidate` function to liquidate her own position or front run other liquidator call.
5. The liquidation is successful, allowing <PERSON> to pay off the required debt and receive the liquidation bonus along with the remaining collateral.


### Impact

The user may prefer to self-liquidate or front-run other liquidators to liquidate themselves and secure the liquidation bonus.


### PoC

```solidity
  function testLiquidationSimple1_Liquidate_SELF() external {
    _generateLiquidationCondition();
    (, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);
// alice tries to liquidate her self
    _mintAndApprove(alice, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    vm.startPrank(alice);
    vm.expectEmit(true, true, true, false);
    emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, alice);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 10 ether);

    vm.stopPrank();

    (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);

    assertTrue(totalDebtBase > totalDebtBaseNew);
  }

```

run test with `forge test --mt testLiquidationSimple1_Liquidate_SELF`.


### Mitigation

Either prevent the liquidation bonus from being paid out in cases of self-liquidation or restrict users from self-liquidating.
