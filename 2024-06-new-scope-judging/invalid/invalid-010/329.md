Clever Ebony Halibut

Medium

# Pool admins are able to cause loss of funds for vaults by setting malicious interestRateModels

## Summary
Pool admins could set malicious interestrate models for the pool reserves leading to either a loss of funds or a loss of funds

## Vulnerability Detail
As stated in the Readme the sponsors wanted to know about ways trusted roles in the protocol can harm other roles. In the following report we will show how a malicious pool owner could brick the the pool withdrawals, which would harm the curated Vaults that use them
this is the part of the Readme, that explicity ask for this:
> For permissioned functions, please list all checks and requirements that will be made before calling the function.
> Essentially we expect all permissioned actors to behave rationally.
> There are two set of actors. Actors who manage pools and actors who mange vaults. If an action done by one party causes the other party to suffer losses we'd want to consider that


The zeroLend allows pool admins to set custom inteerst model using the poolConfigurator.

```solidity
  function setReserveInterestRateStrategyAddress(IPool pool, address asset, address newRateStrategyAddress) external onlyPoolAdmin(pool) {
    DataTypes.ReserveData memory reserve = pool.getReserveData(asset);
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    address oldRateStrategyAddress = reserve.interestRateStrategyAddress;
    pool.setReserveConfiguration(asset, newRateStrategyAddress, address(0), config);
    emit ReserveInterestRateStrategyChanged(asset, oldRateStrategyAddress, newRateStrategyAddress);
  }
```
There are no checks on the validity of the interestRateModel, which could allow malicious owner to set malicious interestRate Models. Knowing also the updateINterestRate model is called on every state changing call, having a contract that will always revert, could lock users funds in the contract. 
## Impact
This misconfiguration privilege enables pool owners to disrupt the marketplace by:

- Freezing the vault by setting malicious interest rates that cause transactions to revert.
- Causing overall loss of user funds and trust in the platform due to manipulative interest rate models.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L105-L111

## Tool used

Manual Review

## Recommendation
To mitigate this issue, and add more trust to the ecosystem, we recommend tha the protocol implements a whitelisting process.
E.g prepare some interest model that can be trusted (rates are capped and never reverting) and only allow using those in the pools