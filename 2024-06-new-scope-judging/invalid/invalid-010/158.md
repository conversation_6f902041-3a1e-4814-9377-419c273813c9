Careful Fleece Pike

High

# `POOL_ADMIN_ROLE` can set a pool's `interestRateStrategyAddress` to a bad address to cause loss of funds to a vault

### Summary

`POOL_ADMIN_ROLE` can set `interestRateStrategyAddress` to a bad address to cause loss of funds to a vault.

### Root Cause

Per the contest's `README`
> There are two set of actors. Actors who manage pools and actors who mange vaults. If an action done by one party causes the other party to suffer losses we'd want to consider that.

In the `PoolConfigurator` contract, the `POOL_ADMIN_ROLE` can set the pool's `interestRateStrategyAddress` after the pool deployment

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L105-L111

```solidity
  function setReserveInterestRateStrategyAddress(IPool pool, address asset, address newRateStrategyAddress) external onlyPoolAdmin(pool) {
    DataTypes.ReserveData memory reserve = pool.getReserveData(asset);
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    address oldRateStrategyAddress = reserve.interestRateStrategyAddress;
    pool.setReserveConfiguration(asset, newRateStrategyAddress, address(0), config);
    emit ReserveInterestRateStrategyChanged(asset, oldRateStrategyAddress, newRateStrategyAddress);
  }
```

### Internal pre-conditions

The vault has some funds in the pool.

### External pre-conditions

_No response_

### Attack Path

The `POOL_ADMIN_ROLE` calls to `PoolConfigurator#setReserveInterestRateStrategyAddress` with `newRateStrategyAddress` equals to a bad address (E.g: `address(0x1337)`). This would cause the function `Pool#withdrawSimple` always revert

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L155

because of the call to `interestRateStrategyAddress#calculateInterestRates` always revert

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L160-L171

### Impact

The vault loses all the funds that are supplied to the pool.

### PoC

_No response_

### Mitigation

There are two ways to mitigate this:
1. Remove the function `setReserveInterestRateStrategyAddress` in the `PoolConfigurator` contract
2. Add a list of whitelisted addresses in the `PoolFactory`. The pool's `interestRateStrategyAddress` can only be set to one of these addresses.