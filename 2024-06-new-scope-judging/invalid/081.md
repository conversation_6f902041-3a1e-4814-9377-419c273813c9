Able Concrete Jellyfish

High

# Unrestricted Flash Loan Exploit in Transactions

## Summary
The `flashLoan` and `flashLoanSimple` functions in the `Pool` contract allow for unrestricted borrowing of tokens. This lack of restrictions can be exploited to manipulate market prices and liquidity, leading to potential economic losses for users and connected protocols.

## Vulnerability Detail
The `flashLoan` function allows borrowing of tokens without adequate checks on the amount or return conditions.
```solidity
  function flashLoan(address receiverAddress, address asset, uint256 amount, bytes calldata params, DataTypes.ExtraData memory data) public {
    _flashLoan(receiverAddress, asset, amount, params, data);
  }

  function flashLoanSimple(address receiverAddress, address asset, uint256 amount, bytes calldata params) public {
    _flashLoan(receiverAddress, asset, amount, params, DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
```
- The attacker can perform a flash loan for a large amount of USDC.
- Using the borrowed USDC, the attacker executes trades on a decentralized exchange (DEX) to manipulate the price of a paired asset.
- The attacker then executes arbitrage trades on various other platforms to take advantage of the manipulated price.
- Finally, the attacker returns the borrowed USDC to the `flashLoan` function, completing the transaction and keeping the profit from the price manipulation.

## Impact
- Attackers can temporarily change token prices on decentralized exchanges, leading to potential arbitrage opportunities and market instability.
- Users and protocols that rely on accurate price feeds may experience financial losses due to manipulated prices.

## Code Snippet
- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L140-L142
- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L145-L147

## Tool used

Manual Review

## Recommendation
- Add strict checks on loan amounts and repayments.
- Implementation of protection mechanisms against price manipulation.
```diff
+function _flashLoan(
+    address receiverAddress,
+    address asset,
+    uint256 amount,
+    bytes calldata params,
+    DataTypes.ExtraData memory data
+) internal {
+    uint256 availableLiquidity = _getAvailableLiquidity(asset);
+    require(amount <= availableLiquidity, "Amount exceeds available liquidity");

+    uint256 fee = _calculateFlashLoanFee(amount);
+    uint256 totalRepayment = amount + fee;

       // Transfer the loan amount to the receiver
+    IERC20(asset).transfer(receiverAddress, amount);

       // Execute the receiver's operation
+    IFlashLoanReceiver(receiverAddress).executeOperation(asset, amount, fee, params);

       // Ensure repayment
+    uint256 balanceAfter = IERC20(asset).balanceOf(address(this));
+    require(balanceAfter >= totalRepayment, "Insufficient repayment amount");

       // Emit an event for transparency
+    emit FlashLoanExecuted(receiverAddress, asset, amount, fee);
}

+function _calculateFlashLoanFee(uint256 amount) internal view returns (uint256) {
       // Define a fixed fee percentage, e.g., 0.09% (9 basis points)
+    uint256 feePercentage = 9;
+    return (amount * feePercentage) / 10000;
}

+function _getAvailableLiquidity(address asset) internal view returns (uint256) {
       // Retrieve the available liquidity for the asset
+    return IERC20(asset).balanceOf(address(this));
}
```
