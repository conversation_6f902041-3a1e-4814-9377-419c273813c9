Curly Pineapple Armadillo

Medium

# A storage gap for Storage contracts has not been implemented

### Summary

When upgradable contracts inherit from other contracts it is important that there are storage gaps in case storage variables are added to inherited contracts. `PoolStorage`, `NFTPositionManagerStorage` and `CuratedVaultStorage` used for the contracts' storage, do not include any storage gaps. This may be detrimental when updating the contracts because upgrades may change the storage slots of all inherited contracts.

### Root Cause

- [`PoolStorage`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolStorage.sol#L26), [`NFTPositionManagerStorage`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerStorage.sol#L22) and [`CuratedVaultStorage`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultStorage.sol#L20) do not include storage gaps

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Upgrading contracts may lead to storage colision.

### PoC

_No response_

### Mitigation

Add a storage gap to `PoolStorage`, `NFTPositionManagerStorage` and `CuratedVaultStorage`.