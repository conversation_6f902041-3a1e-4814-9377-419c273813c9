Clever Ebony Halibut

Medium

# Predefined max supply and borrow caps in the contract are not enough to support certain reserves

## Summary
The supply reserve caps being used in the pool are not well defined to support all the tokens, specially tokens with the really low dollar/eth value per token (e.g memeToken).. Even though zerolend intend to support such tokens, the way the max cap is set in the protocol, it is not possible for pool admins to manage risk and controll the inflow and outflow of such risky assets by setting sufficient caps
## Vulnerability Detail
Pool admins are allowed to set Borrow and supply on a reserve per reserve basic in the Pool Configurator.
```solidity
  function setBorrowCap(IPool pool, address asset, uint256 newBorrowCap) external onlyRiskOrPoolAdmins(pool) {
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    uint256 oldBorrowCap = config.getBorrowCap();
    config.setBorrowCap(newBorrowCap);
    pool.setReserveConfiguration(asset, address(0), address(0), config);
    emit BorrowCapChanged(asset, oldBorrowCap, newBorrowCap);
  }

  /// @inheritdoc IPoolConfigurator
  function setSupplyCap(IPool pool, address asset, uint256 newSupplyCap) external onlyRiskOrPoolAdmins(pool) {
    DataTypes.ReserveConfigurationMap memory config = pool.getConfiguration(asset);
    uint256 oldSupplyCap = config.getSupplyCap();
@>>    config.setSupplyCap(newSupplyCap);
    pool.setReserveConfiguration(asset, address(0), address(0), config);
    emit SupplyCapChanged(asset, oldSupplyCap, newSupplyCap);
  }
```
The problem however is that the code have been forked for AAVE, which is designed to only support well risk averse markets (e.g stable coins, LRT, eth ...), which only allow the max cap to be set to a max of 68_719_476_735 underlying token
```solidity
uint256 internal constant MAX_VALID_SUPPLY_CAP = 68_719_476_735;
```

on supply or borrow if a  cap is set the total supply will be checked against it. e.g for supply

```solidity
  function validateSupply(
    DataTypes.ReserveCache memory cache,
    DataTypes.ReserveData storage reserve,
    DataTypes.ExecuteSupplyParams memory params,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal view {
    require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    (bool isFrozen,) = cache.reserveConfiguration.getFlags();
    require(!isFrozen, PoolErrorsLib.RESERVE_FROZEN);

    uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();
    require(
      supplyCap == 0
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
@>>          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
```

for a risky asset as memetoken, with a lower valuation per token, such as a big market cap Pepe, was constantly trading at a valuation at ~ 0.0000005 usdc at the begin of this year
the max settable supply and borrow cap are at 0.0000005 * 68719476735 ~ 34360 usdc. Which is simply not enough to be able to support such tokens.

Please also note that zero lend already supports some memeTokens and expect the deployment of pools that have them as reserves

## Impact
- supply and borrow cap are not sufficient for some tokens that that the protocol intends to support

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/ReserveConfiguration.sol#L184-L188
## Tool used

Manual Review

## Recommendation

Update the max reserve in the reserveConfiguration to allow for setting higher caps, to allow the effective risk management of different assets