Long Coffee Cat

High

# Rewards are overstated for assets with less than 18 dp and can be stolen by early claimers

### Summary

`NFTRewardsDistributor` incorrectly calculates rewards for assets having non-18 decimals

### Root Cause

It's assumed that `_balances[tokenId]` and `_totalSupply[_assetHash]` have fixed 18 dp:

[NFTRewardsDistributor.sol#L98-L102](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L98-L102)

```solidity
  function earned(uint256 tokenId, bytes32 _assetHash) public view returns (uint256) {
>>  return _balances[tokenId][_assetHash].mul(rewardPerToken(_assetHash).sub(userRewardPerTokenPaid[tokenId][_assetHash])).div(1e18).add(
      rewards[tokenId][_assetHash]
    );
  }
```

[NFTRewardsDistributor.sol#L77-L86](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L77-L86)

```solidity
  function rewardPerToken(bytes32 _assetHash) public view returns (uint256) {
    if (_totalSupply[_assetHash] == 0) {
      return rewardPerTokenStored[_assetHash];
    }
    return rewardPerTokenStored[_assetHash].add(
>>    lastTimeRewardApplicable(_assetHash).sub(lastUpdateTime[_assetHash]).mul(rewardRate[_assetHash]).mul(1e18).div(
        _totalSupply[_assetHash]
      )
    );
  }
```

But it's asset balance figure having the same decimals as `asset`, and can differ from 18:

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165-L183

```solidity
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

>>  _balances[tokenId][_assetHash] = balance;
>>  _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }

  function _handleDebt(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, true);
    uint256 _currentBalance = _balances[tokenId][_assetHash];

    _updateReward(tokenId, _assetHash);

>>  _balances[tokenId][_assetHash] = balance;
>>  _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;
  }
```

E.g. for `USDC` asset `rewardPerToken()` will be overstated, while current balance part will be understated by magnitudes in `earned()`

### Internal pre-conditions

NFTPositionManager is used with non-18 dp asset

### External pre-conditions

Rewards are added for this asset in NFTRewardsDistributor

### Attack Path

No direct attack is needed, it's a malfunction during normal NFTRewardsDistributor workflow

### Impact

Rewards are substantially miscalculated for non-18 dp assets, e.g. can be fully stolen by early claimers in case of low dp assets

### PoC

Any operation with non-18 dp assets will invoke rewards update via `_handleDebt()` and `_handleSupplies()`, that will be incorrect, so rewards end up misallocated

### Mitigation

Consider replacing `1e18` with decimals dependent multiplier in NFTRewardsDistributor