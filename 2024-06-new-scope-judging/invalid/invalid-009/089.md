Tart Paisley Finch

Medium

# Rounding Down in `rewardPerToken()` Leading to Stuck Rewards for Low-Decimal Tokens

## Summary
Due to the low decimals of certain tokens, the calculation in [rewardPerToken()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L81-L85) can result in precision loss, leading to rewards being rounded down to zero. This causes rewards in tokens with low precision to be stuck in the contract.
## Vulnerability Detail
In the `NFTRewardsDistributor` contract, whenever the `_updateReward` function is triggered, the `rewardPerTokenStored[_assetHash]` and `lastUpdateTime[_assetHash]` are updated. The calculation for `rewardPerToken()` involves multiplying the difference between `lastTimeRewardApplicable(_assetHash)` and `lastUpdateTime[_assetHash]` by the `rewardRate[_assetHash]`, then dividing by the `_totalSupply[_assetHash]`. Due to the low precision of certain reward tokens, this calculation may round down to zero, especially when the time difference is small and the total supply is large, resulting in no increase in `rewardPerTokenStored[_assetHash]` and causing rewards to be effectively lost.

```solidity
 function _updateReward(uint256 _tokenId, bytes32 _assetHash) internal {
    rewardPerTokenStored[_assetHash] = rewardPerToken(_assetHash);

    lastUpdateTime[_assetHash] = lastTimeRewardApplicable(_assetHash);

    if (_tokenId != 0) {
      rewards[_tokenId][_assetHash] = earned(_tokenId, _assetHash);

      userRewardPerTokenPaid[_tokenId][_assetHash] = rewardPerTokenStored[_assetHash];
    }
  }


 function rewardPerToken(
    bytes32 _assetHash
  ) public view returns (uint256) {
    if (_totalSupply[_assetHash] == 0) {
      return rewardPerTokenStored[_assetHash];
    }
    return rewardPerTokenStored[_assetHash].add(
      lastTimeRewardApplicable(_assetHash).sub(lastUpdateTime[_assetHash]).mul(rewardRate[_assetHash]).mul(1e18).div(
        _totalSupply[_assetHash]
      )
    );
  }
```
For example, with certain reward tokens that have lower precision, the condition:

`(lastTimeRewardApplicable(_assetHash) - lastUpdateTime[_assetHash]) * rewardRate[_assetHash] * 1e18 / _totalSupply[_assetHash]` 

can cause the reward per token to round down to zero if:

`(lastTimeRewardApplicable(_assetHash) - lastUpdateTime[_assetHash]) * rewardRate[_assetHash] * 1e18 < _totalSupply[_assetHash]`

Hence, until `periodFinish[_assetHash]`, every time someone triggers `_updateReward` earlier than 31 seconds after `lastUpdateTime[_assetHash]`, `rewardPerToken` is not increased, but `lastUpdateTime[_assetHash]` is still updated, so rewards for those 30 seconds are lost. This can also be achieved artificially by an attacker calling `getReward()` every 30 seconds.

## Impact
The contract will not be able to distribute all rewards for low-decimal tokens, due to precision loss, leaving these rewards stuck in the contract.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L77-L86

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L151-L158

## Tool used

Manual Review

## Recommendation
Normalize the `reward` amount to 1e18 when calculating `rewardRate` in the [notifyRewardAmount()](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L131-L137) function:
```diff
 function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
 
  //...
  
    if (block.timestamp >= periodFinish[_assetHash]) {
-     rewardRate[_assetHash] = reward.div(rewardsDuration);
+     rewardRate[_assetHash] = reward * 1e12 / rewardsDuration;
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
-     rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
+     rewardRate[_assetHash] = (reward * 1e12 + leftover) / rewardsDuration;

    }

   //...
  }
```
When claiming rewards in the [getReward()](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L90) function:
```diff
 function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
    _updateReward(tokenId, _assetHash);
-   reward = rewards[tokenId][_assetHash];
+   reward = rewards[tokenId][_assetHash] / 1e12;
    if (reward > 0) {
      rewards[tokenId][_assetHash] = 0;
      rewardsToken.transfer(ownerOf(tokenId), reward);
      emit RewardPaid(tokenId, ownerOf(tokenId), reward);
    }
  }
```
