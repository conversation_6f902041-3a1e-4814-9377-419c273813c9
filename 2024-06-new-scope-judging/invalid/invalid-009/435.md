Joyous Rainbow Shark

Medium

# Claiming dust buildup in `NFTRewardsDistributor` will remove unclaimed rewards for all users

### Summary

Dust builds up on the `NFTRewardsDistributor` contract. The impact is lower for tokens with higher precision, however as meantioned in the readme, USDC is in scope which has 6 decimals of precision. This leads to a non-trivial buildup of dust across the possible `assetHash` that may be rewarded. This dust buildup is supposed to be claimable through the `sweep()` function, however this function transfers all assets off the contract including unclaimed rewards, causing user `getReward()` calls to revert.

### Root Cause

Precision loss occurs on the two lines indicated of [`notifyRewardAmount`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125-L137) below. Some amount of `reward` is divided by the `rewardDuration` and the result is rounded down, leaving the dust amount to accrue on the contract. This value will become significant across all possible `_assetHash` as these are unique for a given pool/asset/isDebt combination, with each requiring their own reward amount.

```javascript
  function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
    rewardsToken.transferFrom(msg.sender, address(this), reward);

    bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
@>    rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
@>    rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }
```

Over time, the admin would like to sweep this dust off the contract, so they call [`NFTPositionManager::sweep()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L131-L139), which transfers all assets off the contract including **all* unclaimed rewards across all pools/assets/users.

```javascript
  function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
      payable(msg.sender).transfer(bal);
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
      erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```

### Internal pre-conditions

- Admin sweeps dust buildup of any `rewardToken`

### External pre-conditions

_No response_

### Attack Path

- After some time, significant dust has built up on the `NFTRewardsDistributor` contract, and the admin would like to sweep it.
- Admin calls `sweep()`
- All pending rewards including ongoing reward distributions and previously completed but unclaimed reward distributions are removed, along with the dust.
- Any users who had pending rewards will not be able to claim them, as `getReward()` will revert.

### Impact

- The impact is variable as it depends on when how many unclaimed rewards existed when `sweep()` was called. 
- There may never be a fair time to call `sweep()`:
  - To have no impact on stakers, it must be called when no ongoing rewards are being distributed across all pools/assets/isDebt combinations, and all pending rewards have been claimed by all users. 
  - It seems like this condition may never be met and therefore the admin is left with a choice to forfeit the dust or remove unclaimed rewards from users.

### PoC

Paste the following test in /test/forge/core/positions/. It shows an admin attempting to sweep dust off the contract, but inadvertently removing Alice's rewards

```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {NFTPostionManagerTest} from './NFTPositionManagerTest.t.sol';
import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {NFTErrorsLib} from 'contracts/interfaces/errors/NFTErrorsLib.sol';
import {console2} from 'forge-std/src/Test.sol';

contract Audit_ClaimingDustRemovesPendingRewards is NFTPostionManagerTest {

  function test_POC_ClaimingDustRemovesPendingRewards() external {
    testShouldSupplyAlice();
    uint256 repayAmount = 10 ether;
    uint256 borrowAmount = 20 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, borrowAmount, 1, data);

    vm.startPrank(alice);
    nftPositionManager.borrow(params);
    assertEq(tokenA.balanceOf(address(pool)), 30 ether, 'Pool Revert');
    assertEq(tokenA.balanceOf(alice), 70 ether, 'Alice Revert');
    vm.stopPrank();

    // Setup reward distribution
    address rewarder = makeAddr('Rewarder');
    vm.startPrank(owner);
    nftPositionManager.grantRole(bytes32(keccak256('REWARDS_ALLOCATOR_ROLE')), rewarder);
    vm.stopPrank();

    // Rewarder allocates
    uint256 rewardAmount = 10e18;
    _mintAndApprove(rewarder, tokenA, rewardAmount, address(nftPositionManager)); 
    vm.startPrank(rewarder);
    nftPositionManager.notifyRewardAmount(rewardAmount, address(pool), address(tokenA), true);
    vm.stopPrank();

    // Time passes
    vm.warp(block.timestamp + 7 days);

    // Halfway through Alice's reward distribution, admin sweeps
    vm.startPrank(owner);
    nftPositionManager.sweep(address(tokenA));
    vm.stopPrank();

    vm.warp(block.timestamp + 7 days);

    // Alice claims
    vm.startPrank(alice);
    bytes32 assetHash = keccak256(abi.encode(address(pool), address(tokenA), true));
    vm.expectRevert('ERC20: transfer amount exceeds balance'); // Alice can't claim as their rewards were swept
    nftPositionManager.getReward(1, assetHash);
    vm.stopPrank();

  }
}
```

### Mitigation

- Keep internal accounting of the accumulated dust as a separate balance from the `rewardToken.balanceOf()`
- Adjust the `sweep()` function to only send the accumulated dust, not all reward tokens.