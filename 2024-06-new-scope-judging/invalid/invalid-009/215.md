Fresh Sangria Mink

Medium

# Loss of rewards due to rounding when the reward token has low decimals

### Summary

`NFTRewardsDistributor::notifyRewardAmount` calculates the reward rate by dividing the amount of rewards by the duration. Some tokens such as USDC, which is in scope as stated by the sponsor, only have 6 decimals, which means it is very easy for rounding errors to cause significant loss of funds. Additionally, rewards are notified by `REWARDS_ALLOCATOR_ROLE` which may distribute rewards as they please, which means that they may choose a distribution over time, possibly hourly or daily, which will trigger the rounding error a lot of times, causing very significant losses.

### Root Cause

In `NFTRewardsDistributor.sol::141` and `NFTRewardsDistributor.sol::145`, the reward rate as `rewardRate[_assetHash] = reward.div(rewardsDuration)` or `rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration)` if there are rewards left. As it does not multiply by any factor before dividing, it will incur rounding errors.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L132
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L136

### Internal pre-conditions

Rounding error due to the added rewards. This is the expected scenario when REWARDS_ALLOCATOR calls the `notifyRewardAmount` function

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The users can suffer loss of rewards when the provider asset has low decimals like BNB and USDC

### PoC

```javascript
function testRoundingErrors() public {
    deal(address(bnb), rewardsAllocator, 1000e8);
    vm.startPrank(rewardsAllocator);
    bnb.approve(address(nftPositionManager), 10e8);
    nftPositionManager.notifyRewardAmount(10e8, address(pool), address(tokenA), false);
    vm.stopPrank();

    uint256 mintAmount = 100 ether;
    uint256 supplyAmount = 50 ether;
    uint256 tokenId = 1;

    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, supplyAmount, tokenId, data);

    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    vm.expectEmit(true, true, true, true);
    emit NFTEventsLib.Supply(address(tokenA), 1, supplyAmount);
    nftPositionManager.supply(params);

    INFTPositionManager.Position memory position = nftPositionManager.positions(1);

    assertEq(position.pool, address(pool));
    assertEq(position.operator, address(0));

    vm.stopPrank();

    skip(14 days);

    bytes32 assetHash = nftPositionManager.assetHash(address(pool), address(tokenA), false);

    vm.startPrank(alice);
    nftPositionManager.getReward(1, assetHash);
    vm.stopPrank();

    
    console.log("BNB balance of NFTPositionManager: ", bnb.balanceOf(address(nftPositionManager)));
    console.log("BNB balance of users: ", bnb.balanceOf(alice));
}
```
>Logs:
>  BNB balance of NFTPositionManager:  870400
>  BNB balance of users:  999129600

0.00870400 BNB remains in the contract

### Mitigation

Multiply the reward rate by rounding factor before diving by the duration to mitigate rounding errors and prevent loss of funds.the 