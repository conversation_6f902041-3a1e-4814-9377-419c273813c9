Sneaky Hazelnut Lizard

Medium

# All the rewards for a particular standard ERC20 token available will not be properly distributed leading to a Permanent loss of yield for stakers due to precision loss.

### Summary

Users are expected to get rewards when they supply and borrow.

But due to Precision loss can lead to a loss of substantial funds, these funds can be huge and are not dust amounts. kindly read the calculation in the vulnerability section

Reward tokens must are standard  ERC-20 tokens which includes WBTC and USDC was also included.

 The token decimal for WBTC is 10**8 thus whenever the reward rate is calculated, a significant amount of funds that should be sent to the users per second are left in the contract.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L131-L137

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L82-L83

From the code snipet above it can be observed that we scaled the REWARDPERTOKENSTORED but the division during REWARDRA<PERSON> calculation introduced the precision loss issue which is not properly handled. 

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

For WBTC -
The reward distributed per seconds

```solidity
  if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }
```

An issue occurs because there is no sufficient wrapping of the amount before dividing by reward duration. The number is divided and later multiplied by time used, causing a loss of precision of the amount modulo remaining time. For the provided period of 2 weeks based on the code implementation , the maximum amount lost can be calculated:

duration= 2*7 * 24 * 3600 = 1,209,600.

Amount = 1 WBTC = 1*10**8.

RewardRATE = 82.671957671957671957671957671958 satoshi per second = 82 per second (SOLIDITY doesn't count the decimal point).

Now let's query the amount that will be distributed
uint256 remaining rewards = (rewardData.finishAt - block.timestamp) * rewardData.rate;

rewards = 1,209,600 * 82 = 99,187,200

Loss due to precision = 100,000,000 - 99,187,200 = 812,800

The worth of the lost amount as of the time of this report is = 812,800 * 54,000/10**8 = $438.912 worth of tokens are not distributed to users.

For WBTC, $438.912 ( at time of writing) will not be distributed to the staker present at the moment. The loss is shared between all users relative to their balance. The loss occurs for every notification, so it is clear losses will be more severe as the distribution continues .

USDC is a 6 decimal token and this error is also present during distribution.

### Impact

Permanent loss of yield per second for stakers due to precision loss.

As a reference the same vulnerability was Report by Trust in March 2024 - https://github.com/code-423n4/2024-03-abracadabra-money-findings/issues/222.

Also check the implementation of Uniswap Unistaker in handling cases like this - https://github.com/uniswapfoundation/UniStaker/blob/887d7dc0c1db3f17227d13af4d8a791a66912d42/src/UniStaker.sol#L618-L621

```solidity
@audit>> amount is scaled to avoid precision loss by uniswap>>
                                scaledRewardRate = (_amount * SCALE_FACTOR) / REWARD_DURATION;
                                  } else {
                                  uint256 _remainingReward = scaledRewardRate * (rewardEndTime - block.timestamp);
                                   scaledRewardRate = (_remainingReward + _amount * SCALE_FACTOR) / REWARD_DURATION;
    }
```

### PoC

_No response_

### Mitigation

Insead of scaling when calculating the rewardpertoken , Store the rewardRate scaled by 1e18, so loss of precision will be lower by magnitude of 1e18. Also divide by the scaling factor whenever we want to utilize rewardrate in any other calculations.