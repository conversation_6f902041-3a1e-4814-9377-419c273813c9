Jumpy Watermelon Mockingbird

Medium

# Precision Loss in notifyRewardAmount Function Leads to Fewer Rewards Distributed

### Summary

In the NFTRewardsDistributor contract, the `notifyRewardAmount()` function calculates the reward rate using integer division, which results in precision loss. This loss accumulates over time, leading to fewer rewards being distributed than intended.

### Root Cause

The use of integer division in the calculation of rewardRate in the notifyRewardAmount function causes truncation of fractional results, leading to a loss of precision.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125-L137

The following line uses safeMath for integer division, which simply returns reward / rewardsDuration. Due to rounding down, this results in precision loss. 
rewardRate[_assetHash] = reward.div(rewardsDuration);

### Internal pre-conditions

1. Allocator calls notifyRewardAmount() with rewards to be distributed. 

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The vulnerability has the following impact: 

- Users receive fewer rewards than intended by the protocol.
- The contract retains an increasing amount of undistributed rewards over time, although this can be withdrawn via `sweep()`

### PoC

A practical PoC is not possible due to the access control restrictions and the use of a transparent proxy for the NFTPositionManagerContract. 

This results in all calls (except `UpgradeToAndCall()`) by the proxyAdmin being revered with the following error: `ProxyDeniedAdminAccess();`.

Hence, it is not possible to grant a user with the role ALLOCATOR to call the `notifyRewardAmount()` function.

### Mitigation

Consider scaling up the reward amount before integer division. 