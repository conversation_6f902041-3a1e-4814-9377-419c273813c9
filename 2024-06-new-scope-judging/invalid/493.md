Massive Glass Crane

Medium

# Users cant execute borrow() even  though the position is healthy , due to a missing "=" check

### Summary
 Only if derived `healthFactor` is `STRICTLY GREATER THAN` the `HEALTH_FACTOR_LIQUIDATION_THRESHOLD` , borrowers are able to borrow.

But in all other functions users health status is considered as healthy even when the `healthFactor` = `HEALTH_FACTOR_LIQUIDATION_THRESHOLD`.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L166
```solidity
=>require(vars.healthFactor > HEALTH_FACTOR_LIQUIDATION_THRESHOLD, PoolErrorsLib.HEALTH_FACTOR_LOWER_THAN_LIQUIDATION_THRESHOLD);
```

Borrowers health status is considered as unhealthy and revert even though they achieve the health status.

### Internal pre-conditions

healthFactor = HEALTH_FACTOR_LIQUIDATION_THRESHOLD

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Borrowers cant borrow even if their position is healthy.


### PoC


### Mitigation

```solidity
=>require(vars.healthFactor >= HEALTH_FACTOR_LIQUIDATION_THRESHOLD, PoolErrorsLib.HEALTH_FACTOR_LOWER_THAN_LIQUIDATION_THRESHOLD);
```

