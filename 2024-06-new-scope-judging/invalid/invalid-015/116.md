Careful Fleece Pike

High

# The incomplete implementation of `sweep` in `NFTPositionManager` will cause the admin can not sweep the residual rewards without disrupting the rewards distribution

### Summary

The incomplete implementation of `sweep`  in `NFTPositionManager` will cause the admin can not sweep the residual rewards without disrupting the rewards distribution.

### Root Cause

When the admin calls to `sweep`, they have to sweep the entire balance of the contract

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L131-L139

```solidity
  function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
      payable(msg.sender).transfer(bal);
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
>>    erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```

### Internal pre-conditions

1. There are users that have unclaimed rewards.
2. `_totalSupply[_assetHash]` is equal to zero during some period that the rewards are giving out, which cause the rewards in this period to not belong to any users.

### External pre-conditions

_No response_

### Attack Path

1. At the start of day 1:
   - Alice supplies `1e18 tokenA`
   - Bob supplies `1e18 tokenA`
   - The admin adds `14e18 tokenB` as rewards for supplying `tokenA` (Note that the `rewardsDuration` is hardcoded to `14 days`)
2. At the end of day 6:
   - Alice withdraws `1e18 tokenA`. She now has `3e18 tokenB` in rewards.
   - Bob withdraws `1e18 tokenA`. He now has `3e18 tokenB` in rewards
3. At the start of day 10:
   - Alice supplies `1e18 tokenA`
4. At the end of day 14:
   - Alice withdraws `1e18 tokenA`. She now has `3e18 + 4e18 = 7e18 tokenB` in rewards.
   - Alice withdraws `7e18 tokenB` rewards using `getReward`

At the end, the `NFTPositionManager` contract has:
- `3e18 tokenB` rewards of Bob, that he has not withdrawn.
- `4e18 tokenB` residual rewards from the end of day 6 to the start of day 10, which does not belong to any users.

Now if the admin wants to claim `4e18 tokenB` residual rewards back, they have to call to `sweep`, but this will also sweep `3e18 tokenB` rewards of Bob. 

Although the admin can call to `getReward` with Bob's `tokenId` to transfer `3e18 tokenB` rewards back to Bob, this would cost the admin a lot of gas when the number of positions that have unclaimed rewards is large.

### Impact

- The admin can not sweep the residual rewards without disrupting the rewards distribution
- In case of the admin calls to `getReward` to every position that has unclaimed rewards, this would become costly in gas when the number of positions that have unclaimed rewards is large.

### PoC

Use `tokenB` as the rewards token in `NftPositionManager`

`DeployNFTPositionManager.t.sol`

```diff
abstract contract DeployNFTPositionManager is PoolSetup {
  NFTPositionManager nftPositionManager;
  address admin = makeAddr('ProxyAdmin');

  function _setup() public {
    NFTPositionManager _nftPositionManager = new NFTPositionManager();
    TransparentUpgradeableProxy proxy = new TransparentUpgradeableProxy(address(_nftPositionManager), admin, bytes(''));
    nftPositionManager = NFTPositionManager(payable(address(proxy)));
-   nftPositionManager.initialize(address(poolFactory), address(0), owner, address(0), address(wethToken));
+   nftPositionManager.initialize(address(poolFactory), address(0), owner, address(tokenB), address(wethToken));
  }
}
```

Run command: `forge test --match-path test/PoC/PoC.t.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {DeployNFTPositionManager} from 'test/forge/core/positions/DeployNFTPositionManager.t.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract NFTPostionManagerTest is DeployNFTPositionManager {
  bytes32 public immutable REWARDS_ALLOCATOR_ROLE = keccak256('REWARDS_ALLOCATOR_ROLE');

  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  uint256 mintAmount = 1 ether;

  function setUp() public {
    _setUpPool();
    _setup();

    vm.prank(owner);
    nftPositionManager.grantRole(REWARDS_ALLOCATOR_ROLE, owner);
    _mintAndApprove(owner, tokenB, 14 ether, address(nftPositionManager));
  }

  function testPoC() public {
    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));
    _mintAndApprove(bob, tokenA, mintAmount, address(nftPositionManager));

    // Start of day 1
    {
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, mintAmount, 1, data);

      vm.startPrank(alice);
      nftPositionManager.mint(address(pool));
      nftPositionManager.supply(params);
      vm.stopPrank();
    }

    {
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), bob, mintAmount, 2, data);

      vm.startPrank(bob);
      nftPositionManager.mint(address(pool));
      nftPositionManager.supply(params);
      vm.stopPrank();
    }

    vm.prank(owner);
    nftPositionManager.notifyRewardAmount(14 ether, address(pool), address(tokenA), false);
    console.log("nftPositionManager's balance before: %e", tokenB.balanceOf(address(nftPositionManager)));

    skip(6 days);

    // End of day 6

    {
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, mintAmount, 1, data);

      vm.prank(alice);
      nftPositionManager.withdraw(params);
    }

    {
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), bob, mintAmount, 2, data);

      vm.prank(bob);
      nftPositionManager.withdraw(params);
    }

    skip(4 days);

    // Start of day 10

    {
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, mintAmount, 1, data);

      vm.startPrank(alice);
      tokenA.approve(address(nftPositionManager), mintAmount);
      nftPositionManager.supply(params);
      vm.stopPrank();
    }

    skip(4 days);

    // End of day 14

    vm.prank(alice);
    nftPositionManager.getReward(1, nftPositionManager.assetHash(address(pool), address(tokenA), false));

    console.log("nftPositionManager's balance after: %e", tokenB.balanceOf(address(nftPositionManager)));
  }
}
```

Logs:

```bash
  nftPositionManager's balance before: 1.4e19
  nftPositionManager's balance after: 7.0000000000000448e18
```

### Mitigation

1. Add a storage variable to track the unclaimed rewards.
2. When sweeping the rewards token, deduct the unclaimed rewards from the balance.