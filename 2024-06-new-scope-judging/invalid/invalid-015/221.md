Acrobatic Rainbow Grasshopper

Medium

# `NFTPositionManager::sweep()` always sweeping the full balance will cause issues

## Summary
`NFTPositionManager::sweep()` always sweeping the full balance will cause issues
## Vulnerability Detail
The admin can call `NFTPositionManager::sweep()` to transfer himself the contract balance of an ERC20 token:
```solidity
  function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
      payable(msg.sender).transfer(bal);
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
      erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```
The token transferred would in pretty much every case be the reward token. The goal of this function, as mentioned by the sponsor, is to transfer out tokens that may have been stuck in the contract (for example, due to precision loss). The issue with this function is that it always transfers out the full balance which can cause huge issues. We will cover multiple scenarios that can occur, let's first imagine a state the contract could be in:
- Rewards duration is 100 seconds
- Rewards deposited are 150 tokens
- Reward rate is thus 1 as 150 / 100 = 1 in Solidity
- 50 tokens are stuck due to the precision loss

Let's first clear the concern that such a round down might not be possible as a trusted party is depositing the rewards. It might not be possible upon the first deposit but upon other deposits, we use `block.timestamp` to determine the remaining time and thus it will be impossible for the trusted party to properly calculate everything in order to not lose precision. Thus, we can assume the above state and furthermore, the developer has implemented the `sweep()` functionality, quite possibly for that exact reason.

Let's imagine that 2 users each have 50 tokens to withdraw from the total of 100 tokens in rewards. The first scenario that can occur is very simple and can be classified as an admin error but is a good example of where we're going with this report:
- As 50 tokens are stuck in the contract, admin calls `sweep()` and all 150 tokens are withdrawn from the contract, thus the reward rates and state variables are completely wrong and users won't be able to withdraw their funds

Another scenario which is a just as problematic and does not involve an admin error is the following:
1. User A withdraws his 50 tokens, now there are 100 tokens in total and 50 tokens for User B to withdraw
2. User B does not withdraw his tokens for some reason so the admin has a choice to either withdraw all of the rewards ruining the contract and making all of the state variables wrong along with User B not being able to withdraw his tokens or he has the choice to simply not withdraw the stuck tokens until User B withdraws his tokens which might never happen, for all we know

It's important to note that the `sweep()` gets the rewards for all asset hashes. Whenever rewards are deposited into the contract, they are attributed to a particular hash based on the pool, asset and the `isDebt` bool. Thus, we would have to wait for all users across all different asset hashes to withdraw their tokens in order for the `sweep()` functionality to not completely ruin the contract. 

We should also point out that the function to get rewards is permissionless. Thus, the admin could forcefully claim the rewards for the user and then sweep the 50 tokens that were stuck. However, if the protocol has thousands of users who have still not claimed their rewards (also factor in that each user can and most likely will have multiple asset hashes), then it would be extremely expensive (especially on mainnet) and bothersome to claim the rewards for thousands of users, thus that is not an actual realistic fix.

Furthermore, this has another issue due to this line:
```solidity
rewardsToken.transfer(ownerOf(tokenId), reward);
```
As seen, we use `ownerOf` to transfer the funds to the owner of the NFT with ID of `tokenId`. That function reverts if the `tokenId` has no owner, thus if the user maliciously burns his token, we will not be able to claim the rewards for him, even forcefully. Of course, that leaves the option to simply sweep all of the tokens for that user however that is also not ideal as we are again, claiming funds that are not actually ours, they are the user's.

As seen, there are multiple scenarios that can occur due to the fact that we are always sweeping the full balance. None of the possible handlings of the situation are perfect and particularly the one where the admin might be required to forcefully claim rewards for thousands of users (very likely scenario), is a serious issue that does not really have a way to properly handle the situation.
## Impact
`NFTPositionManager::sweep()` always sweeping the full balance will cause issues
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L135-L138
## Tool used

Manual Review

## Recommendation
Include an amount parameter as an input to the function