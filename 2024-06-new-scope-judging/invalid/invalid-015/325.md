Curly Pineapple Armadillo

Medium

# Sweeping funds to admin in `NFTPositionManager` may fail

### Summary

Currently when ETH is swept to the admin in the `sweep` function of `NFTPositionManager` `transfer` is used. This is an issue as using `transfer` to transfer ETH is dangerous and may result in a revert. If the admin is a smart contract it is possible for the transfer to fail if the fallback function uses more than 2300 gas units. In the future gas costs might change, further escalating the issue.

### Root Cause

- In [`sweep`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L134C27-L134C35) `transer` is used instead of `call`.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Admin is a smart contract that executes code in its fallback function.
2. Admin attempts to sweep ETH but fails as there is not enough gas to execute the code in the fallback function.

### Impact

Admin may be unable to sweep ETH from `NFTPositionManager`.

### PoC

_No response_

### Mitigation

Use `.call` instead of `.transfer`.