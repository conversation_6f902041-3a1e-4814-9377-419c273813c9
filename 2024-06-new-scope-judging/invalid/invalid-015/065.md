Tart Paisley Finch

Medium

# Transfer of native ETH may fail.

## Summary
The `sweep` function in the `NFTPositionManager` contract uses the transfer method to send ETH to the `msg.sender`. The transfer function forwards a fixed amount of 2300 gas, which can cause ETH transfers to fail under specific conditions. This issue can impact the contract's ability to transfer ETH when interacting with recipients that require more gas.

## Vulnerability Detail
The `sweep` function in the `NFTPositionManager` contract uses the transfer method to send ETH to the `msg.sender`. The transfer function forwards a fixed amount of 2300 gas:
```solidity
  function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
@>    payable(msg.sender).transfer(bal);
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
      erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```
Historically, it has often been recommended to use these functions for value transfers to guard against reentrancy attacks. However, the gas cost of EVM instructions may change significantly during hard forks which may break already deployed contract systems that make fixed assumptions about gas costs. For example. EIP 1884 broke several existing smart contracts due to a cost increase of the SLOAD instruction.

## Impact
The impact of this issue is that ETH transfers via the `sweep` function could fail in scenarios where the recipient requires more than 2300 gas to execute, or when interacting with multisig wallets or through proxies. This failure could lock ETH within the `NFTPositionManager` contract, rendering the `sweep` function ineffective for certain users and potentially causing loss of functionality.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L131-L139

## Tool used
Manual Review

## Recommendation
Use call() instead of transfer().