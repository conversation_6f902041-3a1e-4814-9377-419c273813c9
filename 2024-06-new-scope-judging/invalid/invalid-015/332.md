Happy Corduroy Dalmatian

Medium

# Inconsistency in handling all edge cases of ETH Transfer

## Summary
The ``sweep`` function in the NFTPositionManager contract is responsible for ``transferring`` either ETH or ERC20 tokens from the contract to the admin. While the developer has shown awareness of the deprecated nature of using transfer for ETH transfers by using the call method in other functions (e.g., ``withdrawETH`` and ``borrowETH``), they have opted to use transfer in the ``NFTPositionManager::sweep`` function when handling ETH. This decision could be motivated by potential gas savings. However, using transfer introduces risks related to "gas limitations and future EVM changes", which can cause ETH transfers to fail under certain conditions.
## Vulnerability Detail
The``NFTPositionManager::sweep`` function uses Solidity’s native transfer function to send ETH to the admin when the specified token is address(0) (i.e., when ETH is being transferred). The transfer function forwards a fixed 2300 gas, which can cause the transaction to fail in the following scenarios:

- No Payable Fallback: If the recipient is a smart contract without a payable fallback function, the transfer will fail.
- High Gas Fallback: If the recipient contract implements a payable fallback function that consumes more than 2300 gas, the transfer will fail.
- Proxy Interaction: If the recipient contract needs less than 2300 gas but interacts with a proxy contract that raises the gas consumption above 2300, the transfer will fail.
- Future EVM Changes: EVM gas costs, like those impacted by EIP-1884, may increase in future upgrades, causing existing smart contracts relying on the 2300 gas assumption (as enforced by transfer) to break.
These factors make the transfer method less flexible and more prone to failure when interacting with certain contracts, particularly multisig wallets or contracts with complex logic in their fallback functions.
## Impact
The use of the transfer function with its fixed gas stipend of 2300 can lead to the following negative outcomes:

* Failed ETH Transfers: ETH withdrawals via the sweep function may fail when the recipient contract requires more gas than is provided by transfer. This could prevent legitimate users (particularly contracts and multisigs) from receiving their funds, introducing friction or blocking the admin from performing critical operations.
* Contract Breakage: In future EVM hard forks that alter gas costs, the transfer method may become incompatible with some previously working contract interactions, leading to potentially critical failures.
While the developer uses call for handling ETH in other functions, the inconsistency in the sweep function poses an unnecessary risk. Ensuring a unified ETH transfer mechanism across the entire contract would provide better robustness and future-proofing.


## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L134
solidity
Copy code
```javascript
function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
  if (token == address(0)) {
    uint256 bal = address(this).balance;
  @> payable(msg.sender).transfer(bal);
  } else {
    IERC20Upgradeable erc20 = IERC20Upgradeable(token);
    erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
  }
}
```
## Tool used

Manual Review

## Recommendation
To ensure consistency and avoid issues caused by the use of the deprecated transfer function, it is recommended to replace the transfer with a low-level call function for ETH transfers. Using call allows for specifying the gas limit dynamically and handling fallback failures more gracefully. Here's an updated version of the sweep function:

solidity
Copy code
```diff
function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
  if (token == address(0)) {
    uint256 bal = address(this).balance;
-    payable(msg.sender).transfer(bal);
+ (bool success, ) = payable(msg.sender).call{value: bal}(""); // Updated to use call
+  require(success, "ETH transfer failed");
  } else {
    IERC20Upgradeable erc20 = IERC20Upgradeable(token);
    erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
  }
}
```