Itchy Pewter Canary

Medium

# NFTPositionManager:sweep() is incompatible with multisig wallets

### Summary

`NFTPositionManager:sweep()` is incompatible with multisig wallets when sweeping ETH because it uses `transfer()` which will always fail on such wallets.

### Root Cause

In [NftPositionManager:sweep()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L134), when ETH is selected, the low-level primitive `transfer()` is used:

```js
function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
      payable(msg.sender).transfer(bal); // <@
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
      erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```

### Internal pre-conditions

_No response_

### External pre-conditions

1. owner must be a multisig wallet whose `receive()` function costs more than 2300 gas

### Attack Path

_No response_

### Impact

If the owner is a multisig wallet this operation will always fail since `transfer()` enforces a 2300 gas limit expenditure which isn´t enough for any multisig wallet's `receive()` to completly execute.

### PoC

_No response_

### Mitigation

Use `call()` instead of `transfer()`