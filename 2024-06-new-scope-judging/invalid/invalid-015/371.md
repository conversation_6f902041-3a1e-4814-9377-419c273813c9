<PERSON><PERSON>y <PERSON>jon Chinchilla

Medium

# Using `transfer()` for payable address can cause transactions to always fail

### Summary

The `transfer()` and `send()` functions forward a fixed amount of 2300 gas. Based on protocol team statement, the caller (`DEFAULT_ADMIN_ROLE`) will governance timelock contract. For this case, use of the `transfer()` function for an address will inevitably make the transaction fail when :

1. The caller is smart contract implements a payable fallback function that needs less than 2300 gas units but is called through proxy, raising the call's gas usage above 2300.
2. Additionally, using higher than 2300 gas might be mandatory for some multisig wallets.

### Root Cause

In [NFTPositionManager.sol:134](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L134) using `transfer()` for payable address

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The use of the `transfer()` function for an payable address will inevitably make the transaction fail

### PoC

```solidity
  function sweep(address token) external onlyRole(DEFAULT_ADMIN_ROLE) {
    if (token == address(0)) {
      uint256 bal = address(this).balance;
      payable(msg.sender).transfer(bal);
    } else {
      IERC20Upgradeable erc20 = IERC20Upgradeable(token);
      erc20.safeTransfer(msg.sender, erc20.balanceOf(address(this)));
    }
  }
```

### Mitigation

Use `call()` instead of `transfer()` for payable address