Deep Yellow Tiger

Medium

# Use of `payable(address).transfer` will cause stuck funds for the protocol

### Summary

The sweep function allows an admin to withdraw the contract's funds, both in Ether and ERC-20 tokens. It uses .transfer to handle Ether transfers, which can result in reverts due to the fixed gas stipend of 2300 gas units. 

### Root Cause

This is a potential risk the protocol might face transaction failures, particularly when they attempt to create logs or perform any action that requires more gas. Also some EVM instructions may change significantly during hard forks which may break already deployed contract systems that make fixed assumptions about gas costs. For example. EIP 1884 broke several existing smart contracts due to a cost increase of the SLOAD instruction.

Also a good mention, when using a multisig wallets, higher than 2300 gas might be mandatory.

Here is where the native `.transfer` function is used:
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManager.sol#L131-L139


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The use of the deprecated transfer() function for an address will inevitably make the transaction fail.

### PoC

_No response_

### Mitigation

The recommended approach is to replace .transfer with .call to provide more flexibility and reliability in Ether transfers.