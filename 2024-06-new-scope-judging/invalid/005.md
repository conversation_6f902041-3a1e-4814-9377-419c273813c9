Able Concrete Jellyfish

Medium

# Excessive Transfer Amount Vulnerability in ERC20 Operations

## Summary
The `executeWithdraw` function in the `SupplyLogic` library does not explicitly handle transfers exceeding the `uint96` limit, which can lead to unexpected behavior with certain ERC20 tokens that use `uint96` for balance and transfer operations like UNI or COMP. This oversight could result in failed transactions or unexpected reverts when handling large transfer amounts.
`IERC20(params.asset).safeTransfer(params.destination, params.amount);`

## Vulnerability Detail
1.	Setup:
-	Deploy a mock ERC20 token that uses `uint96` for balances.
-	Initialize the `SupplyLogic` contract with this token.
2.	Reproduction Steps:
-	Attempt to execute a withdrawal using the `executeWithdraw` function with an amount greater than `2^96`.
-	Observe that the transaction reverts due to the token's inability to handle the large amount.
```solidity
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import "forge-std/Test.sol";
import {SupplyLogic} from "../contracts/core/pool/logic/SupplyLogic.sol";
import {DataTypes} from "../contracts/core/pool/configuration/DataTypes.sol";
import {IERC20} from "@openzeppelin/contracts/interfaces/IERC20.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {PoolErrorsLib} from "../contracts/interfaces/errors/PoolErrorsLib.sol";

contract SupplyLogicTest is Test {
    using SafeERC20 for IERC20;

    DataTypes.ReserveData reserve;
    DataTypes.UserConfigurationMap userConfig;
    DataTypes.PositionBalance balance;
    DataTypes.ReserveSupplies totalSupplies;
    DataTypes.ExecuteSupplyParams supplyParams;
    DataTypes.ExecuteWithdrawParams withdrawParams;
    IERC20 token;

    mapping(address => DataTypes.ReserveData) reservesData;
    mapping(uint256 => address) reservesList;
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) balances;

    address user = address(0x123);
    uint256 largeAmount = uint256(type(uint96).max) + 1;

    function setUp() public {
        // Setup initial state
        token = IERC20(address(new MockERC20()));
        supplyParams = DataTypes.ExecuteSupplyParams({
            asset: address(token),
            pool: address(this),
            amount: largeAmount,
            reserveFactor: 0,
            position: bytes32(0),
            data: DataTypes.ExtraData({hookData: "", interestRateData: ""})
        });

        withdrawParams = DataTypes.ExecuteWithdrawParams({
            asset: address(token),
            pool: address(this),
            amount: largeAmount,
            reserveFactor: 0,
            position: bytes32(0),
            data: DataTypes.ExtraData({hookData: "", interestRateData: ""}),
            destination: user
        });

        // Initialize mappings
        reservesData[address(token)] = reserve;
        reservesList[0] = address(token);
        balances[address(token)][bytes32(0)] = balance;
    }

    function testExecuteWithdrawWithLargeAmount() public {
        vm.expectRevert("NOT_ENOUGH_AVAILABLE_USER_BALANCE");
        SupplyLogic.executeWithdraw(reservesData, reservesList, userConfig, balances, totalSupplies, withdrawParams);
    }
}

contract MockERC20 is IERC20 {
    function totalSupply() external pure override returns (uint256) {
        return 0;
    }

    function balanceOf(address /*account*/) external pure override returns (uint256) {
        return 0;
    }

    function transfer(address /*recipient*/, uint256 /*amount*/) external pure override returns (bool) {
        return false;
    }

    function allowance(address /*owner*/, address /*spender*/) external pure override returns (uint256) {
        return 0;
    }

    function approve(address /*spender*/, uint256 /*amount*/) external pure override returns (bool) {
        return false;
    }

    function transferFrom(address /*sender*/, address /*recipient*/, uint256 /*amount*/) external pure override returns (bool) {
        return false;
    }
}
```
```solidity
forge test --match-path test/SupplyLogicTest.t.sol -vvvv
[⠒] Compiling...
[⠔] Compiling 1 files with Solc 0.8.19
[⠒] Solc 0.8.19 finished in 2.32s
Compiler run successful!

Ran 1 test for test/SupplyLogicTest.t.sol:SupplyLogicTest
[PASS] testExecuteWithdrawWithLargeAmount() (gas: 67268)
Traces:
  [67268] SupplyLogicTest::testExecuteWithdrawWithLargeAmount()
    ├─ [0] VM::expectRevert(NOT_ENOUGH_AVAILABLE_USER_BALANCE)
    │   └─ ← [Return] 
    ├─ [42504] SupplyLogic::66f9f343(000000000000000000000000000000000000000000000000000000000000003d00000000000000000000000000000000
0000000000000000000000000000003e0000000000000000000000000000000000000000000000000000000000000026000000000000000000000000000000000000000000000000000000000000003f000000000000000000000000000000000000000000000000000000000000002a00000000000000000000000000000000000000000000000000000000000000c00000000000000000000000005615deb798bb3e4dfa0139dfa1b3d433cc23b72f00000000000000000000000000000000000000000000000000000000000001230000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000) [delegatecall]                                                            │   ├─ [5095] PositionBalanceConfiguration::01fff09e(820ce9cb7ecc8ce1b7be2061d488ce7bed832dad618a9167c2574a523d724b81000000000000
0000000000000000000000000000000000000000000000000000) [delegatecall]                                                                     │   │   └─ ← [Return] 0x0000000000000000000000000000000000000000000000000000000000000000
    │   └─ ← [Revert] revert: NOT_ENOUGH_AVAILABLE_USER_BALANCE
    └─ ← [Stop] 

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 2.84ms (744.90µs CPU time)

Ran 1 test suite in 889.63ms (2.84ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

## Impact
Users attempting to withdraw amounts exceeding the `uint96` limit will experience transaction failures.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L106-L156

## Tool used
- Manual Review
- Foundry

## Recommendation
Implement a check in the `executeWithdraw` function to ensure that `params.amount` does not exceed `uint96` before attempting the transfer.