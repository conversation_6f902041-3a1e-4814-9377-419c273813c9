Low Lemon Dove

High

# wrong calculation in function  executeWithdraw

### Summary

in **SupplyLogic** function **executeWithdraw**  if a user make params.amount  =   type(uint256).max line 121 will make the full amount balance  it will call getSupplyBalance  which will calculate the  the share with the lastSupplyLiquidtyIndexand with  the new one  and update params.amoun with  the collected amount  but when doing that  it is not update  **self.supplyShares** with the new one so in line 145  it will try to convert params.amount into share which will  might lead  for the function to revert because   **withdrawCollateral** will be called  and the calculated amount might be bigger than the   **self.supplyShares** which will revert  

### Root Cause

in SupplyLogic: 121 we need to udatete self.supplyShares with the new liquidity'
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L145 

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

unable to withdraw there own fund

### PoC

_No response_

### Mitigation

_No response_