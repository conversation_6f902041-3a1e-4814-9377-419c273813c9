Cheerful Blue Meerkat

Medium

# Timelock window will be set to a shorter time window due to incorrect and unsafe type casting done in PendingLib

### Summary

In `CuratedVault.sol`, inside the `submitTimelock(...)`, the new timelock value received is down casted to `uint184` instead of `uint192` which is what is accepted by the `PendingUint192` structure. This could result in truncation of value stored in the `PendingUint192` structure.




### Root Cause

In `CuratedVault.sol`, refer to the `submitTimelock(...)` function below, where in line no 112, the passed value for `newTimeLock` is down casted to `uint184` and passed to `update()` function.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L103-L115

`  pendingTimelock.update(...)` invokes `update(...)` on pendingLib as below.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/libraries/PendingLib.sol#L25-L29

On review of the `PendingUint192`, the value type defined is uint192.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/interfaces/vaults/ICuratedVaultBase.sol#L34-L39

Here, there is an unsafe typecast of numeric value from uint256 to uint184, while the intended supported value was of type uint192. Such type casting will cut off some values leading to unexpected behaviour.

Refer to the below code snippet where `PendingUint192` structure is designed to support Uint192.

```solidity
  struct PendingUint192 {
  /// @notice The pending value to set.
 ==>  uint192 value;
  /// @notice The timestamp at which the pending value becomes valid.
  uint64 validAt;
}
```
But, the pending library accepts only uint184 as value there by truncating the received value.

```solidity
library PendingLib {
  /// @dev Updates `pending`'s value to `newValue` and its corresponding `validAt` timestamp.
  /// @dev Assumes `timelock` <= `MAX_TIMELOCK`.
  ===>function update(PendingUint192 storage pending, uint184 newValue, uint256 timelock) internal {
    pending.value = newValue;
    // Safe "unchecked" cast because timelock <= MAX_TIMELOCK.
    pending.validAt = uint64(block.timestamp + timelock);
  }
```


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The Pending Time Window could be set to a totally unexpected value due to this unsafe typecasting of new time lock value. The behaviour for locking and unlocking by vault could be impacted as a result of this.

### PoC

_No response_

### Mitigation

The recommendation is to typecast to the supported data type in the `PendingUint192` structure. Also, use the safeCast library from openzeppelin while doing the data conversions.

Revise the below `PendingLib::update(...)` function to accept `uint192` instead of `uint184` to prevent the truncation of value.

```solidity
library PendingLib {
  /// @dev Updates `pending`'s value to `newValue` and its corresponding `validAt` timestamp.
  /// @dev Assumes `timelock` <= `MAX_TIMELOCK`.
-  function update(PendingUint192 storage pending, uint184 newValue, uint256 timelock) internal {
+ function update(PendingUint192 storage pending, uint192 newValue, uint256 timelock) internal {
    pending.value = newValue;
    // Safe "unchecked" cast because timelock <= MAX_TIMELOCK.
    pending.validAt = uint64(block.timestamp + timelock);
  }
```


https://github.com/OpenZeppelin/openzeppelin-contracts/blob/master/contracts/utils/math/SafeCast.sol