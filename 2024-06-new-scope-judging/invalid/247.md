Curly Pineapple Armadillo

High

# Users will be unable to withdraw their funds, due to implementation of `totalSupplies.underlyingBalance`

### Summary

At the end of `updateInterestRates` `totalSupplies.underlyingBalance` is either increased or decreased by the number of tokens a user has supplied or withdrawn. This is an issue as when interest accrues the amount of tokens that the user can withdraw will be more than the amount of tokens they initially supplied. As a result, `totalSupplies.underlyingBalance` will be less than the sum of all supply balances, causing users to be unable to withdraw their supplied tokens.

### Root Cause

- In [`updateInterestRates`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L176-L177) `totalSupplies.underlyingBalance` is increased by the amount initially supplied, and after a withdrawal will be decreased by the amount initially supplied + accrued interest.
- This causes an issue in accounting as some users will be able to withdraw all of their assets, including the interest, while others will be completely prevented from withdrawing any assets.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. First user supplies 10 ETH, thus 10 ETH is added to `totalSupplies.underlyingBalance`.
2. Interest accrues and the user can withdraw 11 ETH, `totalSupplies.underlyingBalance` remains 10 ETH.
3. Second user supplies 1 ETH, thus 1 ETH is added to `totalSupplies.underlyingBalance`.
4. First user withdraws the entire 11 ETH, and `totalSupplies.underlyingBalance` is set to 0.
5. Second user cannot withdraw anything.

### Impact

Users will be unable to withdraw their assets, causing a loss of funds.

### PoC

_No response_

### Mitigation

Currently, `totalSupplies.underlyingBalance` is only used in the periphery `UIHelper` contract, thus I recommend removing the increase/decrease of `totalSupplies.underlyingBalance` in `updateInterestRates` and using another method for querying the total underlying balance in `UIHelper`, perhaps through the `PoolGetters.totalAssets` view function. 