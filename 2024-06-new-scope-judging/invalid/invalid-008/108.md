Polite Spruce Duck

High

# Users can process faux mints/deposits in the vault



## Summary

Users can process faux mints/deposits in the vault due to the try/catch logic applied to skip some markets.

## Vulnerability Detail

Whenever minting/depositing in/to the vault there is a need to call the internal deposit to pass on the asset to the underlying Zerolend market: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L35-L42

```solidity
  function _deposit(address caller, address receiver, uint256 assets, uint256 shares) internal override {
    super._deposit(caller, receiver, assets, shares);

    _supplyPool(assets);

    // `lastTotalAssets + assets` may be a little off from `totalAssets()`.
    _updateLastTotalAssets(lastTotalAssets + assets);
  }
```

Problem however is that when supplying the assets to the pool, the query could revert and this would not bubble back up to the initial deposit attempt, this is because of the try/catch logic in the internal \_supplyPool: https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L115-L144

```solidity
  function _supplyPool(uint256 assets) internal {
    for (uint256 i; i < supplyQueue.length; ++i) {
      IPool pool = supplyQueue[i];

      uint256 supplyCap = config[pool].cap;
      if (supplyCap == 0) continue;

      pool.forceUpdateReserve(asset());

      uint256 supplyShares = pool.supplyShares(asset(), positionId);

      // `supplyAssets` needs to be rounded up for `toSupply` to be rounded down.
      (uint256 totalSupplyAssets, uint256 totalSupplyShares,,) = pool.marketBalances(asset());
      uint256 supplyAssets = supplyShares.toAssetsUp(totalSupplyAssets, totalSupplyShares);

      uint256 toSupply = UtilsLib.min(supplyCap.zeroFloorSub(supplyAssets), assets);

      if (toSupply > 0) {
        // Using try/catch to skip markets that revert.
        try pool.supplySimple(asset(), address(this), toSupply, 0) {
          assets -= toSupply;
        } catch {}
      }

      if (assets == 0) return;
    }

    if (assets != 0) revert CuratedErrorsLib.AllCapsReached();
  }

```

As the comment suggests the logic is applied in order to skip markets that revert, issue however is that this can then be used by a malicious actor to bypass whatever checks are in the validation logic.

Note, going down to the implementation of `supplySimple` we see that it's just like the classic `supply` implementation, see the two methods here: [Pool.sol#L67-L80](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L67-L80).

Now both end up validating the execution in the validationLogic.sol with https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L70-L89

```solidity
  function validateSupply(
    DataTypes.ReserveCache memory cache,
    DataTypes.ReserveData storage reserve,
    DataTypes.ExecuteSupplyParams memory params,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal view {
    require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    (bool isFrozen,) = cache.reserveConfiguration.getFlags();
    require(!isFrozen, PoolErrorsLib.RESERVE_FROZEN);

    uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();

    require(
      supplyCap == 0
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
  }
```

This then means that anyone can sidestep all the requirements here, since even if the checks revert it doesn't bubble up [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L115-L143).

## Impact

All requirements made in the `validateSupply` can be sidestepped, which makes this of high impact considering reserves are set in the frozen state as a measure to combat any black swan event, however users can still process withdrawals even when the reserves are frozen sidestepping this.

Additionally, just to reiterate, this would mean that if for whatever reason the underlying supply fails the deposit/mint in the vault still gets processed, that's to say another example would be that if the supply cap has been surpassed for a reserve the attempt at minting shares/depositing in the vault [should revert during the validation](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L87), however due to the [try/catch logic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L133-L136) even if the validation reverts, the function still continues its flow as normal and instead the vault still mints these tokens, albeit they would be wrongly backed per the SUPPLY_CAP limit existing.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L35-L42

- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L87
- https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L133-L136

## Tool used

Manual Review

## Recommendation

Do not use a try/catch logic when depositing assets to the underlying Zerolend markets.
