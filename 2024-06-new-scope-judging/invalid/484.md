Expert Arctic Mallard

High

# Lack of Validation in `executeMintToTreasury` Function

### Summary

In `PoolLogic.sol` at line [83](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83), the `executeMintToTreasury` function mints assets accrued through the reserve factor to the treasury without sufficient validation of accrued shares.

### Root Cause

In `PoolLogic.sol` at line [83](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83), the `executeMintToTreasury` function transfers funds to the treasury based on the `accruedToTreasuryShares` value without validating whether this value is accurate or has been correctly updated. This could lead to the unauthorized minting of assets or misallocation of funds.

### Internal pre-conditions

1. The contract must have already initialized and set up reserves.
2. There must be accrued treasury shares that need to be minted.

### External pre-conditions

1. The `executeMintToTreasury` function is called with valid parameters and the contract has accrued shares to mint.

### Attack Path

1. An attacker manipulates or exploits the conditions leading to incorrect computation of `accruedToTreasuryShares`.
2. The attacker calls the `executeMintToTreasury` function.
3. The function mints assets based on the manipulated `accruedToTreasuryShares` value and transfers them to the treasury.
4. This results in unauthorized minting or misallocation of funds.

### Impact

This vulnerability can lead to unauthorized or incorrect minting of assets, which can cause financial losses or distort the asset supply metrics. The protocol's integrity and accuracy are at risk, potentially leading to significant financial implications.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "forge-std/Test.sol";
import "../PoolLogic.sol";

contract TestMintToTreasury is Test {
    PoolLogic poolLogic;
    address treasury = address(0x123);
    address asset = address(0x456);

    function setUp() public {
        poolLogic = new PoolLogic();
    }

    function testMintToTreasury() public {
        // Assuming the following setup mimics the scenario where accrued shares can be manipulated.
        poolLogic.executeMintToTreasury(...); // Replace with actual parameters
    }
}
```

### Mitigation

Implement additional validation checks before executing the mint operation. Ensure that the `accruedToTreasuryShares` is accurate and properly updated. Consider implementing sanity checks to verify that the amount to be minted is within expected limits and conditions.

