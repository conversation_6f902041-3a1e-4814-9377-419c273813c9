Curly Pineapple Armadillo

Medium

# `NFTRewardsDistributor.supportsInterface` is implemented incorrectly

### Summary

`NFTRewardsDistributor.supportsInterface` is implemented incorrectly. Due to the current implementation `supportsInterface` will not return a successful result on `ERC721EnumerableUpgradeable.supportsInterface()`.

### Root Cause

- In [`NFTRewardsDistributor.supportsInterface`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L62) the only supported interfaces would be the ones that comply with `AccessControlEnumerableUpgradeable`'s implementation of `supportsInterface`. This happens because `super.supportsInterface(interfaceId)` is called which will call `supportsInterface` on the most derived contract which will be `AccessControlEnumerableUpgradeable`.
This is insufficient as `ERC721EnumerableUpgradeable`'s implementation of `supportsInterface` also needs to be supported.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Third-party protocol is intended to interact with `NFTPositionManager`
2. It checks whether the contract is ERC721 compliant but the current implementation will return false
3. Third-party protocol cannot interact with `NFTPositionManager`

### Impact

Protocols needing to interact with `NFTPositionManager` will be unable to do so.

### PoC

_No response_

### Mitigation

Change `supprtsInterface` to:
```solidity
function supportsInterface(bytes4 interfaceId)
    public
    view
    virtual
    override (ERC721EnumerableUpgradeable, IERC165Upgradeable, AccessControlEnumerableUpgradeable)
    returns (bool)
  {
    return ERC721EnumerableUpgradeable.supportsInterface(interfaceId) || AccessControlEnumerableUpgradeable.supportsInterface(interfaceId);
  }
```