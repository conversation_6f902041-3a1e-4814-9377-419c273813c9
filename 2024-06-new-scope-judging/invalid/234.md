Joyous Cedar Tortoise

High

# Malicious pool deployer can use a malicious oracle contract to steal funds of vault depositors

### Summary

The malicious deployer will pass in their own custom `oracle` that is currently harmless BUT is upgradeable. Later once vault depositors have deposited funds into the pool, the malicious deployer will upgrade the `oracle` contract to return an extremely inflated price (in [`getAssetPrice`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163)) for a collateral asset (1 wei WETH = 100000e6 USDC)

Now the malicious deployer can supply 100 wei of WETH to borrow all of the vault's deposited funds because of the overvalued collateral

### Root Cause

Allowing the pool deployer to specify any `oracle`

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. When the pool is initialised, the deployer passes in an upgradeable `oracle` contract
2. Vault users deposit 100000e6 USDC into the pool
3. the deployer upgrades their `oracle` contract to inflate the value of 100 wei of WETH to be much greater than the value of assets deposited by the vault
4. The deployer will supply 100 wei of WETH and borrow all of the vault's deposited funds because of the overvalued collateral

### Impact

All the funds deposited to the pool from the vault will be lost, so vault depositors lose their funds.

### PoC

_No response_

### Mitigation

Only allow protocol whitelisted oracle contracts to be set via the Pool configurator