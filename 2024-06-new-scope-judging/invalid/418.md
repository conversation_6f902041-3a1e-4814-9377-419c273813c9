Polite Garnet Swift

High

# `notifyRewardAmount` function allows Mid-Period reward rate alteration in NFTRewardsDistributor


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125

in the funciton  `notifyReward<PERSON>mount`

The bug relates to how rewards are added and distributed, which could lead to unintended consequences.


```solidity
function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
    rewardsToken.transferFrom(msg.sender, address(this), reward);

    bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }

    // Ensure the provided reward amount is not more than the balance in the contract.
    // This keeps the reward rate in the right range, preventing overflows due to
    // very high values of rewardRate in the earned and rewardsPerToken functions;
    // Reward + leftover must be less than 2^256 / 10^18 to avoid overflow.
    uint256 balance = rewardsToken.balanceOf(address(this));
    require(rewardRate[_assetHash] <= balance.div(rewardsDuration), 'Provided reward too high');

    lastUpdateTime[_assetHash] = block.timestamp;
    periodFinish[_assetHash] = block.timestamp.add(rewardsDuration);
    emit RewardAdded(_assetHash, reward);
}
```


1. The function allows adding rewards even if the previous reward period hasn't finished. This can lead to overwriting of existing rewards.

2. When adding rewards in the middle of an ongoing period, it calculates a new `rewardRate` by adding the new reward to the leftover rewards from the current period. However, this new rate is applied for a full `rewardsDuration`, effectively extending the reward period.

3. The check to ensure the reward rate isn't too high only considers the contract's current balance, not accounting for rewards that might be claimed during the reward period.


The impact on the project could be significant:

- Users might receive fewer rewards than expected, leading to dissatisfaction.
- The reward distribution might not align with the project's tokenomics plan.
- In extreme cases, the contract might promise more rewards than it can actually pay out, potentially leading to some users being unable to claim their rewards.


The bug in `notifyRewardAmount` allows for the modification of reward rates mid-period and doesn't properly account for existing rewards when new ones are added.


Triggering the bug:

This bug can be triggered by any address with the REWARDS_ALLOCATOR_ROLE calling `notifyRewardAmount` multiple times within a single reward period.

### PoC

```solidity
// Assume rewardsDuration is 7 days (604800 seconds)
// Initial setup
notifyRewardAmount(7000 ZERO, pool1, assetA, false); // 1 ZERO per second

// After 3 days
notifyRewardAmount(14000 ZERO, pool1, assetA, false);

// Result:
// - Instead of 7000 ZERO over 7 days, now it's 18000 ZERO over 10 days
// - Reward rate increased from 1 ZERO/second to 1.8 ZERO/second
// - Users staking in the latter part of the period get more rewards
```

The impact flows as follows:

1. Unpredictable Reward Rates: Users can't reliably predict their rewards, as rates can change unexpectedly.

2. Potential for Manipulation: A malicious REWARDS_ALLOCATOR could potentially use this to favor certain time periods, benefiting some users over others.

3. Extended Reward Periods: The actual distribution period becomes longer than the intended `rewardsDuration`.

4. Skewed Tokenomics: The project's planned token distribution schedule could be disrupted.


this bug introduce significant unpredictability and potential unfairness in the reward distribution system. This could indirectly impact the project's tokenomics, user engagement, and overall trust in the platform.


### Mitigation :

1. Only allow adding rewards when the previous period has finished.
2. Properly account for and distribute any leftover rewards from the previous period.
3. Implement a more robust check to ensure the contract always has enough balance to cover all potential reward claims throughout the entire reward period.

