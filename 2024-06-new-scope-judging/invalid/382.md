Hidden Gingham Troll

High

# Unauthorized User Can Manipulate Supply of Another User's Position

### Summary

A missing authorization check in the `NFTPositionManagerSetter::_supply` function will cause unauthorized manipulation of supply for position owners as malicious users will supply assets to positions they do not own.

### Root Cause

In [NFTPositionManagerSetters.sol#L44](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L44) the `_supply` function lacks an `_isAuthorizedForToken`, allowing unauthorized access to positions.

### Internal pre-conditions

1. The `_supply` function is called with a non-zero tokenId.
2. The caller is not the owner or authorized user of the tokenId.

### External pre-conditions

_No response_

### Attack Path

1. Malicious user identifies a target position (tokenId) they do not own.
2. Malicious user calls _supply with the target tokenId and supplies assets.
3. The position's balance and collateral are altered without the owner's consent.

### Impact

The position owner faces unauthorized changes to their collateral and rewards, which can lead to unexpected liquidations or financial losses. The attacker gains control over the position's financial state, potentially benefiting from these changes.

### PoC

_No response_

### Mitigation

Implement an `_isAuthorizedForToken` check in the `_supply` function to ensure only authorized users can supply to a position.
```diff
function _supply(AssetOperationParams memory params) internal nonReentrant {
    if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
    if (params.tokenId == 0) {
      if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();
      params.tokenId = _nextId - 1;
    }
+  _isAuthorizedForToken(params.tokenId);
    IPool pool = IPool(_positions[params.tokenId].pool);

    IERC20(params.asset).forceApprove(address(pool), params.amount);
    pool.supply(params.asset, address(this), params.amount, params.tokenId, params.data);

    // update incentives
    uint256 balance = pool.getBalance(params.asset, address(this), params.tokenId);
    _handleSupplies(address(pool), params.asset, params.tokenId, balance);

    emit NFTEventsLib.Supply(params.asset, params.tokenId, params.amount);
  }
```