Expert Arctic Mallard

High

# The `updateState` function will enable reentrancy attacks for the ReserveLogic contract.

### Summary

In `ReserveLogic.sol` at line [87](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87), the `updateState` function performs state updates and accruals without a proper reentrancy guard.

### Root Cause

In `ReserveLogic.sol` at line [87](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87), the `updateState` function updates the reserve's state and performs treasury accruals without any protection against reentrancy attacks. This could allow an attacker to repeatedly call the function and manipulate reserve states or exploit vulnerabilities.

### Internal pre-conditions

1. The contract must be called by a user or an external contract.
2. There should be an ongoing reserve update or state change triggered.

### External pre-conditions

1. The contract must have a balance or state that could be manipulated during the reentrancy.

### Attack Path

1. An attacker calls the `updateState` function, initiating reserve state changes and treasury accruals.
2. During the execution of `updateState`, the attacker repeatedly re-enters the function before the initial call is completed.
3. The repeated calls lead to inconsistent reserve state or improper treasury accruals.

### Impact

The reserve state could be manipulated or funds could be misappropriated due to the lack of a reentrancy guard. The protocol suffers potential financial loss or corruption of reserve data.

### PoC

```solidity
// SPDX-License-Identifier: GPL-3.0-or-later
pragma solidity >=0.8.0;

import "forge-std/Test.sol";
import "../ReserveLogic.sol";

contract TestReentrancy is Test {
    ReserveLogic reserveLogic;

    function setUp() public {
        reserveLogic = new ReserveLogic();
    }

    function testReentrancy() public {
        reserveLogic.updateState(...); // Trigger reentrancy
    }
}
```

### Mitigation

Mitigation: Implement a reentrancy guard in the `updateState` function to prevent reentrancy attacks. You can use OpenZeppelin’s `ReentrancyGuard` or a custom mutex mechanism.