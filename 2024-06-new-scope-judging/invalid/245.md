Fit Bone Eel

Medium

# Using deprecated _setupRole AccessControl function

### Summary

In the `__CuratedVaultRoles_init` function, the `_setupRole` function is being used to initialize the
DEFAULT_ADMIN_ROLE , CURATOR_ROLE , GUARDIAN_ROLE and ALLOCATOR_ROLE roles, which is
[deprecated](https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable/blob/release-v4.6/contracts/access/AccessControlUpgradeable.sol#L201) in the version of openzeppelin-contracts-upgradeable in use. Consider using the
`_grantRole` [function](https://github.com/OpenZeppelin/openzeppelin-contracts-upgradeable/blob/release-v4.6/contracts/access/AccessControlUpgradeable.sol#L223) instead.
```solidity

function __CuratedVaultRoles_init(
    address[] memory _admins,
    address[] memory _curators,
    address[] memory _guardians,
    address[] memory _allocators
  ) internal {
    __AccessControlEnumerable_init();

    for (uint256 i = 0; i < _admins.length; i++) {
      _setupRole(DEFAULT_ADMIN_ROLE, _admins[i]);
    }

    for (uint256 i = 0; i < _curators.length; i++) {
      _setupRole(CURATOR_ROLE, _curators[i]);
    }

    for (uint256 i = 0; i < _guardians.length; i++) {
      _setupRole(GUARDIAN_ROLE, _guardians[i]);
    }

    for (uint256 i = 0; i < _allocators.length; i++) {
      _setupRole(ALLOCATOR_ROLE, _allocators[i]);
    }
  }
```

### Root Cause

The `__CuratedVaultRoles_init()` function uses the deprecated `_setupRole()` function.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultRoles.sol#L21-L44

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

Unexpected behavior may occur during initialization(`__CuratedVaultRoles_init`).

### Impact

Using deprecated functions may eventually produce an unwanted behaviour, for example, if OpenZeppelin decides to remove or update the function.

### PoC

_No response_

### Mitigation

It is recommended to use the _grantRole function instead.