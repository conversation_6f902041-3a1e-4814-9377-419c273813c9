Atomic Currant Mule

Medium

# unlimited number of  pool reserves may cause DOS in certain circumstances

### Summary

The pool initialize reserves in initialize function in Pool.sol (https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L37-L55). The reserve id is assiged as the auto increase state variable _reserveCount. However, there is no max limit on how many reserves a pool can possess. If the reserve number if significant, there could be potential DOS during iterating the reserves process.

### Root Cause

The pool initialized reserves in https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L37-L55, where there is no max limit on how many reserve a pool can init. This could cause some problems when there are too many reserves added to the pool. For example, forceUpdateReserves() function may run out of gas when there are too many reserve attached to the pool.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

_No response_

### PoC

_No response_

### Mitigation

Consider add a max number limit on how many reserves a pool can attach.