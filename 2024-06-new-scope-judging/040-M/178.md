Rural Eggshell Sheep

High

# user who deposit using pool wont be able  receive any rewards .

## Summary
Rewards are distributed from NFTRewardsDistributor when user Supply assets ,  using the Pool to Supply assets   wont be able to get any rewards.
## Vulnerability Detail

In `NFTPositionManager:supply`, supply assets to the pool from NFTPositionManager and balance are updated in `NFTPositionManagerSetters:_supply` , then the Rewards are distributed according to the `_balances`.

The Rewards are not calculated in Pool , User who supply using pool wont be able to receive any rewards as the balances are tracked in NFTPositionManager.

```solidity
  // NFTPositionManager:supply
  function supply(AssetOperationParams memory params) external {
    if (params.asset == address(0)) revert NFTErrorsLib.ZeroAddressNotAllowed();
    IERC20Upgradeable(params.asset).safeTransferFrom(msg.sender, address(this), params.amount);
    _supply(params);
  }

  // NFTPositionManagerSetters:_supply
  function _supply(AssetOperationParams memory params) internal nonReentrant {
    //snip

    _handleSupplies(address(pool), params.asset, params.tokenId, balance);

    //snip
  }

  // NFTRewardsDistributor:_handleSupplies
  function _handleSupplies(address pool, address asset, uint256 tokenId, uint256 balance) internal {
    bytes32 _assetHash = assetHash(pool, asset, false);
    uint256 _currentBalance = _balances[tokenId][_assetHash];
    _updateReward(tokenId, _assetHash);
    _balances[tokenId][_assetHash] = balance;
  
    //which dont seem current but something is wrong here 
    _totalSupply[_assetHash] = _totalSupply[_assetHash] - _currentBalance + balance;

  }
  ```
## Impact
loss of rewards for user who supply from pool.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L165-L183
## Tool used

Manual Review

## Recommendation
1. Only allow user to supply from NFTPositionManager.
 Or 
2. Track balance of user who deposit from the  Pool to be able to receive rewards.