Decent Walnut Squirrel

High

# The protocol checks wrongly for health factor when borrow.

## Summary
The protocol does not use newly borrowing amount in calculation of health factor.   So `health factor` after borrow can be lower than `HEALTH_FACTOR_LIQUIDATION_THRESHOLD`.

## Vulnerability Detail
`BorrowLogic.sol#executeBorrow()` function which is called when borrow is as follows.
```solidity
  function executeBorrow(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteBorrowParams memory params
  ) public returns (DataTypes.SharesType memory borrowed) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);

    reserve.updateState(params.reserveFactor, cache);

@>  ValidationLogic.validateBorrow(
      _balances,
      reservesData,
      reservesList,
      DataTypes.ValidateBorrowParams({
        cache: cache,
        userConfig: userConfig,
        asset: params.asset,
        position: params.position,
        amount: params.amount,
        reservesCount: params.reservesCount,
        pool: params.pool
      })
    );

    ...
  }
```
Here, `ValiationLogic.sol#validateBorrow()` function is as follows.
```solidity
  function validateBorrow(
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.ValidateBorrowParams memory params
  ) internal view {
    require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    ValidateBorrowLocalVars memory vars;

    (vars.isFrozen, vars.borrowingEnabled) = params.cache.reserveConfiguration.getFlags();

    require(!vars.isFrozen, PoolErrorsLib.RESERVE_FROZEN);
    require(vars.borrowingEnabled, PoolErrorsLib.BORROWING_NOT_ENABLED);

    vars.reserveDecimals = params.cache.reserveConfiguration.getDecimals();
    vars.borrowCap = params.cache.reserveConfiguration.getBorrowCap();
    unchecked {
      vars.assetUnit = 10 ** vars.reserveDecimals;
    }

    if (vars.borrowCap != 0) {
      vars.totalSupplyVariableDebt = params.cache.currDebtShares.rayMul(params.cache.nextBorrowIndex);

      vars.totalDebt = vars.totalSupplyVariableDebt + params.amount;

      unchecked {
        require(vars.totalDebt <= vars.borrowCap * vars.assetUnit, PoolErrorsLib.BORROW_CAP_EXCEEDED);
      }
    }

@>  (vars.userCollateralInBaseCurrency, vars.userDebtInBaseCurrency, vars.currentLtv,, vars.healthFactor,) = GenericLogic
      .calculateUserAccountData(
      _balances,
      reservesData,
      reservesList,
      DataTypes.CalculateUserAccountDataParams({userConfig: params.userConfig, position: params.position, pool: params.pool})
    );

    require(vars.userCollateralInBaseCurrency != 0, PoolErrorsLib.COLLATERAL_BALANCE_IS_ZERO);
    require(vars.currentLtv != 0, PoolErrorsLib.LTV_VALIDATION_FAILED);

@>  require(vars.healthFactor > HEALTH_FACTOR_LIQUIDATION_THRESHOLD, PoolErrorsLib.HEALTH_FACTOR_LOWER_THAN_LIQUIDATION_THRESHOLD);

    vars.amountInBaseCurrency = IPool(params.pool).getAssetPrice(params.asset) * params.amount;
    unchecked {
      vars.amountInBaseCurrency /= vars.assetUnit;
    }

    //add the current already borrowed amount to the amount requested to calculate the total collateral needed.
    vars.collateralNeededInBaseCurrency = (vars.userDebtInBaseCurrency + vars.amountInBaseCurrency).percentDiv(vars.currentLtv); //LTV is
    // calculated in percentage

    require(vars.collateralNeededInBaseCurrency <= vars.userCollateralInBaseCurrency, PoolErrorsLib.COLLATERAL_CANNOT_COVER_NEW_BORROW);
  }
```
As we can see above, it does not use `params.amount` in calculation of health factor.   
So `health factor` after borrow can be lower than `HEALTH_FACTOR_LIQUIDATION_THRESHOLD`.

## Impact
The ZeroLend protocol allows health factor after borrowing to be lower than `HEALTH_FACTOR_LIQUIDATION_THRESHOLD`.   So the user can be liquidated unexpectedly or can borrow more amount than normal.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L64

## Tool used

Manual Review

## Recommendation
1. `BorrowLogic.sol#executeBorrow()` function has to be modified as follows.
```solidity
  function executeBorrow(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.UserConfigurationMap storage userConfig,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ExecuteBorrowParams memory params
  ) public returns (DataTypes.SharesType memory borrowed) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory cache = reserve.cache(totalSupplies);

    reserve.updateState(params.reserveFactor, cache);

-   ValidationLogic.validateBorrow(
-     _balances,
-     reservesData,
-     reservesList,
-     DataTypes.ValidateBorrowParams({
-       cache: cache,
-       userConfig: userConfig,
-       asset: params.asset,
-       position: params.position,
-       amount: params.amount,
-       reservesCount: params.reservesCount,
-       pool: params.pool
-     })
-   );

    // mint debt tokens
    DataTypes.PositionBalance storage b = _balances[params.asset][params.position];
    bool isFirstBorrowing;
    (isFirstBorrowing, borrowed.shares) = b.borrowDebt(totalSupplies, params.amount, cache.nextBorrowIndex);
    cache.nextDebtShares = totalSupplies.debtShares;

+   ValidationLogic.validateBorrow(
+     _balances,
+     reservesData,
+     reservesList,
+     DataTypes.ValidateBorrowParams({
+       cache: cache,
+       userConfig: userConfig,
+       asset: params.asset,
+       position: params.position,
+       amount: params.amount,
+       reservesCount: params.reservesCount,
+       pool: params.pool
+     })
+   );

    // if first borrowing, flag that
    if (isFirstBorrowing) userConfig.setBorrowing(reserve.id, true);

    reserve.updateInterestRates(
      totalSupplies,
      cache,
      params.asset,
      IPool(params.pool).getReserveFactor(),
      0,
      params.amount,
      params.position,
      params.data.interestRateData
    );

    IERC20(params.asset).safeTransfer(params.destination, params.amount);

    emit PoolEventsLib.Borrow(params.asset, params.user, params.position, params.amount, reserve.borrowRate);

    borrowed.assets = params.amount;
  }
```
2. `ValiationLogic.sol#validateBorrow()` function has to be modified as follows.
```solidity
  function validateBorrow(
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.ValidateBorrowParams memory params
  ) internal view {
    require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    ValidateBorrowLocalVars memory vars;

    (vars.isFrozen, vars.borrowingEnabled) = params.cache.reserveConfiguration.getFlags();

    require(!vars.isFrozen, PoolErrorsLib.RESERVE_FROZEN);
    require(vars.borrowingEnabled, PoolErrorsLib.BORROWING_NOT_ENABLED);

    vars.reserveDecimals = params.cache.reserveConfiguration.getDecimals();
    vars.borrowCap = params.cache.reserveConfiguration.getBorrowCap();
    unchecked {
      vars.assetUnit = 10 ** vars.reserveDecimals;
    }

    if (vars.borrowCap != 0) {
      vars.totalSupplyVariableDebt = params.cache.currDebtShares.rayMul(params.cache.nextBorrowIndex);

-     vars.totalDebt = vars.totalSupplyVariableDebt + params.amount;
+     vars.totalDebt = vars.totalSupplyVariableDebt;

      unchecked {
        require(vars.totalDebt <= vars.borrowCap * vars.assetUnit, PoolErrorsLib.BORROW_CAP_EXCEEDED);
      }
    }

    ...
  }
```