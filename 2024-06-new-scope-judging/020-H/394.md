Curly Pineapple Armadillo

High

# Liquidations can leave positions at an unhealthier state, allowing to liquidate 100% of a position when only 50% should be allowed

### Summary

Liquidations are limited to either 100% (`MAX_LIQUIDATION_CLOSE_FACTOR = 1e4`) or 50% (`DEFAULT_LIQUIDATION_CLOSE_FACTOR = 0.5e4`) of a liquidated borrower's collateral. The limit is determined by the liquidated user's health factor: if it is less than 95% then full liquidation is possible and if it is more than 95% only 50% of their collateral should be at risk of being liquidated. The issue is that as the assets that comprise a reserve's collateral have different LTVs and liquidation thresholds it is possible that a position lowers its health factor after it has been liquidated. For example, if a position had a health factor of 97%, 50% of its collateral can be liquidated. However, if collateral assets with the highest liquidation thresholds are targeted, the health factor can drop below 95%, allowing to liquidate the entire collateral.

To put it simply, as this is the formula for calculating health factor:
`(totalCollateral * avgLiquidationThreshold%) / totalDebt`
the higher the average liquidation threshold, the higher the health factor, so liquidating assets with the highest liquidation threshold, and leaving those with a lower liquidation threshold will lower a position's health factor.

### Root Cause

- In [`executeLiquidationCall`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94) there is no validation whether a liquidation leaves a position at a worse state than before it was liquidated.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. A position has borrowed 1000 USDC
2. Its collateral assets are comprised of: 
  - 600 DAI (600$), with a 100% liquidation threshold
  - 1 ETH, equal to 400$, with a 90% liquidation threshold
  - Average liquidation threshold = (600 * 100 + 400 * 90) / 1000 = 96%
3. The health factor is: 1000$ * 96% / 1000$ = 0.96
4. 0.96 < 1 so the position is liquidatable but only 50% of collateral can be liquidated, for simplicity we will ignore fees and liquidation bonuses
5. A liquidator liquidates the maximum 500 DAI of collateral, paying 500 USDC in debt
6. Average liquidation threshold = (100 * 100 + 400 * 90) / 500 = 92%
7. The health factor is: 500$ * 92$ / 500 = 0.92
8. The health factor has lowered and is now less than 0.95, thus the position can be fully liquidated

### Impact

Liquidators can liquidate 100% of positions which should only be liquidatable by 50%. Also as the health factor of positions can be lowered by liquidations, the chances of positions becoming insolvent and bad debt accruing increases.

### PoC

_No response_

### Mitigation

Consider validating that the health factor has not lowered after a partial liquidation occurs, thus encouraging liquidators to liquidate the collateral with lower liquidation thresholds first.