Clever Ebony Halibut

Medium

# Inconsistent Handling of Price Feed Decimals Leads to Incorrect Price Calculations

## Summary

The `getAssetPrice` function in `PoolGetters.sol` assumes that all price feeds report prices using the same number of decimals. However, this assumption is incorrect as different price feeds can use varying decimal precisions. For example, the ETH/USD price feed uses 8 decimals, while the AMPL/USD price feed uses 18 decimals. This inconsistency can lead to incorrect price calculations .

## Vulnerability Detail

The vulnerability lies in the `getAssetPrice` function in `PoolGetters.sol`:

```js
function getAssetPrice(address reserve) public view override returns (uint256) {
(, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(\_reserves[reserve].oracle).latestRoundData();
require(price > 0, 'Invalid price');
require(block.timestamp <= updatedAt + 1800, 'Stale Price');
return uint256(price);
}
```

This function retrieves the latest price from the chainlink price feed oracle associated with the given reserve. However, it directly returns the price as a `uint256` without considering the decimal precision used by the price feed. which used as it in the function `calculateUserAccountData` to calculate the value of debt/collateral of a user.

```js
  function calculateUserAccountData(/*params*/) internal view returns (uint256, uint256, uint256, uint256, uint256, bool) {

      vars.assetPrice = IPool(params.pool).getAssetPrice(vars.currentReserveAddress);
  }
```

The issue arises when different chainlink price feeds use varying decimal precisions. For instance:

- The [`ETH/USD`](https://etherscan.io/address/******************************************) price feed reports prices using 8 decimals
- The [`AMPL/USD`](https://etherscan.io/address/******************************************) price feed reports prices using 18 decimals.

By directly returning the price without adjusting for the decimal precision, the `getAssetPrice` function will wrongly value the assets . This lead to incorrect borrowing and collateral valuations, allowing users to borrow more than intended or undervaluing collateral.

- this is particularly an issue within the zerolend-one protocol since it aims to support a wide range of assets.

## Impact

The inconsistent handling of price feed decimals can lead to incorrect price calculations, which can have the following impacts:

1. **Inaccurate borrowing and collateral valuations**: If the decimal precision is not accounted for, the calculated borrowing and collateral values may be significantly off, allowing users to borrow way more than intended or undervaluing collateral.
2. **Potential market manipulation**: Attackers can exploit the inconsistencies in price calculations to drain pools .

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158-L163

## Tool used

Manual Review

## Recommendation

To address this issue, it is recommended to modify the `getAssetPrice` function to account for the decimal precision of the price feed.and use a unified decimals for value Here's an updated version of the function:

```js
function getAssetPrice(address reserve) public view override returns (uint256) {
(, int256 price,, uint256 updatedAt,) = IAggregatorV3Interface(\_reserves[reserve].oracle).latestRoundData();
require(price > 0, 'Invalid price');
require(block.timestamp <= updatedAt + 1800, 'Stale Price');

    uint8 decimals = IAggregatorV3Interface(_reserves[reserve].oracle).decimals();
    if (decimals <= 18) return uint256(price) * 10**(18 - decimals);
    if (decimals > 18) return uint256(price) / 10**(decimals - 18);

}
```
