Mythical Alabaster Snake

High

# Several functions in the contracts falsely assume the number of decimals being returned by the oracle

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L207-L219

the above code effectively returns assetBalance * price / assetUnit 
if all three paramaters were of the same decimals, there would be no issue, however chainlink which is the oracle used and it always returns 8 decimals for non eth pairs and 18 decimals for eth pairs(eth as the base currency)
poc 
assume usdc is the base currency for the pool 
and dai is an asset in the pool then for 1000 dai ie 1e18 dai (assume usdc and dai currently have the same price)
the value would be 1000e18 * 1e8 / 1e18 = 1e8  
however the value should be 1e18 

and the other case that "seems to solve the problem" ie using eth pairs 
non 18 decimals tokens cannot be used in the pool 
eg for usdc 
the price returned would be 4239382e+14 due to being returned in 18 decimals 
1000e6 * 4.239382e+14 / 1e6 = 1e17 seriously inflating the value of the currency 

this is just one example 

others include 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L184-L198

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L168-L171

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L138-L148 
and the underlying 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L373

recommendation 
the decimals returned by the oracle should be configured to match the assets decimals from the original 