Big Admiral Dove

Medium

# All oracle aggregators of pool reserves should have the same decimals

## Summary

Unequal price feed decimals of reserves oracle aggregators may cause incorrect calculation of total collateral, total debt and health factor.

## Vulnerability Detail

Chainlink aggregators generally supports two kinds of price feed decimals, 18 for ETH pair, 8 for non-ETH pair. ([Reference](https://ethereum.stackexchange.com/questions/92508/do-all-chainlink-feeds-return-prices-with-8-decimals-of-precision))

To evaluate various kinds of reserve assets and healthy status of a position, the `GenericLogic::calculateUserAccountData()` function converts collateral and debt assets into base currency by using the `_getPositionBalanceInBaseCurrency()` and `_getUserDebtInBaseCurrency()` functions.

```solidity
  function calculateUserAccountData(
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    DataTypes.CalculateUserAccountDataParams memory params
  ) internal view returns (uint256, uint256, uint256, uint256, uint256, bool) {
    if (params.userConfig.isEmpty()) {
      return (0, 0, 0, 0, type(uint256).max, false);
    }

    CalculateUserAccountDataVars memory vars;
    uint256 reservesCount = IPool(params.pool).getReservesCount();
    while (vars.i < reservesCount) {
      ... ...

      if (vars.liquidationThreshold != 0 && params.userConfig.isUsingAsCollateral(vars.i)) {
        vars.PositionBalanceInBaseCurrency = _getPositionBalanceInBaseCurrency(
          _balances[vars.currentReserveAddress][params.position], currentReserve, vars.assetPrice, vars.assetUnit
        );

        ... ...
      }

      if (params.userConfig.isBorrowing(vars.i)) {
        vars.totalDebtInBaseCurrency += _getUserDebtInBaseCurrency(
          _balances[vars.currentReserveAddress][params.position], currentReserve, vars.assetPrice, vars.assetUnit
        );
      }

      unchecked {
        ++vars.i;
      }
    }

    ... ...
  }
```

By the way, `_getPositionBalanceInBaseCurrency()` and `_getUserDebtInBaseCurrency()` functions return balances in price decimals.

```solidity
  function _getPositionBalanceInBaseCurrency(
    DataTypes.PositionBalance storage _balance,
    DataTypes.ReserveData storage reserve,
    uint256 assetPrice,
    uint256 assetUnit
  ) private view returns (uint256) {
    uint256 normalizedIncome = reserve.getNormalizedIncome(); // {decRAY}
    uint256 balance = (_balance.supplyShares.rayMul(normalizedIncome)) * assetPrice;  // {decAsset} + {decPrice}
    unchecked {
      return balance / assetUnit; // {decAsset} + {decPrice} - {decAsset} = {decPrice}
    }
  }

  function _getUserDebtInBaseCurrency(
    DataTypes.PositionBalance storage balance,
    DataTypes.ReserveData storage reserve,
    uint256 assetPrice,
    uint256 assetUnit
  ) private view returns (uint256) {
    // fetching variable debt
    uint256 userTotalDebt = balance.debtShares; // {decAsset}
    if (userTotalDebt != 0) userTotalDebt = userTotalDebt.rayMul(reserve.getNormalizedDebt()); // {decAsset}
    userTotalDebt = assetPrice * userTotalDebt;  // {decAsset} + {decPrice}
    unchecked {
      return userTotalDebt / assetUnit; // {decAsset} + {decPrice} - {decAsset} = {decPrice}
    }
  }
```

So if aggregators of pool reserves provides various decimals, total debt and total collateral will be sumed up with different decimals and health factor will also be affacted by this.

## Impact

This vulnerability will affect the entire pool workflow.

## Code Snippet

[core/pool/Pool.sol#L37-L59](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L37-L59)

## Tool used

Manual Review

## Recommendation

Should validate uniqueness of price feed decimals when initializing a pool.
