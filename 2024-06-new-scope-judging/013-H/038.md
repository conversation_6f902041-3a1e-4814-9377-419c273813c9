Acrobatic Rainbow Grasshopper

High

# Not including Chainlink decimals upon calculations will lead to accounting issues

## Summary
Not including Chainlink decimals upon calculations will lead to accounting issues
## Vulnerability Detail
Upon calculating prices in the base currency, we use `GenericLogic::_getUserDebtInBaseCurrency()` or `GenericLogic::_getPositionBalanceInBaseCurrency()`:
```solidity
function _getUserDebtInBaseCurrency(
    DataTypes.PositionBalance storage balance,
    DataTypes.ReserveData storage reserve,
    uint256 assetPrice,
    uint256 assetUnit
  ) private view returns (uint256) {
    // fetching variable debt
    uint256 userTotalDebt = balance.debtShares;
    if (userTotalDebt != 0) userTotalDebt = userTotalDebt.rayMul(reserve.getNormalizedDebt());
    userTotalDebt = assetPrice * userTotalDebt;

    unchecked {
      return userTotalDebt / assetUnit;
    }
  }
```
The issue is that the `assetPrice` is provided as it is after being fetched from Chainlink. Chainlink feeds return prices in different decimals (most commonly 8 and 18) and there is no clear pattern as to how many decimals will a feed return:
>There are also price feeds such as [AMPL/USD](https://etherscan.io/address/******************************************#readContract#F3) that report using 18 decimals which breaks the general rule that USD price feeds report in 8 decimals. (https://medium.com/cyfrin/chainlink-oracle-defi-attacks-93b6cb6541bf)

Thus, for some assets, we will have a much larger or smaller value depending on the amount of decimals a Chainlink feed returns which will be used for important calculations such as the health factor of a user. A user might be unfairly liquidated or unfairly kept with a healthy position due to that. Note that the `assetUnit` input in the function are the decimals of the asset, not the decimals which Chainlink returns.
## Impact
Not including Chainlink decimals upon calculations will lead to accounting issues
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L184-L219
## Tool used

Manual Review

## Recommendation
Fetch the Chainlink decimals as well and include them in the calculation so that you always return the same amount of decimals