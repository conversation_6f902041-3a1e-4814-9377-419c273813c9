Careful Fleece Pike

Medium

# Different oracle's decimals will cause wrong calculation in `GenericLogic#calculateUserAccountData`

### Summary

Different oracle's decimals will cause wrong calculation in `GenericLogic#calculateUserAccountData`.

### Root Cause

In `GenericLogic`, the returned values of `_getUserDebtInBaseCurrency` and `_getPositionBalanceInBaseCurrency` are in oracle's decimals

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L184-L198

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L207-L219

then these values are added to together in `calculateUserAccountData`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L109-L111

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L125-L127

### Internal pre-conditions

The pool uses oracles with different decimals.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

- `totalCollateralInBaseCurrency`, `totalDebtInBaseCurrency` are sums of unnormalized values
- `healthFactor` will be wrong, which leads to the liquidation will happen sooner or later than expected

### PoC

Change `oracleB`'s `_initialAnswer` in `CorePoolTests.sol`

```diff
abstract contract CorePoolTests is Test {
  ...
  function _setUpCorePool() internal {
    ...
-   oracleB = new MockV3Aggregator(18, 2 * 1e8);
+   oracleB = new MockV3Aggregator(18, 2 * 1e18);
    ...
  }
}
```

Run command: `forge test --match-path test/PoC/PoC.t.sol -vv`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {PoolSetup} from 'test/forge/core/pool/PoolSetup.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is PoolSetup {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  function setUp() public {
    _setUpPool();
  }

  function testPoC() external {
    uint256 mintAmount = 10 ether;
    uint256 borrowAmount = 1 ether;

    _mintAndApprove(alice, tokenA, mintAmount, address(pool));
    _mintAndApprove(alice, tokenB, mintAmount, address(pool));

    _mintAndApprove(bob, tokenA, borrowAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenA), bob, borrowAmount, 0);

    vm.startPrank(alice);
    pool.supplySimple(address(tokenB), alice, mintAmount, 0);
    pool.borrowSimple(address(tokenA), alice, borrowAmount, 0);
    vm.stopPrank();

    (uint256 totalCollateralInBaseCurrency, uint256 totalDebtInBaseCurrency,,,, uint256 healthFactor) = pool.getUserAccountData(alice, 0);

    console.log("totalCollateralInBaseCurrency: %e", totalCollateralInBaseCurrency);
    console.log("totalDebtInBaseCurrency: %e", totalDebtInBaseCurrency);
    console.log("healthFactor: %e", healthFactor);
  }
}
```

Logs:
```bash
  totalCollateralInBaseCurrency: 2e19
  totalDebtInBaseCurrency: 1e8
  healthFactor: 1.6e29
```



### Mitigation

Normalize the returned value of `_getUserDebtInBaseCurrency` and `_getPositionBalanceInBaseCurrency` to `wad`