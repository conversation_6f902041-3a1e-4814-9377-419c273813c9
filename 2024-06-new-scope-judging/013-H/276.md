Joyous Cedar Tortoise

High

# getAssetPrice does not account for the decimals returned by chainlink, an attacker can use this to steal supplied funds

### Summary

Different chainlink price feeds can return values with different decimals, even though the base currency is the same. For example on Linea: FOXY/USD (18 dec) and ETH/USDC (8 dec). All price feed data can be found [here](https://docs.chain.link/data-feeds/price-feeds/addresses?network=linea&page=1#linea-mainnet).

The issue is that the `getAssetPrice` [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolGetters.sol#L158) assumes that the returned amount is denominated in the same number of decimals for all tokens.

### Root Cause

`getAssetPrice` assumes that the returned amount from chainlink always has the same decimals, this is not true it may be 8 or 18

### Internal pre-conditions

Pool uses at least one token whose oracle returns a different number of decimals

### External pre-conditions

_No response_

### Attack Path

A pool is created with reserves FOXY and DAI

Assume 1 FOXY = 1 DAI = 1 USD

1. Honest user supplies 100,000e18 DAI (worth 100,000 USD)
2. Attacker supplies only 1e18 FOXY (worth 1 USD)
3. Attacker borrows more than 100,000e18 USDC because the user’s supplied collateral was overvalued due to the incorrect `getAssetPrice` function, which returns FOXY price in 18 decimals.

### Impact

Attacker can use overinflated collateral to steal all supplied assets

### PoC

_No response_

### Mitigation

In the `getAssetPrice` function check how many decimals the returned value has and then normalize them to the same value