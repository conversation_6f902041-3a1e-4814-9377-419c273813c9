Rapid Onyx Meerkat

Medium

# chainlink oracles that have different decimals will return the wrong prices

### Summary

During the liquidation process, the protocol converts the `debtToCover` amount into the equivalent `baseCollateral` amount using two prices fetched from the Chainlink data feed. However, the protocol incorrectly assumes that both assets have the same decimal, which leads to price errors when the assets have different decimals.

### Root Cause

[LiquidationLogic.sol::executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L139-L148) Fetch two assets price from chainlink data feed.
```solidity
    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```
[LiquidationLogic.sol::_calculateAvailableCollateralToLiquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L346-L352) and then converts the `debtToCover` amount into the equivalent `baseCollateral` amount  
```solidity
    unchecked {
      vars.collateralAssetUnit = 10 ** vars.collateralDecimals;
      vars.debtAssetUnit = 10 ** vars.debtAssetDecimals;
    }

    // This is the base collateral to liquidate based on the given debt to cover
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
```

chainlink data feed decimals is ignored and assumes that both assets have the same decimal.

### Internal pre-conditions

_No response_

### External pre-conditions

1.two assets using different decimals

### Attack Path

_No response_

### Impact

the calculation of `baseCollateral` is incorrect which can lead to protocol lost of funds

### PoC

ampl-usd return 18 decimals
https://data.chain.link/feeds/ethereum/mainnet/ampl-usd

eth-usd return 8 decimals
https://data.chain.link/streams/eth-usd

### Mitigation

compare two assets decimals before use it