Shambolic Orchid Crow

High

# No owner address initialized in PoolFactory contract, which could render all onlyOwner check revert.

### Summary

In **PoolFactory** contract, onlyOwner modifier was used in some state variables setting fucntions such as  "setImplementation", this is to ensure only the current owner of the contract can call functions reset state variables  like implementatio, treasury etc.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L91-L101


The onlyOwner modifier actually comes from the basic **Ownable** contract which **PoolFactory** indirectly inherits from (**Ownable2Step** contract). It provides the onlyOwner modifier like this: 

modifier onlyOwner() {
    require(owner() == _msgSender(), "Ownable: caller is not the owner");
    _;
}

To make the modifier works, Ownable contract should initialized the owner in its constructor function. However, in both **PoolFactory** contract and **Ownable2Step** contract, we don't see this constructor being explicitly called, causing the PoolFactory contract not having an initialized owner.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L58-L61




### Root Cause

Ownable contract's constructor is not initialized in either the PoolFactory contract or the Ownable2Step contract. 


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The Ownable contract's constructor is not initialized in the PoolFactory contract, owner is not set, thus all Onlyowner check in PoolFactory will revert, no body could set those state variables.

### PoC

_No response_

### Mitigation

To fix this, the initialowner parameter should be passed in the Ownable constructor alongside the PoolFactory constructor initialization, it could be like this:  

contract PoolFactory is IPoolFactory, Ownable2Step {

    constructor(address _implementation) Ownable(msg.sender) {
        implementation = _implementation;
        treasury = msg.sender;
    }

}