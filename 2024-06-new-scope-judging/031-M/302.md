Polished Iris Antelope

Medium

# Before changing the ```reserveFactor``` on ```PoolFactory```, all existing Pools must update their reserves to avoid using an incorrect ```reserveFactor``` for interest accrued prior to the change.

## Summary
Changing the ```reserveFactor``` of ```PoolFactory```, through ```setReserveFactor()```, doesn't update the reserves of the Pools and as a result more interest si charged with the new ```reserveFactor```.

## Vulnerability Detail
```reserveFactor``` represents the percentage of the interest accrued from Pools that goes to the Zerolend protocol as a fee. All deployed Pools inherit the same ```reserveFactor``` from the factory they were deployed from. Here is the relevant implementation:
```solidity
  function _supply(
    address asset,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
  ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    if (address(_hook) != address(0)) _hook.beforeSupply(msg.sender, pos, asset, address(this), amount, data.hookData);

    res = SupplyLogic.executeSupply(
      _reserves[asset],
      _usersConfig[pos],
      _balances[asset][pos],
      _totalSupplies[asset],
      DataTypes.ExecuteSupplyParams({
@>        reserveFactor: _factory.reserveFactor(),
        asset: asset,
        amount: amount,
        data: data,
        position: pos,
        pool: address(this)
      })
    );

    if (address(_hook) != address(0)) _hook.afterSupply(msg.sender, pos, asset, address(this), amount, data.hookData);
  }
```
For example, in ```_supply()``` of a ```Pool```, the ```reserveFactor``` of factory is passed in the ```executeSupply``` call and then the Zerolend will keep, based on this value, a percentage of interest accrued between calls as a fee. However, this variable can change anytime from the owner of ```PoolFactory```. The vulnerability becomes because this update of ```reserveFactor``` does not notify the Pools so to charge the interest accrued with the previous ```reserveFactor``` and as a result interest that accrued before the ```reserveFactor``` change, are charged with the new ```reserveFactor``` unfairly. 

To understand better this vulnerability, consider this scenario :
1. Interest accrued : 100
   ```reserveFactor```: 2%
   But no action has been taken, so it has not been charged yet.
2. ```reserveFactor``` changes to 5% and interest accrues more to 150.
   Now, an action has been taken and the 5% ```reserveFactor``` is applied to the whole 150 interest while technically it should be applied only to the 50 which accrued after the update. The other 100 should have charged with the previous ```reserveFactor``` which was 2%.

## Impact
The vulnerability results in the miscalculation of fees that goes to the Zerolend protocol making it potentially unfair for either the users or the protocol. Furthermore, it does not match with the expectation of LPs who expect to be charged with a certain amount of fee for a given period but eventually they are charged differently while this period has passed.

## Code Snippet
You can see the vulnerable code snippet that updates the ```reserveFactor``` without notifying the Pools here :
```solidity
  function setReserveFactor(uint256 updated) external onlyOwner {
    uint256 old = reserveFactor;
    reserveFactor = updated;
    emit ReserveFactorUpdated(old, updated, msg.sender);
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolFactory.sol#L112)


## Tool used
Manual Review

## Recommendation
Consider notifying the Pools for the update of the ```reserveFactor``` by looping them and call their ```forceUpdateReserve()``` function.