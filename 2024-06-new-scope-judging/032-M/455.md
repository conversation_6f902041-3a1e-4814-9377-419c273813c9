Curly Pineapple Armadillo

Medium

# `withdrawable` may return a too high amount, causing a revert and preventing users from withdrawing their assets from curated vaults

### Summary

`_withdrawable` returns the minimum value among `totalSupplyAssets - totalBorrowAssets`, `IERC20(asset()).balanceOf(address(pool))` and `supplyAssets`. The issue is that `IERC20(asset()).balanceOf(address(pool))` also includes the treasury fees which will be attempted to be sent to the treasury at the end of a withdrawal. Thus, `withdrawable` may return a value higher than what is actually withdrawable, causing the call to revert and not withdraw any assets from the pool. As a result, the rest of the positions in the withdrawal queue may have insufficient funds to handle the entire withdrawal.

### Root Cause

- In [`_withdrawable`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L126) treasury fees are not considered when getting the minimum value among `totalSupplyAssets - totalBorrowAssets`, `IERC20(asset()).balanceOf(address(pool))` and `supplyAssets`.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. There are two pools in the withdrawal queue, `_withdrawable` returns 10 ETH for the first one and 1 ETH for the second one.
2. User tries to withdraw 10 ETH, but there are 0.5 ETH of treasury fees in the first pool. Normally 9.5 ETH should be withdrawn from the first pool and 0.5 ETH from the second, but as `_withdrawable` wrongly returns 10 ETH and not 10 ETH - 0.5 ETH, the call to `withdrawSimple` on the first pool will fail, as there will be not enough funds to account for the treasury fees.
3. The revert is caught in the `try/catch` block, causing the withdrawal to be forwarded to the next pool.
4. The second pool can only handle a withdrawal of 1 ETH, thus `_withdrawPool` will revert in the following `if` statement: `if (withdrawAmount != 0) revert CuratedErrorsLib.NotEnoughLiquidity();`

### Impact

Curated vault withdrawals can be blocked.

### PoC

_No response_

### Mitigation

Consider, accounting in the treasury fees in `_withdrawable`.