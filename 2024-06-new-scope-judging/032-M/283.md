Curly Pineapple Armadillo

Medium

# `withdrawable` may revert, preventing users from withdrawing their assets from curated vaults

### Summary

`withdrawable` returns the minimum value among `totalSupplyAssets - totalBorrowAssets`, `IERC20(asset()).balanceOf(address(pool))` and `supplyAssets`. The issue is that the calculating `totalSupplyAssets - totalBorrowAssets` is dangerous as it is possible for `totalBorrowAssets` to be more than `totalSupplyAssets`, leading to an underflow.

Furthermore, using `totalSupplyAssets - totalBorrowAssets` to derive the withdrawable number of assets is incorrect as `totalBorrowAssets` includes borrow interest that will be distributed to the suppliers of the borrowed debt, and `totalSupplyAssets` should not be decreased by that amount.

### Root Cause

- Unsafe calculations in [`totalSupplyAssets - totalBorrowAssets`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L126)

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. A pool reserve that is inside of the withdrawal queue has 1 ETH of assets supplied
2. The assets are all borrowed
3. After some time borrow rates accrue, causing `pool.totalDebt(asset())` to increase to 1.1 ETH
4. Now If a user decides to withdraw their assets, all pools inside of the withdrawal queue will be looped through. Once the iteration reaches the pool we observed above, `withdrawable` will revert due to an underflow as `totalBorrowAssets` will be 1.1 ETH, while `totalSupplyAssets` will still be 1 ETH. In some cases `totalSupplyAssets` may also have accrued interest, but the supply interest rate is likely to be lower than the borrow rate, as the functions to derive the rates are different.

### Impact

Curated vault withdrawals can be blocked.

### PoC

_No response_

### Mitigation

As I have stated above using `totalSupplyAssets - totalBorrowAssets` to derive the withdrawable number of assets is questionable and risky. I would recommend changing the `withdrawable` function to getting the minimum value only among `IERC20(asset()).balanceOf(address(pool))` and `supplyAssets`.