Soft Rose Cod

High

# Protocol bad debt is never handled

### Summary

The protocol never handles bad debt. Bad debt accrues due to a token crashing in price and a positions collateral becoming worth less than their debt, this leads to liquidations no longer being profitable, and losses for liquidity providers.

### Root Cause

Not handling bad debt leads to it continuing to accrue interest and leading to eventual insolvency. Eventually, this could result in a bank run and while the majority of users will get their funds, the last users to withdraw will not be left with anything remaining, resulting in a full DoS on their collateral. 

### Internal pre-conditions

1. bad debt accrues and is not handled

### External pre-conditions

1. asset crashes in price and protocol is left with bad debt
2. LP's panic withdraw their provided assets to sell them off to avoid further losses

### Attack Path

1. User deposits collateral and takes out a loan on an asset
2. Their collateral asset crashes in price before they can be liquidated, resulting in the position becoming insolvent and bad debt being created.
3. From here, several things happen. The bad debt will continue to accrue interest causing permanent losses to LP's.
4. LP's try to salvage their losses and rush to withdraw and swap their assets. A bank run occurs and the last users are unable to get their money

### Impact
Two main issues will occur:
1. due to a bank run, bad debt can bubble up to the last users trying to withdraw and result in a lack of available funds and them being unable to withdraw their assets
2. continual accrual of interest on bad debt results in permanent losses for all LP's

### PoC

1. place the following test inside ```NFTPositionManagerTest.t.sol```
2. The following test will revert due to integer underflow when Bob goes to make a full withdraw due their being insufficient funds for a full withdrawal
```solidity
  function test_badDebt() public {
    uint256 mintAmount = 100 ether;
    uint256 supplyAmount = 1 ether;
    uint256 tokenId = 1;

    //approvals
    _mintAndApprove(alice, tokenA, 1000 ether, address(pool));
    _mintAndApprove(alice, tokenB, 1000 ether, address(pool));
    _mintAndApprove(alice, tokenC, 1000 ether, address(pool));
    _mintAndApprove(bob, tokenB, 5000 ether, address(pool));


    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory aliceSupplyA =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, 1000 ether, tokenId, data);
    INFTPositionManager.AssetOperationParams memory aliceBorrowB =
      INFTPositionManager.AssetOperationParams(address(tokenB), alice, 750 ether, tokenId, data);
    INFTPositionManager.AssetOperationParams memory bobSupplyB =
      INFTPositionManager.AssetOperationParams(address(tokenB), bob, 2000 ether, 2, data);
    oracleA.updateAnswer(1e8);
    oracleB.updateAnswer(1e8);

    vm.startPrank(alice);
    tokenA.approve(address(nftPositionManager), 100000 ether);
    tokenB.approve(address(nftPositionManager), 100000 ether);
    tokenC.approve(address(nftPositionManager), 100000 ether);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(aliceSupplyA);
    vm.stopPrank();

    vm.startPrank(bob);
    tokenA.approve(address(pool), 100000 ether);
    tokenB.approve(address(pool), 100000 ether);
    tokenC.approve(address(pool), 100000 ether);
    tokenA.approve(address(nftPositionManager), 100000 ether);
    tokenB.approve(address(nftPositionManager), 100000 ether);
    tokenC.approve(address(nftPositionManager), 100000 ether);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(bobSupplyB);
    vm.stopPrank();

    vm.startPrank(alice);
    nftPositionManager.borrow(aliceBorrowB);
    vm.stopPrank();

    oracleA.updateAnswer(1e7);

    //should revert due to integer underflow, bob is forced to suffer the bad debt
    vm.startPrank(bob);
    nftPositionManager.withdraw(bobSupplyB);
    vm.stopPrank();

  }
```

### Mitigation

One mitigation is to socialize the losses across all LP's. In this case, the losses are split so that no one user or group of users is taking on the bad debt when they withdraw. This will help prevent bank runs and stop the bad debt from accruing interest.
The bad debt calculations could be done inside ```_calculateAvailableCollateralToLiquidate```
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328C1-L329C1