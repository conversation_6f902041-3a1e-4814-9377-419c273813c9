Clever Ebony Halibut

Medium

# Protocol exposed to risk of insolvancy, due to not handling bad debt

## Summary
With the new design the repay() function have become permissoned, in contrast to the original aave Implemention (permissionless, anyone can repay a loan of a user). The new changes exposes the protocol to the risk of insolvancy, because bad debt is not (cannot be) handled. leading to a scenario where the last withdrawal will incure the loss. 

## Vulnerability Detail
The codebase have been forked from the AAVE protocol, however the repay funciton have been made permissioned (only position owner can repay loan)
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L99-L106

```solidity
  function repay(address asset, uint256 amount, uint256 index, DataTypes.ExtraData memory data) public returns (DataTypes.SharesType memory) {
@>>    return _repay(asset, amount, msg.sender.getPositionId(index), data);
  }

  /// @inheritdoc IPoolSetters
  function repaySimple(address asset, uint256 amount, uint256 index) public returns (DataTypes.SharesType memory) {
@>>    return _repay(asset, amount, msg.sender.getPositionId(index), DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
```

as we can see from the implementaion msg.sender is used and only the position owner can repay the position. Those changes have been added to the original codebase.

To put things in perspective, AAVE uses a concept of a safety module. User stake AAVE tokens, and in the event of bad debt, up to 30% of the staked aave tokens are swapped in order to repay the incurred bad debt on behalf of the violator, in order to return the solvancy of the protocol.

The problem in the implementaion now, is if bad debt is incured (Value of debt, becomes more than value of collateral and there is no incentive for liquidations/ violator to repay) There is no way to repay the debt/ close the debt position. This will cause significant insolvancy risk on the protocol, 
The bad debt will continue to gradually accrue interest and grow, but there is no way to close down the debt and repay it.

## Impact
- There is absolutly no way to handle bad debt (repay/socialize), which increase the risk of protocol insolvancy due to the accumulation of bad debt
## Code Snippet

## Tool used

Manual Review

## Recommendation
We recommend that the protocol makes the repay function permissionless as it is the case in the AAVE code base. This way the protocol will be able to to implement and put systems in place to handle the repayment of bad debt and mitigate protocol insolvancy issues. however please keep in mind ,that repaying a debt of position that borrowed from the `positionManger` directly through the pool, will lead to incorrect balance track in the position manager, and incorrect reward distribution. so you need also to notify the position manager in case of a repay directly through pool. 
