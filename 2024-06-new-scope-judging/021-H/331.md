Shiny Seaweed Mongoose

High

# Bad debt are not properly handled, this will lead to loss to the lender that that withdraw last.

### Summary

When a borrower's collateral is below the debt the position is insolvent and liquidating the position will lead to bad debt for the protocol.
That is a loss for the lenders, the problem is that during liquidation bad debts are not accounted for which means that the protocol is still expecting the debt to be paid but it will never be paid, this loss is not spread across lenders.



### Root Cause


The root cause here is that liquation doesn't fully update the reserve in case of insolvency, it just updates the reserve with the token repaid by the liquidator which is not the real state of the reserve. The reserve has suffered a loss but it is not updated accordingly.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163

There is no case within the function below to update the reserve accordingly in case of insolvency.

```js
function executeLiquidationCall(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
    mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
    DataTypes.ExecuteLiquidationCallParams memory params
  ) external {

    ...

    debtReserve.updateInterestRates(
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );
    ...
  }

```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Alice and Bob supplied 500 USD each
2. Mark borrowed 1000 USD
3. Mark's position becomes insolvent
4. The position is liquidated but the collateral can only cover 500 USD of the debt, so 500 USD of bad debt.
5. Alice withdraws her 500 USD and it works.
6. Bob tries to withdraw his 500 USD it reverts because the contract doesn't have it.

From the above only Bob bears the loss due to an insolvent position.

### Impact

The loss is not distributed equally among lenders this will lead to lenders pulling out their funds to avoid being the last to withdraw and incur the lost.

### PoC


Add the following test to the `forge/core/pool` folder and run the test.

This is the output.

Before Liquidation
HF  : 742857142857142857
Debt: 140000000000
After  Liquidation
HF  : 0
Debt: 16190476190
Supplier 1 Balance Before:  1250000000000000000000
Supplier 2 Balance Before:  1250000000000000000000
Supplier 1 Balance After:   2000000000000000000000
Supplier 2 Balance After:   1250000000000000000000

From the above output, we can see that only supplier 1 could take their balance, and supplier 2 incurred the losses alone.

```js

// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import './PoolSetup.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';

import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

contract LiquidationSLPOC is PoolSetup {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;

  address borrower  = makeAddr('borrower');
  address supplier1 = makeAddr("supplier 1");
  address supplier2 = makeAddr("supplier 2");

  address liquidator = makeAddr("liquidator");

  uint256 mintAmountA = 500 ether;
  uint256 mintAmountB = 2000 ether;
  uint256 supplyAmountA = 500 ether;
  uint256 supplyAmountB = 750 ether;
  uint256 borrowAmountB = 700 ether;

  function setUp() public {
    _setUpPool();

    pos = keccak256(abi.encodePacked(borrower, 'index', uint256(0)));
  }

  function testLiquidationSimple() external {

    oracleA.updateAnswer(5e10);

    _mintAndApprove(borrower, tokenA, mintAmountA, address(pool)); // borrower 500 tokenA

    _mintAndApprove(supplier1, tokenB, mintAmountB, address(pool)); // supplier1 2000 tokenA
    _mintAndApprove(supplier2, tokenB, mintAmountB, address(pool)); // supplier2 2000 tokenB
    _mintAndApprove(liquidator, tokenB, mintAmountB, address(pool)); // max 2000 tokenB

    vm.prank(supplier1);
    pool.supplySimple(address(tokenB), supplier1, supplyAmountB, 0); // 750 tokenB supplier1 supply

    vm.prank(supplier2);
    pool.supplySimple(address(tokenB), supplier2, supplyAmountB, 0); // 750 tokenB supplier2 supply


    vm.startPrank(borrower);
    pool.supplySimple(address(tokenA), borrower, supplyAmountA, 0); // 500 tokenA borrower supply
    pool.borrowSimple(address(tokenB), borrower, borrowAmountB, 0); // 700 tokenB borrower borrow
    vm.stopPrank();

    oracleA.updateAnswer(2.6e8);

    (,uint debt0,,,,uint healthFactor0) = pool.getUserAccountData(borrower, 0);

    vm.prank(liquidator);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, borrowAmountB);

    (,uint debt1,,,, uint healthFactor1) = pool.getUserAccountData(borrower, 0);

    uint bal1 = tokenB.balanceOf(supplier1);
    uint bal2 = tokenB.balanceOf(supplier2);

    vm.prank(supplier1);
    pool.withdrawSimple(address(tokenB), address(supplier1), supplyAmountB, 0); 

    vm.prank(supplier2);
    vm.expectRevert();
    pool.withdrawSimple(address(tokenB), address(supplier2), supplyAmountB, 0); 

    console.log("Before Liquidation");
    console.log("HF  :", healthFactor0);
    console.log("Debt:", debt0);

    console.log("After  Liquidation");
    console.log("HF  :", healthFactor1);
    console.log("Debt:", debt1);

    console.log("Supplier 1 Balance Before: ", bal1);
    console.log("Supplier 2 Balance Before: ", bal2);

    console.log("Supplier 1 Balance After:  ", tokenB.balanceOf(supplier1));
    console.log("Supplier 2 Balance After:  ", tokenB.balanceOf(supplier2));

  }

}
```

### Mitigation

Update the reserve accordingly during the liquidation of insolvent positions.