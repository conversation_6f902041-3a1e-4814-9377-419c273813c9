Joyous Rainbow Shark

High

# Bad debt is not handled by the protocol, resulting in withdrawal race conditions for suppliers

### Summary

Once bad debt is in the protocol there is no way to socialize it evenly across all suppliers, meaning the first suppliers to withdraw will retain all funds + interest earned, at the expense of suppliers who are unable to withdraw in time.


### Root Cause

The root cause of bad debt is price fluctions in the collateral/debt making the debt worth more than the collateral. In these circumstances, the liquidation will seize all collateral, but only partially repay the debt (as explained below). The result is a position with no collateral and outstanding debt, for which no price movements can recover the position from being bad debt. 

If the value of bad debt exceeds the difference between the value of all supplyShares and all debtShares then rational suppliers should rush to withdraw their liquidity, as the last users will be left holding `liquidityShares` with no claimable assets. 

Please note, for the below description assume that `debtToCover` and `userCollateralBalance` are in token amounts, not share amounts. I believe this is a separate issue.
In the liquidation flow, `LiquidationLogic::_calculateAvailableCollateralToLiquidate()` is called. This function first calculates `vars.baseCollateral` which is the amount of collateral equal in value to the amount of `debtToCover` (1). This value is then scaled up by the `liquidationBonus` and assigned to `vars.maxCollateralToLiquidate` (2). Then if this value would exceed the position's collateral balance, all collateral is liquidated and the `vars.debtAmountNeeded` is calculated which scales down `debtAssetPrice` based on the amount of collateral being seized (3). Note in step 3, the `liquidationBonus` is still applied so liquidators can always except to receive an amount of collateral worth more than the value of debt they repay. 

```javascript
  function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    ... SKIP!...
@>  vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit); // 1

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus); // 2

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
@>    vars.debtAmountNeeded = ( // 3
        (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }
  ... SKIP!...
  }
```

### Internal pre-conditions

1. A pool has suppliers and borrowers
2. A liquidator partially liquidates a position with bad debt taking all collateral and repaying less than all of the debt

### External pre-conditions

1. Due to price changes, the health of a position declines from 'healthy' (>100% health factor) to 'bad debt' (< avgLTV). Note the liklihood of this occuring varies with the `liquidationThreshold` setting.

### Attack Path

1. A Pool supports tokenA as collateral with an `LTV` of 75%, `liquidationThreshold` of 80%, and `liquidationBonus` of 105%.
2. The same pool has enabled tokenB for borrowing
3. When both tokenA and tokenB have a normalized price of 1e8, a borrower supplies 1e18 tokenA and borrows 0.7e18 tokenB. The resulting healthFactor of the position is .8 * 1e18 * 1e8 / (.7e18 * 1e8) ~= 1.14.
4. In a single price update, the normalized price of tokenA drops to 0.8e8 and the price tokenB normalized increases to 1.2e8. The healthFactor of the position is now .8 * 1e18 * 0.8e8 / (.7e18 * 1.2e8) ~= 0.76. The position is therefore fully liquidatable.
  - However, the debt on the position will not be completely repaid because the liquidator always gets the `liquidationBonus` on the assets they seize.
5. The liquidator attempts to liquidate the max, [all collateral is seized, but the debt amount to repay is scaled down](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L357-L360).
   - Base collat = .7e18 * 1.2e18 / .8e8 = 1.05e18 * 1.05 = 1.103e18
   - But this is more collateral than the position has so it's scaled down to 1e18 worth 0.8 units.
   - The actual debt repaid is therefore (1e18 * 0.8e8 / 1.2e8) / 1.05 = 0.6359e18 tokens worth ~ 0.763 units.
6. The position is now left with no collateral and 0.36e18 debt tokens worth of bad debt.


### Impact

1. Pool suppliers are incentivized to exit their positions from the pool as fast as possible. The last supplier(s) to do so will be left with unclaimable supplyShares. There is likely to be very limited withdrawal liquidity in this time, and savy suppliers may backrun `repay` transactions with `withdraw` transactions to minimize their losses.
2. Bad debt continues to accrue interest which will never be repaid
3. NFTPositions with bad debt will continue to accrue rewards through `NFTRewardDistributor`
4. Bad debt continues to count towards 'useage ratios' of the pool which impact interest rate calculations
5. `supplyIndex` will be out of sync with what the `supplyShares` are actually worth, leading to incorrect valuation of curated vault shares and vault interest calculations

### PoC

_No response_

### Mitigation

1. Implement an admin-only function which takes a position and checks for bad debt, and that the position has been liquidated as much as possible (ie. no remaining collateral). If so, remove the debt and rebase the `liquidityIndex` to account for the value of the debt removed. This socializes the bad debt, and removes the race condition for suppliers to withdraw their liquidity as fast as possible.
2. Do not continue accruing interest on bad debt, and a corresponding reduction in supply interest should also be applied.
