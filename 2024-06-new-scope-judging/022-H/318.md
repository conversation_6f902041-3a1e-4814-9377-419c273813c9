Clever Ebony Halibut

High

# Liquidation will be blocked if there is not enough collateral in the pools

## Summary
By updating the core aave code base, and removing the ability to seize/repay using Atokens instead of underlying tokens, the protocol exposes itself to the risk of liquidation being blocked if there is not enough liquidity in the pools. (safetransfer to liquidator will revert)


## Vulnerability Detail
This is a known issue that aave have mitigated by allowing liquidators to seize ATokens instead of underlying tokens, when there is not enough liquidity in the pools. 
To achieve the modularity expected zerolend have tried to simplify the design by removing this core functionality, this however exposes the protocol to the risk of liquidation being blocked if there is not enough liquidity in the pools.

In the pool as mentioned there is no functionality to liquidate using collateral tokens shares.
https://github.com/A2-Security/zeroland/blob/1afd0f931951afce3e950d2ba8285326492af2b2/zerolend-one/contracts/core/pool/Pool.sol#L109-L116

```solidity
  function liquidate(address collat, address debt, bytes32 pos, uint256 debtAmt, DataTypes.ExtraData memory data) public {
@>>    _liquidate(collat, debt, pos, debtAmt, data);
  }

  /// @inheritdoc IPoolSetters
  function liquidateSimple(address collat, address debt, bytes32 pos, uint256 debtAmt) public {
 @>>   _liquidate(collat, debt, pos, debtAmt, DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
```
The liquidate function after computing the actual amount to seize will be sent from the pool to the liquidator in the burnCollateralTokens function.
```solidity
  function _burnCollateralTokens(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    DataTypes.PositionBalance storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    DataTypes.ReserveCache memory collateralReserveCache = collateralReserve.cache(totalSupplies);
    collateralReserve.updateState(params.reserveFactor, collateralReserveCache);
    collateralReserve.updateInterestRates(
      totalSupplies,
      collateralReserveCache,
      params.collateralAsset,
      IPool(params.pool).getReserveFactor(),
      0,
      vars.actualCollateralToLiquidate,
      params.position,
      params.data.interestRateData
    );

    // Burn the equivalent amount of aToken, sending the underlying to the liquidator
    balances.withdrawCollateral(totalSupplies, vars.actualCollateralToLiquidate, collateralReserveCache.nextLiquidityIndex);
@>>    IERC20(params.collateralAsset).safeTransfer(msg.sender, vars.actualCollateralToLiquidate);
  }
```
however the safeTransfer will revert if there is not enough liquidity in the pool, this will block the liquidation process and make the liquidation fail.
Meaning liquidation will always be blocked for pool conditions following the following equation: 
- `actualCollateralToLiquidate + liquidationProtocolFeeAmount` < `asset.balanceOf(pool)`
- notice that this can be targeted by a malicious attacker in markets volatility events , by borrowing the whole amount available thus blocking liquidation. 

## Impact
- Liquidation will be blocked if there is not enough liquidity in the pools, which exposes the pools to bad debts and insolvency.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L207C1-L230C4
## Tool used

Manual Review

## Recommendation
To solve this issue consider keeping reintreducing the seize token shares from aave, and allowing liquidators to choose whatever the seized amount they want to take is recieved in shares or in underlying token