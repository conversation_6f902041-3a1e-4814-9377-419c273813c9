Powerful Licorice Elephant

Medium

# DoS on liquidations when a reserve’s asset is borrowed/withdrawn fully in a pool.

## Summary
An attacker could use this bug to frontrun a liquidation transaction by withdrawing/borrowing the collateral asset(used for liquidation as a collateral asset to size) from the pool.or by default all asset funds(which are used for liquidation) can be borrowed. As a result, liquidation will revert.


## Vulnerability Detail
same asset can be borrowed/supplied(i.e same asset can be used as collateral and borrowing) in a pool.When a position is liquidated, the liquidator seizes some (or all) of the borrower's assets(a collateral asset) in compensation for repaying the unhealthy debt(a debt asset).However, when all funds of the collateral asset is borrowed from the pool, liquidations won't work because of insufficient pool liquidity.

An attacker could use this bug to frontrun a liquidation transaction by withdrawing/borrowing the collateral asset(used for liquidation as a collateral asset to size) from the pool to create insufficient liquidity(the collateral asset). 

1. Let’s assume, a pool has two assets(asset1 and asset2).
 
2. <PERSON> supplies 500 amounts of asset1 and 500 amounts of asset2 to her position.
 
3. <PERSON> supplies 300 amounts of asset2 to his position.
 
4. Now <PERSON> burrows 300 amounts of asset1 to his position.now totalsupply of asset1 = 200 and totalsupply of asset2 =800 in the pool.
 
5. Asset2 price changes and <PERSON>'s position becomes liquidable. A liquidator calls the liquidate function for <PERSON>'s position with asset2 as collateral and asset1 as debt.
 
6. Before calling the liquidate function, <PERSON> frontruns and withdraws her 500 asset2 and borrows others(remaining in the pool) 300 amounts of  asset2 using asset1 as collateral.
 
7. Now the liquidate function will revert as asset1 funds = 0 in the pool.


## Impact
When all funds of the collateral asset(the collateral asset used for this liquidation) is borrowed from the pool,, the liquidations will fail, causing bad debt on the protocol if the price moves against the borrower. Liquidations are a core invariant of any lending protocol and should never fail in order to prevent bad debt, and ultimately, a bank run.



## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94
## Tool used

Manual Review

## Recommendation
implement a mechanism to prevent frontrun attack.