Smooth Carbon Narwhal

Medium

# Positions can be liquidated immediately after users withdraw assets

## Summary
`Liquidation` occurs when a `position`’s `health factor` drops below `1`. 
To reduce this risk, users need to deposit enough `collateral` to cover their `debts`. 
All `collateral` is converted using the `loan-to-value (LTV) ratio`, and the converted value should exceed the `total debt` when users `borrow` new `assets`. 
This ensures that the `health factor` remains above the defined `minimum limit`.

However, when users `withdraw` `assets` or disable some `collateral`, their `health factor` can drop to `1`, making the position vulnerable to immediate `liquidation` due to small price fluctuations.
## Vulnerability Detail
When setting the `reserve configuration`, we ensure that the `LTV` is lower than the `liquidation threshold`. 
This check is necessary as below comments say.
```solidity
function setReserveConfiguration(
  mapping(address => DataTypes.ReserveData) storage _reserves,
  address asset,
  address rateStrategyAddress,
  address source,
  DataTypes.ReserveConfigurationMap memory config
) public {
  // validation of the parameters: the LTV can
  // only be lower or equal than the liquidation threshold
  // (otherwise a loan against the asset would cause instantaneous liquidation)
  require(config.getLtv() <= config.getLiquidationThreshold(), PoolErrorsLib.INVALID_RESERVE_PARAMS);
}
```
When users `borrow` `assets`, we calculate the current `LTV` and `health factor` in the `calculateUserAccountData` function. 
```solidity
function calculateUserAccountData(
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  DataTypes.CalculateUserAccountDataParams memory params
) internal view returns (uint256, uint256, uint256, uint256, uint256, bool) {
  unchecked {
136:    vars.avgLtv = vars.totalCollateralInBaseCurrency != 0 ? vars.avgLtv / vars.totalCollateralInBaseCurrency : 0;
137:    vars.avgLiquidationThreshold =
      vars.totalCollateralInBaseCurrency != 0 ? vars.avgLiquidationThreshold / vars.totalCollateralInBaseCurrency : 0;
  }

141:  vars.healthFactor = (vars.totalDebtInBaseCurrency == 0)
    ? type(uint256).max
    : (vars.totalCollateralInBaseCurrency.percentMul(vars.avgLiquidationThreshold)).wadDiv(vars.totalDebtInBaseCurrency);
}
```
From `lines 136, 137`, and `141`, we can observe the formulas:
```solidity
current LTV = sum of (collateral[i] × LTV[i]) / sum of (collateral[i])
health factor = sum of (collateral[i] × liquidation threshold[i]) / (total debt)
```
In `line 174`, `vars.userDebtInBaseCurrency` represents the current `total debt`, and `vars.amountInBaseCurrency` represents the new `borrow` amount. 
This means the new `total debt` is the sum of these two values. 
```solidity
function validateBorrow(
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  DataTypes.ValidateBorrowParams memory params
) internal view {
  //add the current already borrowed amount to the amount requested to calculate the total collateral needed.
174:  vars.collateralNeededInBaseCurrency = (vars.userDebtInBaseCurrency + vars.amountInBaseCurrency).percentDiv(vars.currentLtv); //LTV is

177:  require(vars.collateralNeededInBaseCurrency <= vars.userCollateralInBaseCurrency, PoolErrorsLib.COLLATERAL_CANNOT_COVER_NEW_BORROW);
}
```
The check in `line 177` ensures:
```solidity
New total debt ≤ sum of (collateral[i]) × (current LTV) = sum of (collateral[i] × LTV[i])
```

The new `health factor` is calculated as:
```solidity
New health factor = sum of (collateral[i] × liquidation threshold[i]) / (new total debt)
```

Since the `LTV` is always less than the `liquidation threshold`, the new `health factor` remains above the safe limit, preventing immediate `liquidation`.

However, when users `withdraw` `assets`, only the `validateHFAndLtv` check is performed in `line 150`. 
```solidity
function executeWithdraw(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  DataTypes.UserConfigurationMap storage userConfig,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies,
  DataTypes.ExecuteWithdrawParams memory params
) external returns (DataTypes.SharesType memory burnt) {
  if (isCollateral && userConfig.isBorrowingAny()) {
150:    ValidationLogic.validateHFAndLtv(balances, reservesData, reservesList, userConfig, params);
  }
}
```
In this check, we only ensure that the new `health factor` is greater than `1` (`line 254`). 
```solidity
function validateHealthFactor(
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage _balances,
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  DataTypes.UserConfigurationMap memory userConfig,
  bytes32 position,
  address pool
) internal view returns (uint256, bool) {
  (,,,, uint256 healthFactor, bool hasZeroLtvCollateral) = GenericLogic.calculateUserAccountData(
    _balances,
    reservesData,
    reservesList,
    DataTypes.CalculateUserAccountDataParams({userConfig: userConfig, position: position, pool: pool})
  );
  
254:  require(healthFactor >= HEALTH_FACTOR_LIQUIDATION_THRESHOLD, PoolErrorsLib.HEALTH_FACTOR_LOWER_THAN_LIQUIDATION_THRESHOLD);
  return (healthFactor, hasZeroLtvCollateral);
}
```
This allows the `health factor` to drop to `1` after a `withdrawal`, leaving the position vulnerable to immediate `liquidation` due to minor price changes. 
The same issue occurs in the `executeUseReserveAsCollateral` function.
## Impact
For example, if the `liquidation threshold` is set at `130%` of the `LTV` for safety, users should maintain a `health factor` of at least `1.3` when `depositing` or `borrowing`. 
This provides enough time for users to increase their `health factor` by adding more `collateral` or `repaying` `debts` if the `health factor` falls below `1.3`. 
However, due to `withdrawals`, the `health factor` can still drop to `1`, which should be avoided just as we prevent it when `borrowing`.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L155-L158
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L135-L143
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L174-L177
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L150
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L254
## Tool used

Manual Review

## Recommendation
Instead of  checking if the `health factor` is greater than `1` during `withdrawals` or when disabling `collateral`, we should perform the `LTV check`, just as we do when `borrowing`.