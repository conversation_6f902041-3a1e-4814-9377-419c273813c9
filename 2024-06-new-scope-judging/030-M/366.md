Happy Corduroy Dalmatian

Medium

# Missing ERC20 Transfer Checks Can Permanently Trap Rewards

## Summary
Calls to the ERC20 transfer method in the ``NFTRewardDistributor::notifyRewardAmount`` and ``NFTRewardDistributor::getReward`` functions do not check the return value, leading to a vulnerability where tokens could be stuck in the contract if the transfer fails. This issue arises with Some tokens (like USDT) that do not implement the EIP20 standard correctly and their ``transfer/transferFrom`` function return void instead of a success boolean. Calling these functions with the correct EIP20 function signatures will revert. This could lead to loss of rewards for users if the contract interacts with such tokens.
## Vulnerability Detail
Certain ERC20 tokens, like ZRX,USDT,BNB e.t.c. do not revert when a transfer fails but instead return false. The current implementation of the ``transferFrom`` and ``transfer`` calls in both the``NFTRewardDistributor::notifyRewardAmount``  and ``NFTRewardDistributor::getReward``  functions does not check the return value to confirm whether the transfer succeeded. In the event that one of these tokens is used, the rewards could be stuck in the contract as the transfer fails silently, while the protocol logic proceeds as though the transfer succeeded. This leaves the affected tokens locked in the contract, potentially causing permanent loss of user funds.
## Impact
* Severity: Medium
* Affected Party: Users of the protocol
* Potential Loss: Users could lose their rewards if such ERC20 token that returns false on transfer failure is used.
*Example Impact: If the rewardsToken does not revert but returns false upon transfer failure, the rewards meant for the user will remain stuck in the contract. This results in unrecoverable loss for users while giving them the false impression that rewards were successfully transferred.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L125
```solidity
 function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
@>    rewardsToken.transferFrom(msg.sender, address(this), reward);

    bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }
```
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L88
```solidity
 function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
    _updateReward(tokenId, _assetHash);
    reward = rewards[tokenId][_assetHash];
    if (reward > 0) {
      rewards[tokenId][_assetHash] = 0;
     @> rewardsToken.transfer(ownerOf(tokenId), reward);
      emit RewardPaid(tokenId, ownerOf(tokenId), reward);
    }
  }
```
## Tool used

Manual Review

## Recommendation
To ensure tokens are not stuck in the contract, use OpenZeppelin’s SafeERC20 library to handle token transfers. This library ensures that the return value of transfer and transferFrom is checked, and it reverts if the transfer fails. Here’s how the transfer should be updated
```diff
 function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
    _updateReward(tokenId, _assetHash);
    reward = rewards[tokenId][_assetHash];
    if (reward > 0) {
      rewards[tokenId][_assetHash] = 0;
 -    rewardsToken.transfer(ownerOf(tokenId), reward);
+   rewardsToken.safeTransfer(ownerOf(tokenId), reward);
      emit RewardPaid(tokenId, ownerOf(tokenId), reward);
    }
  }
```
```diff
 function notifyRewardAmount(uint256 reward, address pool, address asset, bool isDebt) external onlyRole(REWARDS_ALLOCATOR_ROLE) {
 -  rewardsToken.transferFrom(msg.sender, address(this), reward);
+  rewardsToken.safeTransferFrom(msg.sender, address(this), reward);

    bytes32 _assetHash = assetHash(pool, asset, isDebt);
    _updateReward(0, _assetHash);

    if (block.timestamp >= periodFinish[_assetHash]) {
      rewardRate[_assetHash] = reward.div(rewardsDuration);
    } else {
      uint256 remaining = periodFinish[_assetHash].sub(block.timestamp);
      uint256 leftover = remaining.mul(rewardRate[_assetHash]);
      rewardRate[_assetHash] = reward.add(leftover).div(rewardsDuration);
    }
```
