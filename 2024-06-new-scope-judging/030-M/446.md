Ancient Crepe Griffin

Medium

# check the return of transfer in "getReward"

## Summary
we are not checking the output of the transfer function,lets suppose if the transfer is failed then    rewards will be lost.
## Vulnerability Detail
 function getReward(uint256 tokenId, bytes32 _assetHash) public nonReentrant returns (uint256 reward) {
    _updateReward(tokenId, _assetHash);
    reward = rewards[tokenId][_assetHash];
    if (reward > 0) {
      rewards[tokenId][_assetHash] = 0;
   @>>   rewardsToken.transfer(ownerOf(tokenId), reward);
      emit RewardPaid(tokenId, ownerOf(tokenId), reward);
    }
  }
## Impact
  rewards  can be   lost.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTRewardsDistributor.sol#L88
## Tool used

Manual Review

## Recommendation
(success,)= rewardsToken.transfer(ownerOf(tokenId), reward);
require(success);