Great <PERSON> Shetland

Medium

# Profit is transferred to the treasury without checking if there's enough balance, blocking users from withdrawing their funds

### Summary

When users borrow and repay debts, some interest is accumulated in the pools, this interest will be distributed to suppliers, and part of this profit/interest will be minted/transferred to the protocol's treasury. If the reserve factor is set to a value >0, interest will be accrued to the treasury whenever `ReserveLogic::updateState` (which is called in almost every action).

The protocol sends the treasury its profit in [`PoolLogic::executeMintToTreasury`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L98), by just transferring some calculated amount of a specific asset, however, it is not checking if the pool has enough balance to do so.
This blocks users from withdrawing their supplied funds.

Let's take the following example:
1. <PERSON> supplies 1k USDC
2. <PERSON> supplied some collateral and borrowed the 1k USDC
3. Some time passes, and some interest is accumulated
4. <PERSON> repays half of her debt (500 + interest)
5. <PERSON> tries to withdraw the repaid amount but the TX reverts, as it'll try to send a small profit to the treasury

Knowing that <PERSON> in this case should be able to withdraw all of the repaid funds, as <PERSON> is still in debt and more funds are coming in. So skipping the transfer in case there are no enough funds will keep the treasury's rights saved.

### Root Cause

In `PoolLogic::executeMintToTreasury`, the protocol sends the treasury's profit to the treasury without checking if it has enough balance to do so.

### Impact

Users won't be able to withdraw all of their funds.

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract Contest_Pool is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    poolFactory.setConfigurator(address(new PoolConfigurator(address(poolFactory))));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      ltv: 0,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _syncOracles();
  }

  function testInvalidTransfer_DOS() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    {
      USDC.mint(bob, USDCamount);
      USDC.mint(alice, USDCamount);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      USDC.approve(address(pool), type(uint256).max);
    }

    // Bob deposits 1k USDC
    vm.prank(bob);
    pool.supply(address(USDC), bob, USDCamount, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));

    // Alice supplies 1 WETH
    // Alice borrows 1k USDC
    vm.startPrank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);
    pool.borrowSimple(address(USDC), alice, USDCamount, 0);
    vm.stopPrank();

    // Some time passes
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserve(address(USDC));

    // Some interest is accumulated
    uint256 aliceDebt = pool.getDebt(address(USDC), alice, 0);
    assertGt(aliceDebt, USDCamount);

    uint256 repayAmount = aliceDebt / 2;

    // Alice repays half her debt
    vm.prank(alice);
    pool.repaySimple(address(USDC), repayAmount, 0);

    assertEq(repayAmount, USDC.balanceOf(address(pool)));

    // Bob tries to withdraw the repaid amount, reverts with not enough balance, as the protocol will try to pay the treasury
    vm.prank(bob);
    vm.expectRevert(bytes('ERC20: transfer amount exceeds balance'));
    pool.withdraw(address(USDC), bob, repayAmount, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
}
```
</details>

### Mitigation

Check if the contract has enough funds to send the profit to the treasury before attempting a transfer in `PoolLogic::executeMintToTreasury`.