Chilly Cherry Deer

High

# Flaw in calculation allocation to treasury  in `_handleFlashLoanRepayment`, will cause a misallocation of funds for liquidity providers

### Summary

The direct allocation of the entire(100%) flash loan premium to the treasury, without implementing logic to distribute it , will cause a misallocation of funds for liquidity providers, results in loss of funds for liquidity providers 

[FlashLoanLogic.sol#L116](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116)
- current logic allocates the full premium to the treasury, neglecting any portion meant for liquidity providers.
```solidity
 _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
```

#### Details

1.  `_params.totalPremium` : caculation of the total premium calculated for the flash loan
```solidity
uint256 totalPremium = _params.amount.percentMul(_params.flashLoanPremiumTotal);
```
- `_params.amount` : The principal amount borrowed in the flash loan.
- `_params.flashLoanPremiumTotal` : The premium rate

2. Scaling with Liquidity Index & divides this total premium by the liquidity index: `rayDiv(cache.nextLiquidityIndex)`
3. Final allocation to Treasury : This logic updates the treasury's share of the premium by adding the `totalPremium` to the existing total in `_reserve.accruedToTreasuryShares`.
```solidity
 _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
```

#### Issue :  `Lack of Distribution Logic :: The code directly allocates 100% the entire scaled premium to the treasury without considering any division between the treasury and liquidity providers, lead to misallocation of funds`
- The total premium is calculated based on the borrowed amount and premium rate.
- The premium is then scaled by the liquidity index to ensure it aligns with the reserve's current liquidity state.
- Finally, the scaled premium is added to the treasury's accrued shares.


### Root Cause

- In [FlashLoanLogic.sol#L116](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116) directly allocates the entire `totalPremium` to `accruedToTreasuryShares`.
- There is no logic to calculate and allocate a portion of the premium to liquidity providers, means that the premium is not distributed according to specified split between the treasury and liquidity providers, leading to potential misallocation of funds.


### Impact

- The liquidity providers suffer loss of funds.
- potentially reducing their earnings, & 
- potentially causing them to withdraw their liquidity, which affects the protocol's overall liquidity.

Example: 
if `totalPremium` after calculation logic is 100 units, according to current  logic 100 units is added to `_reserve.accruedToTreasuryShares` , this shows 100% allocation of premium to protocol, it leads to loss of funds for liquidity providers.


### Mitigation

- Implement distribution logic correctly : Introduce logic to calculate the portion of the premium meant for the treasury and the portion meant for liquidity providers.
- and then use the calculated shares to update `accruedToTreasuryShares`