Innocent Raisin Dachshund

Medium

# Withdrawal of funds can be DOSed due to large accrued treasury shares

### Summary

Large accrued treasury shares can cause disruption to withdrawal of funds. The user won't be able to withdraw his funds since the liquidity of the reserves should also accommodate the transfer of income funds (accrued treasury shares) to treasury before the withdrawal push through.

### Root Cause

The asset reserve [withdrawal process](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L84) involves a crucial step of transferring interest income funds (accrued treasury shares) from the asset reserves to treasury. This can cause a problem because this accrued treasury shares can grow so large that it can cause disruption to withdrawal of funds of users. The user should be only concerned about "his funds" when withdrawing but forced to wait until the funds are already available both for accrued treasury shares and user funds.

This issue can be more apparent if the asset reserves won't receive any deposits for a long time and user has no choice but to wait and his funds are not available to withdraw until then.

### Internal pre-conditions

1. The asset reserve has very low deposit activity. It takes average of once a month deposit activity.
2. A very large borrowing-repayment-borrowing happened in a span of one month period.
3. Reserve factor has been setup (from sample scenario given we use 50%)
4. Borrow cap and supply cap are not exceeded

### External pre-conditions

1. The asset is not widely popular to attract more liquidity.

### Attack Path

This scenario can be attack path and can also just a normal sequences to happen depending on conditions.

1. Lender A deposit funds to reserve A amounting 10 million Atokens for lending purposes.
2. Borrower B, borrowed 10 million Atokens from reserve A.
3. After a month, Borrower B repaid his loan with interest, the total Atokens paid is composed of 10 million Atokens principal and 100,000 Atokens for interest. 50% of 100,000 Atokens interest paid will be treated as accrued treasury shares due to reserve factor of 50%
4. Borrower C, borrowed 10,060,000 Atokens
5. Lender D, deposited 10,000 Atokens for lending
6. Lender D, quickly changed his mind and decided  to withdraw his assets. 
Lender D will fail to withdraw his funds even a single token due to insufficient balance of Reserve A to accommodate the accrued treasury shares amounting 50,000 Atokens and his own funds of 10,000 Atokens. Total to withdraw is 60,000 while the balance is only 50,000, therefore withdrawal will revert. (see ref 6 in below image)
7. Despite having balance of 50,000 Atokens for the reserve. Lender D can't withdraw his 10,000 Atokens.

See image below to trace the effect of the above steps to the balances.
<img width="457" alt="image" src="https://github.com/user-attachments/assets/bbcb828d-ceb9-4b76-af96-ae84f07303be">



If the asset reserve is not really popular and has difficulty in attracting liquidity, this can be disastrous to the depositors who are forced to wait for additional deposits. In other words, their funds are stuck.

Please be reminded also that the protocol intends to apply user-set fixed rate lending in which utilization rate has no effect. If this is the case, the borrower won't be forced to pay his loan because his interest rate is fixed and not increasing. Liquidity will be affected and repayment won't happen anytime soon.

### Impact

The withdrawal of funds will be disrupted and the users will wait depending on availability of liquidity. In other words, their funds are stuck.

### PoC

_No response_

### Mitigation

Transferring of actual interest income of treasury should be done in repayment of loan and not from withdrawal.