Clever Ebony Halibut

Medium

# Potential Denial of Service in `withdraw` Function Due to Insufficient checks on Treasury Minting

## Summary
The `withdraw` function in the ZeroLend protocol’s Pool contract contains a call to the `executeMintToTreasury` function. If the call to `executeMintToTreasury` does not succeed due to insufficient funds, the entire `withdraw` transaction will revert. This could potentially lead to a Denial of Service (DoS) condition where users are unable to withdraw their assets even though there is enough balance in the contract.
The cause of this issue is the missing handling and checks to the current pool balance after withdraws.

## Vulnerability Detail

With each withdraw from a pool, the protocol tries to mint the current accrued protocol fees to the treasury. Those fees are sent using the method `executeMintToTreasury` which is implemented in `PoolLogic.sol`

```solidity
  function _withdraw(
    address asset,
    address to,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
  ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    require(to != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);
    //note what is the purpose of the reserveFactor here? answer:
    DataTypes.ExecuteWithdrawParams memory params =
      DataTypes.ExecuteWithdrawParams({reserveFactor: _factory.reserveFactor(), asset: asset, amount: amount, position: pos, destination: to, data: data, pool: address(this)});

    if (address(_hook) != address(0)) _hook.beforeWithdraw(params);

    res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);
    // send amount accrue to treasury , and update the share supply and reset treasury accrue to 0
@>>    PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);

    if (address(_hook) != address(0)) _hook.afterWithdraw(params);
```
The problem however is that in the `executeMintToTreasury` function, the Logic will try to send the whole accumulated fees to the treasury, but doesn't check if there is enough liquidity in the protocol to satisfy the full minting. Leading to a DoS on withdraw. (`IERC20(asset).safeTransfer(treasury, amountToMint)` will revert if the contract doesn't have enough liquidity)

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
@>      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
@>      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```

### Proof of Concept
1. User fetch the pool balance and see that enough liquidity exist to withdraw an amount.
2. The `withdraw` function  will send the balance the user want to withdraw first, and then will try to transfer all the accrued protocol fees to the treasury.
3. there are not enough funds left in the pool, the `executeMintToTreasury` reverts, causing the entire `withdraw` function to revert.
4. As a result, the user is unable to withdraw their funds despite the pool having a sufficient balance/liquidity.
## Impact
Pool withdraws will be Dossed even though there is enough balance in the pool.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103

## Tool used

Manual Review

## Recommendation
After a withdraw, the protocol should check how much balance is left in the pool, and if it's not enough to send the whole amount of accrued fees to the treasury, the protocol should send the remaining balance to the treasury.
We recommend adding the following logic to the `executeMintToTreasury` function:

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
      uint256 availableLiquidity = IERC20(asset).balanceOf(address(this));
      
      uint256 actualAmountToMint = amountToMint < availableLiquidity ? amountToMint : availableLiquidity;
      uint256 actualSharesToReduce = actualAmountToMint.rayDiv(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, actualAmountToMint);
      totalSupply.supplyShares -= actualSharesToReduce;
      reserve.accruedToTreasuryShares -= actualSharesToReduce;

      emit PoolEventsLib.MintedToTreasury(asset, actualAmountToMint);
    }
  }
```