Sneaky Hazelnut Lizard

Medium

# DOS to withdrawal Function when the user (whales) tries to withdraw the max liquidity available

### Summary


Zerolend's current withdrawal implementation combines user withdrawals with minting funds to the treasury, which introduces a significant **Denial of Service (DOS) vulnerability**. If the pool does not have sufficient liquidity to cover both the user's withdrawal and the treasury's portion, the entire transaction reverts, preventing users from withdrawing their own funds. This issue can especially impact whales or large withdrawals, creating a scenario where liquidity is available for users but locked due to insufficient funds for the treasury mint.

This report compares Zerolend's withdrawal logic with Aave V3's separation of user withdrawals and treasury minting, highlighting how the combined functionality in Zerolend creates a critical flaw. 

---

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L62-L87

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L78-L103

Aave v3 implementation-

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/pool/Pool.sol#L195-L216


https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/SupplyLogic.sol#L94-L163

```solidity
  /**
   * @notice Implements the withdraw feature. Through `withdraw()`, users redeem their aTokens for the underlying asset
   * previously supplied in the Aave protocol.
   * @dev Emits the `Withdraw()` event.
   * @dev If the user withdraws everything, `ReserveUsedAsCollateralDisabled()` is emitted.
   * @param reservesData The state of all the reserves
   * @param reservesList The addresses of all the active reserves
   * @param eModeCategories The configuration of all the efficiency mode categories
   * @param userConfig The user configuration mapping that tracks the supplied/borrowed assets
   * @param params The additional parameters needed to execute the withdraw function
   * @return The actual amount withdrawn
   */
  function executeWithdraw(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    mapping(uint8 => DataTypes.EModeCategory) storage eModeCategories,
    DataTypes.UserConfigurationMap storage userConfig,
    DataTypes.ExecuteWithdrawParams memory params
  ) external returns (uint256) {
    DataTypes.ReserveData storage reserve = reservesData[params.asset];
    DataTypes.ReserveCache memory reserveCache = reserve.cache();

    reserve.updateState(reserveCache);

    uint256 userBalance = IAToken(reserveCache.aTokenAddress).scaledBalanceOf(msg.sender).rayMul(
      reserveCache.nextLiquidityIndex
    );

    uint256 amountToWithdraw = params.amount;

    if (params.amount == type(uint256).max) {
      amountToWithdraw = userBalance;
    }

    ValidationLogic.validateWithdraw(reserveCache, amountToWithdraw, userBalance);

    reserve.updateInterestRates(reserveCache, params.asset, 0, amountToWithdraw);

    bool isCollateral = userConfig.isUsingAsCollateral(reserve.id);

    if (isCollateral && amountToWithdraw == userBalance) {
      userConfig.setUsingAsCollateral(reserve.id, false);
      emit ReserveUsedAsCollateralDisabled(params.asset, msg.sender);
    }

    IAToken(reserveCache.aTokenAddress).burn(
      msg.sender,
      params.to,
      amountToWithdraw,
      reserveCache.nextLiquidityIndex
    );

    if (isCollateral && userConfig.isBorrowingAny()) {
      ValidationLogic.validateHFAndLtv(
        reservesData,
        reservesList,
        eModeCategories,
        userConfig,
        params.asset,
        msg.sender,
        params.reservesCount,
        params.oracle,
        params.userEModeCategory
      );
    }

    emit Withdraw(params.asset, msg.sender, params.to, amountToWithdraw);

    return amountToWithdraw;
  }
```

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/pool/Pool.sol#L443-L447

```solidity
  /// @inheritdoc IPool
  function mintToTreasury(address[] calldata assets) external virtual override {
    PoolLogic.executeMintToTreasury(_reserves, assets);
  }
```

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/PoolLogic.sol#L79-L107

```solidity
  /**
   * @notice Mints the assets accrued through the reserve factor to the treasury in the form of aTokens
   * @param reservesData The state of all the reserves
   * @param assets The list of reserves for which the minting needs to be executed
   */
  function executeMintToTreasury(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address[] calldata assets
  ) external {
    for (uint256 i = 0; i < assets.length; i++) {
      address assetAddress = assets[i];

      DataTypes.ReserveData storage reserve = reservesData[assetAddress];

      // this cover both inactive reserves and invalid reserves since the flag will be 0 for both
      if (!reserve.configuration.getActive()) {
        continue;
      }

      uint256 accruedToTreasury = reserve.accruedToTreasury;

      if (accruedToTreasury != 0) {
        reserve.accruedToTreasury = 0;
        uint256 normalizedIncome = reserve.getNormalizedIncome();
        uint256 amountToMint = accruedToTreasury.rayMul(normalizedIncome);
        IAToken(reserve.aTokenAddress).mintToTreasury(amountToMint, normalizedIncome);

        emit MintedToTreasury(assetAddress, amountToMint);
      }
    }
  } 
```

Funds are minted to the treasury account like every other account this account can now call withdraw to make make withdrawals and burn atokens.


https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/AToken.sol#L108-L114 


```solidity
 /// @inheritdoc IAToken
  function mintToTreasury(uint256 amount, uint256 index) external virtual override onlyPool {
    if (amount == 0) {
      return;
    }
    _mintScaled(address(POOL), _treasury, amount, index);
  }
```



### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

In Zerolend, the **withdrawal function** not only handles the user's withdrawal but also attempts to mint assets to the treasury in the same transaction. This is problematic because:

1. **Treasury Minting**: During every withdrawal, Zerolend simultaneously attempts to mint the treasury's share of accrued funds.
2. **Insufficient Liquidity**: If the pool has enough liquidity to cover the user's withdrawal but not enough to mint the treasury's funds, the transaction reverts, causing a denial of service for the user.

Example of the problematic code in Zerolend's `_withdraw` function:
```solidity
res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);
PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);
```

The issue arises when the following minting logic fails due to insufficient funds:
```solidity
if (accruedToTreasuryShares != 0) {
    uint256 normalizedIncome = reserve.getNormalizedIncome();
    uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

@audit>> revert >>     IERC20(asset).safeTransfer(treasury, amountToMint);
   
   totalSupply.supplyShares -= accruedToTreasuryShares;

    emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
}
```

When there aren't enough available funds in the pool, the transaction will revert, even if the user has sufficient liquidity to withdraw their own funds, thus creating a **Denial of Service (DOS) risk**.

---

### Comparison with Aave V3:

Aave V3 separates user withdrawals from treasury minting into two distinct functions. This ensures that the user's withdrawal process is not impacted by the treasury’s minting operations. Specifically, Aave's withdrawal function (`withdraw`) and treasury minting function (`mintToTreasury`) are separate:

1. **User Withdrawal Function**:
   ```solidity
   function withdraw(
       address asset,
       uint256 amount,
       address to
   ) public virtual override returns (uint256) {
       return SupplyLogic.executeWithdraw(...);
   }
   ```

2. **Treasury Minting Function**:
   ```solidity
   function mintToTreasury(address[] calldata assets) external virtual override {
       PoolLogic.executeMintToTreasury(...);
   }
   ```

This separation ensures that the user withdrawal process is isolated from treasury operations, preventing the treasury minting logic from blocking user withdrawals.

---

### Impact

**Denial of Service (DOS)**: Users, especially those making large withdrawals, are unable to withdraw their funds if the pool does not have sufficient liquidity to mint the treasury’s accrued share. This could lead to severe service disruptions, particularly for whales or during periods of heavy liquidity usage.

### PoC

_No response_

### Mitigation

To resolve this issue, the withdrawal process and treasury minting process should be separated or the withdrawal logic should ensure that the transfer to the treasury only proceeds if there is sufficient liquidity to cover the user's withdrawal even if we can't cover the treasury's share. Here are the key steps:

1. **Separate Functions**: Implement the treasury minting logic in a separate function, as seen in Aave V3. This ensures that user withdrawals are not blocked by treasury minting. 

2. **Check for Sufficient Liquidity**: Modify the withdrawal logic to first check if there is enough liquidity to cover both the user's withdrawal and the treasury's minting. If there is not enough liquidity, the treasury minting should be skipped, allowing the user withdrawal to proceed.

Example mitigation in the withdrawal logic:

```solidity
 function _withdraw(
    address asset,
    address to,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
  ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    require(to != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);

    DataTypes.ExecuteWithdrawParams memory params = DataTypes.ExecuteWithdrawParams({
      reserveFactor: _factory.reserveFactor(),
      asset: asset,
      amount: amount,
      position: pos,
      destination: to,
      data: data,
      pool: address(this)
    });

    if (address(_hook) != address(0)) _hook.beforeWithdraw(params);

    res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);

++     poolLiquidity = IERC20(asset()).balanceOf(address(this));

--  PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);

++    PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset, poolLiquidity);

    if (address(_hook) != address(0)) _hook.afterWithdraw(params);
  }
```


```solidity
 /**
   * @notice Mints the assets accrued through the reserve factor to the treasury in the form of aTokens
   * @param reservesData The state of all the reserves
   * @param asset The reserves for which the minting needs to be executed
   */
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset,
 ++  poolLiquidity
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
    



++  if (poolLiquidity >= amountToMint) {
++     IERC20(asset).safeTransfer(treasury, amountToMint);
++      totalSupply.supplyShares -= accruedToTreasuryShares;

++    emit PoolEventsLib.MintedToTreasury(asset, amountToMint);

++  } else {
++    // Skip transferrring to treasury if liquidity is insufficient
  
++ }

    
     
    }
  }
```


---