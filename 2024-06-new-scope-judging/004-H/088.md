Rapid Onyx Meerkat

High

# The `executeMintToTreasury` function incorrectly decreases the total supply of shares

### Summary

The `executeMintToTreasury` function incorrectly decreases the total supply of shares, potentially leading to an error in tracking the total supply.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L93-L102
```solidity
if (accruedToTreasuryShares != 0) {
  reserve.accruedToTreasuryShares = 0;
  uint256 normalizedIncome = reserve.getNormalizedIncome();
  uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

  IERC20(asset).safeTransfer(treasury, amountToMint);
  totalSupply.supplyShares -= accruedToTreasuryShares;    <@

  emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
}
```

### Root Cause

When user supply assets to pool , the totalSupply increase:
```solidity
  function depositCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage totalSupply,
    uint256 amount,
    uint128 index
  ) internal returns (bool isFirst, uint256 sharesMinted) {
    sharesMinted = amount.rayDiv(index);
    require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
    isFirst = self.supplyShares == 0;
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares += sharesMinted;
    totalSupply.supplyShares += sharesMinted;  <@
  }
```
when user withdraw assets from pool,the totalSupply decrease.
```solidity
  function withdrawCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares -= sharesBurnt;
    supply.supplyShares -= sharesBurnt;   <@
  }
```

the extra accruedToTreasuryShares is from flashLoan process,after user pay back assets along with `totalPremium`, those assets is not belongs to totalSupply.
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
```
Therefore, subtracting `accruedToTreasuryShares` from `supplyShares` is clearly incorrect and will result in calculation errors. In the following test, we added a flashloan operation between `supply` and `withdraw`, which caused a revert due to an overflow in calculations

### Internal pre-conditions

1.alice suplpy
2.external user flashLoan
3.alice withdraw 

### External pre-conditions

_No response_

### Attack Path

1.alice call nftPositionManager.supply()
2. owner call setFlashloanPremium() 
3.external user call pool.flashLoan()
4.alice call nftPositionManager.withdraw() 

### Impact

1. the tracking the total supply is incorrect
2. user maybe unable to withdraw due to revert

### PoC

to add to file `NFTPositionManagerTest.t.sol`
```solidity
  function testexecuteMintToTreasury() public {
      //alice supply
      vm.warp(1641070800);
      uint256 mintAmount = 100 ether;
      DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
      INFTPositionManager.AssetOperationParams memory params =
        INFTPositionManager.AssetOperationParams(address(tokenA), alice, 1e18, 1, data);

      _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));

      vm.startPrank(alice);
      nftPositionManager.mint(address(pool));
      nftPositionManager.supply(params);
      vm.stopPrank();

      //set flashLoanPremiumToProtocol.
      poolFactory.setFlashloanPremium(100);

      //executeMintToTreasury
      tokenA.mint(address(this), 100e18);
      tokenA.approve(address(pool), type(uint256).max);
      pool.flashLoan(address(this), address(tokenA), 1e18, bytes(''), data);

      //alice withdraw.
      params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, type(uint256).max, 1, data);

      vm.startPrank(alice);
      nftPositionManager.withdraw(params);
      vm.stopPrank();

  }
```

out:
```shell
    │   │   │   │   │   └─ ← [Return] NFTPostionManagerTest: [0x7FA9385bE102ac3EAc297483Dd6233D62b3e1496]
    │   │   │   │   ├─ [7418] PoolLogic::dcd96c16(4b7638b16fb7af619e7e45eab2af3c081f420c1a9aa2ccd71c4f586ba4135fda00000000000000000000000000000000000000000000000000000000000000330000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e14960000000000000000000000005991a2df15a8f6a256d3ec51e99254cd3fb576a9) [delegatecall]
    │   │   │   │   │   ├─ [0] console::log("accruedToTreasuryShares:", 10000000000000000 [1e16]) [staticcall]
    │   │   │   │   │   │   └─ ← [Stop] 
    │   │   │   │   │   ├─ [0] console::log("totalSupply.supplyShares:", 0) [staticcall]
    │   │   │   │   │   │   └─ ← [Stop] 
    │   │   │   │   │   ├─ [3034] tokenA::transfer(NFTPostionManagerTest: [0x7FA9385bE102ac3EAc297483Dd6233D62b3e1496], 10000000000000000 [1e16])
    │   │   │   │   │   │   ├─ emit Transfer(from: RevokableBeaconProxy: [0xffD4505B3452Dc22f8473616d50503bA9E1710Ac], to: NFTPostionManagerTest: [0x7FA9385bE102ac3EAc297483Dd6233D62b3e1496], value: 10000000000000000 [1e16])
    │   │   │   │   │   │   └─ ← [Return] true
    │   │   │   │   │   └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)
    │   │   │   │   └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)
    │   │   │   └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)
    │   │   └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)
    │   └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)
    └─ ← [Revert] panic: arithmetic underflow or overflow (0x11)

Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 11.49ms (1.79ms CPU time)

Ran 1 test suite in 1.43s (11.49ms CPU time): 0 tests passed, 1 failed, 0 skipped (1 total tests)

Failing tests:
Encountered 1 failing test in test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[FAIL. Reason: panic: arithmetic underflow or overflow (0x11)] testexecuteMintToTreasury() (gas: 820592)
```

### Mitigation

```diff
@@ -89,14 +89,12 @@ library PoolLogic {
     DataTypes.ReserveData storage reserve = reservesData[asset];
 
     uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;
     if (accruedToTreasuryShares != 0) {
       reserve.accruedToTreasuryShares = 0;
       uint256 normalizedIncome = reserve.getNormalizedIncome();
       uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
 
       IERC20(asset).safeTransfer(treasury, amountToMint);
-      totalSupply.supplyShares -= accruedToTreasuryShares;
```