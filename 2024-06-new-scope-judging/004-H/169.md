Mythical Alabaster Snake

High

# When accruing to treasury, totalShares is not incremented but it is decremented when withdrawing to treasury

when depositing totalshares of an asset dont increase 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116
but when withdrawing 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103 

totalShares is decremented 

a simple example would be assuming index = 1.0. bob deposits 10 eth
and is minted 10 shares (totalShares = 10 eth shares)
bob borrows back his 10eth and is minted 10 debt shares 
and reserve factor is 2&
bob pays back at 1.01 and pays back 10.1 eth, amount to treasury would hence be (10.1 - 10) * 2 / 100 = 0.02 eth shares 
bob withdraws his entire shares which would be 10 eth shares (totalShares= 0)||
any instance would be when a flash loan happens, the treasury is minted shares but totalSupply.supplyShares is not incremented, in fact it is especially true here where the fee could end up being very large
when the call attempts to mint to treasury it will try to do totalSupply.supplyShares -= accruedToTreasuryShares; which would cause the call to revert as supply shares is zero 

