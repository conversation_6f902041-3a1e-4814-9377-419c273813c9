Joyous Cedar Tortoise

High

# _accrueToTreasury (interest fee) does not increment total supply

### Summary

[`_accrueToTreasury`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213) (interest fee) does not increment total supply, but it does accrue shares to the treasury (which will later be burned).

This means that after a single withdrawal occurs and `executeMintToTreasury()` is called, the total supply of the shares will be less than the sum of each user’s share balance.

This leads to a race to withdraw since the last user will not be able to redeem all their shares

### Root Cause

_accrueToTreasury (interest fee) does not increment total supply, but it increments the variable `_reserve.accruedToTreasuryShares` by `amountToMint`

Then within `executeMintToTreasury()`, the following occurs:

```solidity
IERC20(asset).safeTransfer(treasury, amountToMint);
totalSupply.supplyShares -= accruedToTreasuryShares;
```

The total supply shares is decreased in `executeMintToTreasury()` but not increased within `_accrueToTreasury()`

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

As a result of the incorrect accounting, the sum of each position’s supply shares will be greater than `totalSupply.supplyShares`

This means that `amountToMint` worth of shares will never be withdrawable by users, so they will race to exit the vault.

This leaves the last user with unredeemable shares, so they lose their funds.

### PoC

_No response_

### Mitigation

Within `_accrueToTreasury()`, increment the total supply by the amount of shares accrued to the treasury.