Acrobatic Rainbow Grasshopper

High

# Withdrawals might be DoS in certain cases and funds can get stuck

## Summary
Withdrawals might be DoS in certain cases and funds can get stuck
## Vulnerability Detail
Upon operations in the pool, we accrue shares to the treasury using `ReserveLogic::_accrueToTreasury()`. This happens if the reserve factor is not 0 and if debt has been accrued. Then, this is the only state change that occurs regarding this operation:
```solidity
if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
```
Then, upon withdrawals, this is the function being called for the treasury to receive those funds:
```solidity
function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);


      IERC20(asset).safeTransfer(treasury, amountToMint);

      totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```
First, we convert the shares into an amount, transfer those funds to the treasury and subtract the shares from the total supply. Imagine the following scenario:
1. Bob has 100 supply shares and he is the only user who has supplied
2. Some borrowing happens, debt accrues, etc. which makes it so the treasury has funds to receive, let's say 10 shares
3. Bob tries to withdraw his 100 supply shares, the total supply gets to 0 as he is the only supplier
4. The function above gets called and we deduct 10 from 0 leading to an underflow revert
5. Bob can't withdraw all of his shares due to that

This happens because of multiple factors, the sponsor would have to determine what's the best fix but one of the cores of this issue is the fact that the supply is not being increased upon the accrual to the treasury.

Paste the following POC into `PoolBorrowTests.t.sol` (also import `StdError` from Foundry to catch the underflow revert):
```solidity
    function testWithdrawReverts() public {
    bytes32 pos = keccak256(abi.encodePacked(alice, 'index', uint256(0)));
    uint256 mintAmount = 1000 ether;
    uint256 borrowAmount = 10 ether;
    poolFactory.setReserveFactor(1000);
    _mintAndApprove(alice, tokenA, mintAmount, address(pool));
    _mintAndApprove(alice, tokenB, mintAmount, address(pool));
    _mintAndApprove(bob, tokenB, borrowAmount, address(pool));

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, borrowAmount, 0);
    vm.stopPrank();

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, mintAmount, 0);

    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);

    vm.warp(block.timestamp + 1 days);
    pool.repaySimple(address(tokenB), type(uint256).max, 0);
    vm.stopPrank();

    uint256 userSupplies = pool.getBalance(address(tokenB), bob, 0);
    uint256 totalSupply = pool.totalAssets(address(tokenB));

    assertEq(userSupplies, totalSupply); // All shares belong to Bob
    
    vm.startPrank(bob);
    vm.expectRevert(stdError.arithmeticError);
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);
  }
```
## Impact
Withdrawals might be DoS in certain cases and funds can get stuck
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213
## Tool used

Manual Review

## Recommendation
I believe increasing the supply upon accruing to the treasury would solve this issue but it could create some weird edge cases to consider