Stale Tweed Seal

High

# Incorrect accounting when executing mint to treasury in withdrawals

### Summary

`PoolLogic@executeMintToTreasury` incorrectly substract the accrued treasury shares from total supply shares when they were never added to it in the first place ultimately causing reverts in the withdrawals due to an arithmetic underflow.

### Root Cause

* Treasury shares are accrued after flashloans are repaid or when debt is accrued and the reserve factor is non zero, but the total supply shares are not increased :

[FlashLoanLogic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L123)
```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128(); // <==== Audit

    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```

[ReserveLogic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213)
```solidity
  function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;

    // calculate the total variable debt at moment of the last interaction
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);

    // calculate the new total variable debt after accumulation of the interest on the index
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);

    // debt accrued is the sum of the current debt minus the sum of the debt at the last update
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

    if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128(); // <==== Audit
  }
```

* Withdrawals trigger the distribution of those accrued treasury share to the treasury after the withdrawal is executed :

[PoolSetters](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L62-L87)
```solidity
  function _withdraw(
    address asset,
    address to,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
  ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    require(to != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);

    DataTypes.ExecuteWithdrawParams memory params = DataTypes.ExecuteWithdrawParams({
      reserveFactor: _factory.reserveFactor(),
      asset: asset,
      amount: amount,
      position: pos,
      destination: to,
      data: data,
      pool: address(this)
    });

    if (address(_hook) != address(0)) _hook.beforeWithdraw(params);

    res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);
    PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);  // <==== Audit

    if (address(_hook) != address(0)) _hook.afterWithdraw(params);
  }
```

* After the accrued treasury shares are sent, they are subtracted from the total supply shares which can cause a revert due an underflow if there are not enough supply shares left since this happens after the withrawal is executed :

[PoolLogic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103)
```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;  // <==== Audit

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```

* Other withdrawals can revert too for the same reason if an accrued treasury shares distribution was successful before since it would have tapped into the total supply shares coming from other suppliers :

[PositionBalanceConfiguration](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L85-L96)
```solidity
  function withdrawCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares -= sharesBurnt;
    supply.supplyShares -= sharesBurnt;  // <==== Audit
  }
```

### Internal pre-conditions

Reserve factor is not zero or Flash Loan premium is not 0

### External pre-conditions

N/A

### Attack Path

This can be triggered intentionally by a malicious user but will also happen naturally through regular usage :

1. Bob supplies `tokenB`
2. Either Alice supplies `tokenA` and borrows then repays `tokenB` or Alice takes a FlashLoan, both lead to treasury share accrual
3. Bob tries to fully withdraw `tokenB` but can't due to the arithmetic underflow.
4. Alternatively, there are other suppliers of `tokenB` and Bob can fully withdraw, making other suppliers not able to.

### Impact

As accrued treasury shares keep being incorrectly deducted from the total supply shares, the gap between the actual total supply shares and the stored total supply shares will keep growing ultimately leading to actors (.i.e Pools users, Curated Vaults and their users, NFT Positions users) not being able to withdraw their full collateral and potentially disrupting functionality where the withdrawn amount cannot be controlled like Curated Vaults reallocation for example.

### PoC

```solidity
  function testTreasuryAccruals() external {
    poolFactory.setReserveFactor(0.2e4);
    poolFactory.setFlashloanPremium(2);

    _mintAndApprove(alice, tokenA, 3000 ether, address(pool));
    _mintAndApprove(alice, tokenB, 1000 ether, address(pool));
    _mintAndApprove(bob, tokenB, 5000 ether, address(pool));

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, 500 ether, 0);

    skip(12);
    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, 1000 ether, 0);

    skip(12);
    oracleA.updateRoundTimestamp();
    oracleB.updateRoundTimestamp();
    pool.borrowSimple(address(tokenB), alice, 375 ether, 0);

    skip(12);
    pool.repaySimple(address(tokenB), type(uint256).max, 0);

    skip(12);
    bytes memory emptyParams;
    MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenB), 500 ether, emptyParams);

    skip(12);
    vm.startPrank(bob);
    vm.expectRevert(); // Will revert because their are not enough supply shares
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);

    skip(12);
    address otherSuppliers = makeAddr('otherSuppliers');
    _mintAndApprove(otherSuppliers, tokenB, 1000 ether, address(pool));

    vm.startPrank(otherSuppliers);
    pool.supplySimple(address(tokenB), otherSuppliers, 500 ether, 0);

    skip(12);
    vm.startPrank(bob);
    // Will not revert because are enough supply shares from other suppliers
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);

    skip(12);
    vm.startPrank(otherSuppliers);
    // Will revert at the withrawal stage because treasury shares distribution deducted from the total supply shares provided by the other suppliers
    vm.expectRevert();
    pool.withdrawSimple(address(tokenB), otherSuppliers, type(uint256).max, 0);

    vm.stopPrank();
  }
```

### Mitigation

Possible solutions would be to increase the total supply shares when accruing treasury shares or remove the deduction in  `executeMintToTreasury`.