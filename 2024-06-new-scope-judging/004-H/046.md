Loud Rosewood Oyster

Medium

# Failure to Adjust Reserve `UnderlyingBalance` After Treasury Withdrawal Leads to Inaccuracy

## Summary
The underlying reserve asset balance is incorrectly overstated because the balance isn't decremented after transferring accrued treasury shares during withdrawals, leading to an inaccurate pool reserve balance.
## Vulnerability Detail
during withdrawals from the Pool, [PoolLogic::executeMintToTreasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) is called to transfer the assets worth of the accrued treasury shares to the treasury.
In [executeMintToTreasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) function, after the transfer of the assets worth of the treasury shares, the underlying balance of the asset isn't decremented by the transferred amount.

+ To Illustrate:

Assuming a reserve asset has an underlying balance of 1,000, after some borrowing and accrual of interest from the borrowers the new balance becomes 1,100.
The interest earned = 1100 - 1000 ==> 100
Just for simplification, if the reserve factor is 20% and assuming the total shares in this reserve asset have been deposited by one user, with a reserve factor of 20%, the treasury asset share, will be: 100 * 20% ==> 20

If the user withdraws all of his asset shares, the new underlying balance at [PoolLogic::executeMintToTreasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) will be:  1100 - 1080 = > 20
But after the transfer of the assets to the treasury the underlying balance isn't decremented by the transferred amount, this will thus make the reserve asset underlying balance always overstate the actual pool reserve asset balance.

## Impact
The underlying reserve asset balance will always overstate the actual pool reserve balance.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolSetters.sol#L62-L87
## Tool used

Manual Review

## Recommendation
Should decrement the reserve asset underlying balance by the amount sent to the treasury, i.e. `amountToMint`