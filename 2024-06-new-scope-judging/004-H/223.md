Magnificent Scarlet Dove

High

# executeMintToTreasury incorrectly deducts the treasury shares from totalSupply reserve

## Summary
ExecuteMintToTreasury incorrectly deducts the treasury shares from totalSupply reserve because those shares were never added to the  totalSupply mapping for a reserve. 
## Vulnerability Detail
Whenever assets are deposited or withdrawn from a particular reserve of a pool firstly  updateState is called which is as follows 
```solidity
 function updateState(DataTypes.ReserveData storage self, uint256 _reserveFactor, DataTypes.ReserveCache memory _cache) internal {
    // If time didn't pass since last stored timestamp, skip state update
    if (self.lastUpdateTimestamp == uint40(block.timestamp)) return;

    _updateIndexes(self, _cache);
    _accrueToTreasury(_reserveFactor, self, _cache);

    self.lastUpdateTimestamp = uint40(block.timestamp);
  }
```
Then accrueToTreasury function is called which is as follows
```solidity
function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;

    // calculate the total variable debt at moment of the last interaction
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);

    // calculate the new total variable debt after accumulation of the interest on the index
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);

    // debt accrued is the sum of the current debt minus the sum of the debt at the last update
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

    if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
  }
```
As can be seen from above only accrued to treasury shares are increased for a reserve these shares are not added to the totalSupply shares of a reserve.
Now issue is when withdraw function is called for a particular reserve by any position holder then following function is called 
```solidity
function _withdraw(
    address asset,
    address to,
    uint256 amount,
    bytes32 pos,
    DataTypes.ExtraData memory data
  ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
    require(to != address(0), PoolErrorsLib.ZERO_ADDRESS_NOT_VALID);

    DataTypes.ExecuteWithdrawParams memory params = DataTypes.ExecuteWithdrawParams({
      reserveFactor: _factory.reserveFactor(),
      asset: asset,
      amount: amount,
      position: pos,
      destination: to,
      data: data,
      pool: address(this)
    });

    if (address(_hook) != address(0)) _hook.beforeWithdraw(params);

    res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);
    PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);

    if (address(_hook) != address(0)) _hook.afterWithdraw(params);
  }
```
As can be seen that at the end  execute mint to treasury is called which is as follows
```solidity
 function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
      totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```
Issue is in the following line
```solidity
 totalSupply.supplyShares -= accruedToTreasuryShares;
```
This is a issue because shares accrued to treasury were never added to the totalSupply of a reserve when updateState function was called so no need of deducting them from totalSupply.supplyshares. This will cause incorrect accounting of the supply shares and will lead to underflow.
## Impact
Incorrect Accounting of supply shares of a reserve and will lead to underflow ultimately.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99
## Tool used

Manual Review

## Recommendation
Exclude the following line 
```solidity
 totalSupply.supplyShares -= accruedToTreasuryShares;
```