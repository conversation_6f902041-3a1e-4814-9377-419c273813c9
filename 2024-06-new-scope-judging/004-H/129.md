Careful Fleece Pike

High

# Incorrect deduction of `accruedToTreasuryShares` from `totalSupply.supplyShares` in `executeMintToTreasury` will cause broken accounting, insolvency

### Summary

Incorrect deduction of `accruedToTreasuryShares` from `totalSupply.supplyShares` in `executeMintToTreasury` will cause broken accounting, insolvency.

### Root Cause

Incorrect deduction of `accruedToTreasuryShares` from `totalSupply.supplyShares` in `executeMintToTreasury`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
>>    totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```

### Internal pre-conditions
In `PoolFactory`, the admin set:
1. `reserveFactor > 0` OR
2. `flashLoanPremiumToProtocol > 0`

### External pre-conditions

_No response_

### Attack Path

When `reserveFactor` is greater than zero, the protocol will take a cut of the repaid interest, which is converted to shares and stored in `accruedToTreasuryShares`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213

```solidity
  function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;

    // calculate the total variable debt at moment of the last interaction
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);

    // calculate the new total variable debt after accumulation of the interest on the index
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);

    // debt accrued is the sum of the current debt minus the sum of the debt at the last update
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

>>  if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
  }
```

When `flashLoanPremiumToProtocol` is greater than zero, the protocol will charge a flash loan fee, then the fee is converted to shares

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116

```solidity
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

>>  _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();

    _reserve.updateInterestRates(_totalSupplies, cache, _params.asset, IPool(_params.pool).getReserveFactor(), amountPlusPremium, 0, '', '');

    IERC20(_params.asset).safeTransferFrom(_params.receiverAddress, address(_params.pool), amountPlusPremium);

    emit PoolEventsLib.FlashLoan(_params.receiverAddress, msg.sender, _params.asset, _params.amount, _params.totalPremium);
  }
```


Note that, the shares of the protocol are not included in `_totalSupplies[asset].supplyShares`, but when a user withdraws from a pool, the `accruedToTreasuryShares` is deducted from `_totalSupplies[asset].supplyShares`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolSetters.sol#L84

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99

### Impact

- Broken accounting. `_totalSupplies[asset].supplyShares` will store the value that is less than the total of supply shares.
- Insolvency. All the users can not withdraw, because there are not enough shares in `_totalSupplies[asset].supplyShares` for all the users. There will be an underflow revert when:
   - Deducting the shares of a user from `_totalSupplies[asset].supplyShares` OR
   - Deducting the shares of the protocol from `_totalSupplies[asset].supplyShares` OR
- The only way for the protocol to mitigate is setting `reserveFactor` and `flashLoanPremiumToProtocol` to zero, but now the protocol can not accrue revenue at all.

### PoC

Add an underflow check in `PositionBalanceConfiguration.sol:L95` for debugging

```diff
library PositionBalanceConfiguration {
  ...
  function withdrawCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares -= sharesBurnt;
+   require(supply.supplyShares >= sharesBurnt, "[audit] underflow when deducting user's shares");
    supply.supplyShares -= sharesBurnt;
  }
  ...
}
```

Add an underflow check in `PoolLogic.sol:L99` for debugging

```diff
library PoolLogic {
  ...
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
+     require(totalSupply.supplyShares >= accruedToTreasuryShares, "[audit] underflow when deducting protocol's shares");
      totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
  ...
}
```

Run command: `forge test --match-path test/PoC/PoC.t.sol`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {PoolSetup} from 'test/forge/core/pool/PoolSetup.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is PoolSetup {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  function setUp() public {
    _setUpPool();
  }

  function testPoC() external {
    poolFactory.setReserveFactor(0.1e4);

    uint256 mintAmount = 10 ether;
    uint256 borrowAmount = 1 ether;

    _mintAndApprove(alice, tokenA, mintAmount, address(pool));
    _mintAndApprove(alice, tokenB, mintAmount, address(pool));

    _mintAndApprove(bob, tokenB, mintAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenB), bob, mintAmount, 0);

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, mintAmount, 0);
    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);
    vm.stopPrank();

    skip(1 days);

    pool.forceUpdateReserves();

    console.log("Total supplyShares: %e", pool.getTotalSupplyRaw(address(tokenB)).supplyShares);
    console.log("Bob's supplyShares: %e", pool.getBalanceRaw(address(tokenB), bob, 0).supplyShares);
    console.log("accruedToTreasuryShares: %e", pool.getReserveData(address(tokenB)).accruedToTreasuryShares);

    vm.prank(alice);
    pool.repaySimple(address(tokenB), type(uint256).max, 0);

    vm.startPrank(bob);
    vm.expectRevert("[audit] underflow when deducting protocol's shares");
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);

    pool.withdrawSimple(address(tokenB), bob, 9 ether, 0);
    vm.expectRevert("[audit] underflow when deducting user's shares");
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);

    vm.stopPrank();
  }
}
```

Logs:

```bash
  Total supplyShares: 1e19
  Bob's supplyShares: 1e19
  accruedToTreasuryShares: 4.080511283376e12
```

`accruedToTreasuryShares` is not included in total supplyShares.

### Mitigation

Remove the deduction of `accruedToTreasuryShares` from `totalSupply.supplyShares`

```diff
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
-     totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```