Loud Rosewood Oyster

High

# Flawed Treasury Share Handling Could DoS Some Users from Withdrawing

## Summary
When withdrawing the underlying asset worth treasury shares to the treasury, the shares are wrongly deducted from the supply shares, thus DoSing some suppliers from being able to withdraw their shares due to an underflow revert in [PositionBalanceConfiguration::withdrawCollateral](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L85-L96) since supply shares will be less than the total user shares.
## Vulnerability Detail
during withdrawals from the Pool, [PoolLogic::executeMintToTreasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) is called to transfer the assets worth of the accrued treasury shares to the treasury.
In executeMintToTreasury function, after the transfer of the assets worth of the treasury shares, the function attempts to deduct the accruedToTreasuryShares from the reserve total supply shares, when these shares were never added to the supply shares.

+ To Illustrate:

Assuming a reserve asset has a debt share of 1,000 at a borrow index of 1.1, and a current index of 1.2, the total debt interest will be:
1,000 * 1.2 - 1,000 * 1.1 => 1200 - 1,000
= 100

if the reserve factor is 20%, the treasury share will be: 100 * 20% ==> 20
converting to shares will now be: 20 * 1.2 ==> 24
accruedToTreasuryShares += 24

the rest of the interest is then fairly shared to the suppliers based on shares, by updating the liquidity index.
Note that the reserve asset supply shares are never updated here, the supply shares only store the shares deposited by the pool suppliers.
In [ValidationLogic::validateSupply](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ValidationLogic.sol#L70-L89), we can also observe that the total supply shares are computed as the reserve supply shares plus the accrued treasury shares:
```solidity
    require(
      supplyCap == 0
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
```
In [_handleFlashLoanRepayment](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106-L123), we can also observe that the premium shares are only added to `accruedToTreasuryShares`
```solidity
_reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
``` 
## Impact
This will make supply shares less than the total user shares, which will completely block some users from being able to withdraw their deposited assets
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/PoolSetters.sol#L62-L87
## Tool used

Manual Review

## Recommendation
Remove this line from [PoolLogic::executeMintToTreasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103):

```diff
-  totalSupply.supplyShares -= accruedToTreasuryShares;
```