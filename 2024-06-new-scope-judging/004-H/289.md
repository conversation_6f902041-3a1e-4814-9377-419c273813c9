Smooth Carbon Narwhal

High

# Users are unable to withdraw their funds due to the incorrect handling of treasury shares

## Summary
`Borrowers` are required to pay `interest` to the `protocol`, a portion of which is allocated as `fees` to the `treasury`. 
These `fees` are converted into `shares` based on the current `liquidity index`. 
However, the issue arises because these `treasury shares` are not included in the `pool`’s `total shares` when they are created, but they are deducted from the `total shares` when transferred to the `treasury`. 
This results in the `total pool shares` becoming less than the sum of all `depositors`' `shares`.
## Vulnerability Detail
When users `deposit` `assets` into the `pool`, they receive `shares` calculated based on the current `liquidity index`, and the `total pool shares` increase. 
```solidity
function depositCollateral(
  DataTypes.PositionBalance storage self,
  DataTypes.ReserveSupplies storage totalSupply,
  uint256 amount,
  uint128 index
) internal returns (bool isFirst, uint256 sharesMinted) {
  sharesMinted = amount.rayDiv(index);
  require(sharesMinted != 0, PoolErrorsLib.INVALID_MINT_AMOUNT);
  isFirst = self.supplyShares == 0;
  self.lastSupplyLiquidtyIndex = index;
  self.supplyShares += sharesMinted;         // @audit, here
  totalSupply.supplyShares += sharesMinted;  // @audit, here
}
```
`Borrowers` pay `interest` to the `protocol`, reflected as an increase in the `borrow index`. 
The `fees` owed to the `treasury` are managed in the `_accrueToTreasury` function, but these `shares` are not added to the `total pool shares`. 
```solidity
function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
  vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);
  vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);
  vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

  vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);
  if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128(); // @audit, here
}
```
Later, when the `fees` are transferred to the `treasury` in the `executeMintToTreasury` function, they are deducted from the `total pool shares`. 
```solidity
function executeMintToTreasury(
  DataTypes.ReserveSupplies storage totalSupply,
  mapping(address => DataTypes.ReserveData) storage reservesData,
  address treasury,
  address asset
) external {
  DataTypes.ReserveData storage reserve = reservesData[asset];
  uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

  if (accruedToTreasuryShares != 0) {
    reserve.accruedToTreasuryShares = 0;
    uint256 normalizedIncome = reserve.getNormalizedIncome();
    uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

    IERC20(asset).safeTransfer(treasury, amountToMint);
    totalSupply.supplyShares -= accruedToTreasuryShares;  // @audit, here
  }
}
```
This causes the `total pool shares` to become less than the sum of `shares` of all `depositors`.

(We also didn't update the `interest rate` and track changes to the `underlying balance` here.
Not sure this is intended.)
## Impact
- **Deposit Withdrawals Blocked**: `Depositors` may be unable to `withdraw` their full funds. 
For instance, if the total sum of `depositor shares` is `300` but the `total pool shares` drop to `270` due to `treasury fee` deductions, the last `depositors` could not `withdraw` the `30 shares` they are owed.
    
- **Incorrect Calculations in Other Functions**: Parts of the system that rely on the `total pool shares` will function incorrectly. 
For example, in the `curated vault`, the `withdrawable` amount from the `pool` is miscalculated due to the incorrect `total shares`.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L48-L49
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L212
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99
## Tool used

Manual Review

## Recommendation
```solidity
function executeMintToTreasury(
  DataTypes.ReserveSupplies storage totalSupply,
  mapping(address => DataTypes.ReserveData) storage reservesData,
  address treasury,
  address asset
) external {
  DataTypes.ReserveData storage reserve = reservesData[asset];
  uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

  if (accruedToTreasuryShares != 0) {
    reserve.accruedToTreasuryShares = 0;
    uint256 normalizedIncome = reserve.getNormalizedIncome();
    uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

    IERC20(asset).safeTransfer(treasury, amountToMint);
    
-    totalSupply.supplyShares -= accruedToTreasuryShares;  

  }
}
```