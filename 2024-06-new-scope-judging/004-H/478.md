Atomic Currant Mule

High

# Missing fee shares update to totalSupplies.supplyShares will cause inconsistent state of protocol

### Summary

The reserve state is updated before each pool action (supply/borrow/repay/liquidate) execute.  Fee accuruing is performed in _accrueToTreasury() during state change in updateState() ([ReserveLogic.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87-L94)). However, the fee share accrued is only recoreded in _reserve.accruedToTreasuryShares variable, not added to the totalSupplies.supplyShares.

### Root Cause

In ReserveLogic.sol there is a missing update for fee shares to totalSupplies.supplyShares. While the fee is  tranferred to treasury during withdraw process, the fee shares is removed from total supplyShares. This will cause  inconsistent  state of variable supplyShares.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The last withdrawing users may not able to get back theis assects since the totalShares could be reduced to 0 before they can execute withdrawing.

### PoC

_No response_

### Mitigation

Consider add the feeShares to updateState function in ReserveLogic.sol,
```solidity
function updateState(...){
...
_accrueToTreasury(_reserveFactor, self, _cache);
+ totalSupplies.supplyShares = totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares);
+ reserve.accruedToTreasureyShare = 0;
self.lastUpdateTimestamp = uint40(block.timestamp);
}
```
and change the following lines in validateSupply function in validationLogic.sol
```solidity
function validateSupply(){
...
-     require(
      supplyCap == 0
        || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
+     require(
      supplyCap == 0
        || (totalSupplies.supplyShares.rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );

}
```