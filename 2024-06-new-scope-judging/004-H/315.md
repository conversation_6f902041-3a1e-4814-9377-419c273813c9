Clever Ebony Halibut

High

# Incorrect Accounting of Treasury Shares Leads to Loss of Funds and Protocol Insolvency

## Summary

The protocol suffers from an accounting mismatch in handling treasury shares. While accrued shares are added to `accruedToTreasuryShares`, they are not included in `totalSupply.supplyShares`. However, during withdrawals, these shares are subtracted from `totalSupply.supplyShares`, leading to an artificial reduction of the total supply. This discrepancy compounds over time, eventually causing withdrawal failures due to underflow errors, resulting in fund loss and insolvency of the protocol.

### Vulnerability Details

- The vulnerability stems from an accounting mismatch in the handling of treasury shares within the pool contract. This issue is rooted in the interaction between several key functions and processes:

1. **State Update Process**: In each action within the poolContract, the state is updated by calling the `updateState()` function in ReserveLogic. This function, in turn, calls `_accrueToTreasury()`.

```js
    function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;
    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);
    if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
}
```

This function calculates the amount to accrue to the treasury based on the debt accrued and the reserve factor. Crucially, it only adds the accrued shares to `_reserve.accruedToTreasuryShares`, but does not add them to `totalSupply.supplyShares`.

2. **Flash Loan Premium Fees**: the same with flash loan premium fees. These fees are added to the treasury but not to the total supply, further exacerbating the discrepancy.

```js
   function _handleFlashLoanRepayment(/*params*/) internal {
    uint256 amountPlusPremium = _params.amount + _params.totalPremium;

    DataTypes.ReserveCache memory cache = _reserve.cache(_totalSupplies);
    _reserve.updateState(_params.reserveFactor, cache);

 >> _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cachenextLiquidityIndex).toUint128();
 // more code ..
  }
```

3. **Withdrawal Process**: The critical issue manifests in the `_withdraw` function in the **PoolSetters** contract. After each withdrawal, the `executeMintToTreasury` function in **PoolLogic.sol** is called.

```js
  function _withdraw(/*pramas*/ ) internal nonReentrant(RentrancyKind.LENDING) returns (DataTypes.SharesType memory res) {
   // some code ..
    res = SupplyLogic.executeWithdraw(_reserves, _reservesList, _usersConfig[pos], _balances, _totalSupplies[asset], params);
 >> PoolLogic.executeMintToTreasury(_totalSupplies[asset], _reserves, _factory.treasury(), asset);
  }
```

This function transfers tokens to the treasury and subtracts the `accruedToTreasuryShares` from `totalSupply.supplyShares`. However, these shares were never added to `totalSupply.supplyShares` in the first place.

```js
 function executeMintToTreasury(/*prams*/) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];
    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;
    if (accruedToTreasuryShares != 0) {
        reserve.accruedToTreasuryShares = 0;
        uint256 normalizedIncome = reserve.getNormalizedIncome();
        uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);
        IERC20(asset).safeTransfer(treasury, amountToMint);
  >>    totalSupply.supplyShares -= accruedToTreasuryShares;
        emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
}
```

- The consequence of this mismatch is that over time, the `totalSupply.supplyShares` will be artificially reduced, while the actual sum of user shares remains unchanged. This leads to a situation where the total of user shares exceeds the `totalSupply.supplyShares`.

### Impact

- As users attempt to withdraw their funds, their shares are subtracted from the already diminished `totalSupply.supplyShares`. Eventually, this will lead to an underflow error, causing withdrawal transactions to revert and effectively locking user funds in the contract.

- This vulnerability compounds with each accrual to the treasury and subsequent minting operation, progressively increasing the loss of users assets.

- one other impact is that interestRate will be calculated based on the wrong `totalSupply.supplyShares`.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L92
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99

## Tool used

Manual Review

## Recommendation

To address this issue, we recommend the following changes:

```diff
// In _accrueToTreasury function
function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    // ... existing code ...
    if (vars.amountToMint != 0) {
        uint256 sharesToMint = vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
        _reserve.accruedToTreasuryShares += sharesToMint;
+       totalSupply.supplyShares += sharesToMint;
    }
}

// In _handleFlashLoanRepayment function
  function _handleFlashLoanRepayment(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage _totalSupplies,
    DataTypes.FlashLoanRepaymentParams memory _params
  ) internal {
  // existing code ..
-    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
+ uint256 sharesToMint = params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
+ _reserve.accruedToTreasuryShares += sharesToMint;
+   totalSupply.supplyShares += sharesToMint;;
    // existing code ..
  }
```