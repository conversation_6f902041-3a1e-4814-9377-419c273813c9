Ancient Gingerbread Rooster

Medium

# Incorrect deduction of `accruedToTreasuryShares` from totalSupply, causing loss of shares

## Summary
In `executeMintToTreasury`, `accruedToTreasuryShares` is set to zero and also deducted from `totalSupply.supplyShares`. This is wrong because it was never added to `totalSupply.supplyShares`.

## Vulnerability Detail
ZeroLend One accumulates `accruedToTreasuryShares` from supplies to cover pool operations. It grows whenever a supply/lend operation is executed towards the pool reserve.
Important thing here is that `accruedToTreasuryShares` only increase by itself and never added to `totalSupply.supplyShares` of the pool.

```solidity
  function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;

    // calculate the total variable debt at moment of the last interaction
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);

    // calculate the new total variable debt after accumulation of the interest on the index
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);

    // debt accrued is the sum of the current debt minus the sum of the debt at the last update
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

>   if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
  }
```

This is evident with the fact that `ValidationLogic.validateSupply` function uses the sum of `totalSupply.supplyShares + reserve.accruedToTreasuryShares` to check the cap.
```solidity
  function validateSupply(
    DataTypes.ReserveCache memory cache,
    DataTypes.ReserveData storage reserve,
    DataTypes.ExecuteSupplyParams memory params,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal view {
    require(params.amount != 0, PoolErrorsLib.INVALID_AMOUNT);

    (bool isFrozen,) = cache.reserveConfiguration.getFlags();
    require(!isFrozen, PoolErrorsLib.RESERVE_FROZEN);

    uint256 supplyCap = cache.reserveConfiguration.getSupplyCap();

    require(
      supplyCap == 0
>       || ((totalSupplies.supplyShares + uint256(reserve.accruedToTreasuryShares)).rayMul(cache.nextLiquidityIndex) + params.amount)
          <= supplyCap * (10 ** cache.reserveConfiguration.getDecimals()),
      PoolErrorsLib.SUPPLY_CAP_EXCEEDED
    );
  }
```

That's why it shouldn't be deducted from `totalSupply.supplyShares` when `executeMintToTreasury` is called. It'd be a double deduction of shares and potentially could fail due to lack of totalSupply value.
```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
>     totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```

## Impact
It'd be a double deduction of shares and potentially could fail due to lack of totalSupply value.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103

## Tool used
Manual Review

## Recommendation
It's enough just to reset `reserve.accruedToTreasuryShares` to 0, not deducting it from `totalSupply.supplyShares`.

```diff
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
-     totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```