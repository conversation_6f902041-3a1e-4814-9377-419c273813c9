Curly Pineapple Armadillo

High

# Treasury fee shares are removed from `totalSupply.supplyShares`, causing withdrawals to fail

### Summary

Treasury fee shares are removed from `totalSupply.supplyShares` in `executeMintToTreasury` even though when they are accumulated they are never included in the `supplyShares`. This will cause users to be unable to withdraw all of their supplied assets as when fees are transferred to the treasury, `totalSupply.supplyShares` will become less than the total shares minted to suppliers.

### Root Cause

- In [`executeMintToTreasury`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99) `accruedToTreasuryShares` are removed from `totalSupply.supplyShares` even though when they are accumulated they are never included in the `supplyShares`.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. One user supplies 1 ETH, receiving 1e18 of shares.
2. Interest accrues and the user can withdraw 1.1 ETH for the 1e18 of shares, while the treasury has been minted 0.01e18 of shares.
3. User tries to redeem all of his shares but the call reverts. This happens as `totalSupply.supplyShares` is only 1e18, so when the withdraw is executed the value is reduced to 0, after that, when `executeMintToTreasury` is called, `totalSupply.supplyShares` is attempted to be decreased by 0.01e18, which will cause the revert (0 - 0.01e18).

### Impact

Users will be unable to withdraw their assets, causing a loss of funds.

### PoC

_No response_

### Mitigation

Do not decrease `totalSupply.supplyShares` by `accruedToTreasuryShares` as the treasury fees are never actually included in the total supply.