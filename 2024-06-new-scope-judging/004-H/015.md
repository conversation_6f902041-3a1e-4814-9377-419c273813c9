Great Jade Shetland

High

# Total shares is wrongly subtracted when minting to treasury, blocking users from withdrawing their funds

### Summary

When users borrow and repay debts, some interest is accumulated in the pools, this interest will be distributed to suppliers, and part of this profit/interest will be minted/transferred to the protocol's treasury. If the reserve factor is set to a value >0, interest will be accrued to the treasury whenever `ReserveLogic::updateState` (which is called in almost every action).

In `ReserveLogic::_accrueToTreasury`, the accrue happens in the following:
```solidity
if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
```
As shown, only `accruedToTreasuryShares` is being incremented, without touching the reserve shares total supply.

However, in `PoolLogic::executeMintToTreasury`, which is called on every withdrawal action, is where the protocol sends the treasury its profit, in the following:
```solidity
uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

if (accruedToTreasuryShares != 0) {
  ...

  IERC20(asset).safeTransfer(treasury, amountToMint);
  totalSupply.supplyShares -= accruedToTreasuryShares;

  ...
}
```
As shown, the  `accruedToTreasuryShares` is being subtracted from the reserve's total supply, but **it was never added in the first place**. The will get to a point where the shares distributed among users are > `totalSupply.supplyShares`.

So when users try to withdraw their funds, the TX will revert with an underflow error in https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L94.


### Root Cause

Treasury accrued shares are subtracted from the total shares when minting profit to treasury [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99), but they are not being added to it when accruing [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L212).

### Impact

Users won't be able to withdraw their funds, i.e. funds will be stuck forever.

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract Contest_Pool is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    poolFactory.setConfigurator(address(new PoolConfigurator(address(poolFactory))));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      ltv: 0,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _syncOracles();
  }

  function testInvalidSharesSubtraction_DOS() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    {
      USDC.mint(bob, USDCamount);
      USDC.mint(alice, USDCamount);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      USDC.approve(address(pool), type(uint256).max);
    }

    // Bob deposits 1k USDC
    vm.prank(bob);
    pool.supply(address(USDC), bob, USDCamount, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));

    // Alice supplies 1 WETH
    // Alice borrows 1k USDC
    vm.startPrank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);
    pool.borrowSimple(address(USDC), alice, USDCamount, 0);
    vm.stopPrank();

    // Some time passes
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserve(address(USDC));

    // Some interest is accumulated
    uint256 aliceDebt = pool.getDebt(address(USDC), alice, 0);
    assertGt(aliceDebt, USDCamount);

    // Alice repays her debt
    vm.prank(alice);
    pool.repaySimple(address(USDC), aliceDebt, 0);

    // The pool has enough USDC to allow Bob to withdraw all his funds
    uint256 bobBalance = pool.getBalance(address(USDC), bob, 0);
    assertLt(bobBalance, USDC.balanceOf(address(pool)));

    // Bob tries to withdraw all his funds, reverts with underflow
    vm.prank(bob);
    vm.expectRevert(stdError.arithmeticError);
    pool.withdraw(address(USDC), bob, bobBalance, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));
  }
}
```
</details>

### Mitigation

Remove the following line from `PoolLogic::executeMintToTreasury`:
```solidity
totalSupply.supplyShares -= accruedToTreasuryShares;
```