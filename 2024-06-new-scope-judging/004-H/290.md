Big Admiral Dove

Medium

# Unexpected decrease of reserve total supply may prevent full withdrawal of a position

## Summary

In the `PoolLogic::executeMintToTreasury()` function, there is a supply shares deduction by `reserve.accruedToTreasuryShares` which is an unintended behavior.

So this unexpected decrease will prevent full withdrawal of a position with underflow revert.

## Vulnerability Detail

When updating a reserve state, the pool accrues a certain portion(reserve factor) of income to the reserve treasury.

```solidity
  function updateState(DataTypes.ReserveData storage self, uint256 _reserveFactor, DataTypes.ReserveCache memory _cache) internal {
    ... ...
    _accrueToTreasury(_reserveFactor, self, _cache);
    ... ...
  }

  function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    if (reserveFactor == 0) return;
    AccrueToTreasuryLocalVars memory vars;

    // calculate the total variable debt at moment of the last interaction
    vars.prevtotalDebt = _cache.currDebtShares.rayMul(_cache.currBorrowIndex);

    // calculate the new total variable debt after accumulation of the interest on the index
    vars.currtotalDebt = _cache.currDebtShares.rayMul(_cache.nextBorrowIndex);

    // debt accrued is the sum of the current debt minus the sum of the debt at the last update
    vars.totalDebtAccrued = vars.currtotalDebt - vars.prevtotalDebt;

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

    if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
  }
```

The `_reserve.accruedToTreasuryShares` value is actually independent to reserve total supply shares. But in the `PoolLogic::executeMintToTreasury()` function which is called by withdrawing, `reserve.supplyShares` is unreasonably deducted by `reserve.accruedToTreasuryShares`.

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    ... ...

    if (accruedToTreasuryShares != 0) {
      ... ...
      totalSupply.supplyShares -= accruedToTreasuryShares;
      ... ...
    }
  }
```

Let's assume a following scenario:

1. A depositer supplies 100 assets to the pool.
2. Another user borrow a flash loan and immediately repays it with premium(e.g 0.2%) that is accrued to the pool treasury.
3. The original depositor tries to withdraw entire 100 assets and fails with underflow revert.

Then why does this happen?
If a depositor tries a full withdrawal, the reserve total supply becomes 0 by `SupplyLogic.executeWithdraw()`. After then, `PoolLogic.executeMintToTreasury()` is called to subtract the accrued shares from the reserve total supply which is 0 now.

### Proof-Of-Concept

Let's try a full withdrawal after doing flash loan operation:

```solidity
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import "../../../../lib/forge-std/src/console.sol";
import "../../../../lib/forge-std/src/StdError.sol";

import {DataTypes} from './../../../../contracts/core/pool/configuration/DataTypes.sol';
import {IPool} from './../../../../contracts/interfaces/pool/IPool.sol';
import {MockFlashLoanSimpleReceiver} from './../../../../contracts/mocks/MockSimpleFlashLoanReceiver.sol';
import {PoolEventsLib, PoolSetup} from './PoolSetup.sol';

contract PoolFullWithdrawWithTreasuryTests is PoolSetup {
  address alice = address(1);
  address bob = address(2);

  event Transfer(address indexed from, address indexed to, uint256 value);

  function setUp() public {
    _setUpPool();
  }

  function testFullWithdrawWithTreasury() public {
    bytes memory emptyParams;
    MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
    _generateFlashloanCondition();

    poolFactory.setFlashloanPremium(20);
    uint256 premium = poolFactory.flashLoanPremiumToProtocol();
    assertEq(premium, 20);

    vm.startPrank(alice);

    vm.expectEmit(true, true, true, true);
    emit PoolEventsLib.FlashLoan(address(mockFlashSimpleReceiver), alice, address(tokenA), 100 ether, (100 ether * premium) / 10_000);
    emit Transfer(address(0), address(mockFlashSimpleReceiver), (100 ether * premium) / 10_000);

    pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 100 ether, emptyParams);
    vm.stopPrank();

    vm.startPrank(bob);
    vm.expectRevert(stdError.arithmeticError); // Revert with underflow
    pool.withdrawSimple(address(tokenA), bob, 100 ether, 0);
    vm.stopPrank();
  }

  function _generateFlashloanCondition() internal {
    _mintAndApprove(bob, tokenA, 5000 ether, address(pool));

    vm.startPrank(bob);
    pool.supplySimple(address(tokenA), bob, 100 ether, 0);

    vm.stopPrank();
  }
}

```

Here are the logs:
```bash
$ forge test --match-test testFullWithdrawWithTreasury -vv
[⠒] Compiling...
[⠃] Compiling 1 files with Solc 0.8.19
[⠊] Solc 0.8.19 finished in 4.76s
Compiler run successful!

Ran 1 test for test/forge/core/pool/PoolFlashLoanPocTests.t.sol:PoolFullWithdrawWithTreasuryTests
[PASS] testFullWithdrawWithTreasury() (gas: 882291)
Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 3.37ms (967.40µs CPU time)

Ran 1 test suite in 9.94ms (3.37ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

## Impact

Depositors will be unable to withdraw their full assets and should leave `accruedToTreasuryShares` amount of their shares to the pool.

## Code Snippet

[pool/logic/PoolLogic.sol#L99](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99)

## Tool used

Manual Review

## Recommendation

I'd suggest removing the issued subtraction from the `executeMintToTreasury()` function:

```diff
  function executeMintToTreasury(
    ... ...
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
-     totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }

```
