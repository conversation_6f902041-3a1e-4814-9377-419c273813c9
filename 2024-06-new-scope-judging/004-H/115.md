Steep Aqua Yak

Medium

# Can not withdraw assets from Pool because of treasury accrued shares

## Summary
Each time the reserve state is updated, an amount of shares is accrued to treasury but the reserve total supply is not updated accordingly. As a result, withdrawals would get reverted because lack of reserve supply.

## Vulnerability Detail
When reserve state is updated and there is interest accrued, [there will be shares accrued to treasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L197-L213). The share amount is added to `_reserve.accruedToTreasuryShares` , without increasing total supply of the updated asset. 
And in every withdrawal action, [accrued share will be sent to treasury](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L84). 
[And the amount to be sent will be deducted from total supply](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L93-L100), which will get reverted due to underflow

This issue causes a depositor can not withdraw all his shares and funds get locked.

#### POC
Update the test file `/test/forge/core/pool/PoolBorrowTests.t.sol` as below:

```diff
diff --git a/zerolend-one/test/forge/core/pool/PoolBorrowTests.t.sol b/zerolend-one/test/forge/core/pool/PoolBorrowTests.t.sol
index 37a709f..604a17c 100644
--- a/zerolend-one/test/forge/core/pool/PoolBorrowTests.t.sol
+++ b/zerolend-one/test/forge/core/pool/PoolBorrowTests.t.sol
@@ -181,4 +181,25 @@ contract PoolBorrowTests is PoolSetup {
     DataTypes.ReserveData memory data = pool.getReserveData(address(tokenA));
     assertNotEq(data.accruedToTreasuryShares, 0);
   }
+
+  function test_treasuryShares() public {
+    _mintAndApprove(alice, tokenA, 4000 ether, address(pool));
+
+    // Set the reserve factor to 1000 bp (10%)
+    poolFactory.setReserveFactor(10_000);
+
+    // Alice supplies and borrows tokenA from the pool
+    vm.startPrank(alice);
+    pool.supplySimple(address(tokenA), alice, 2000 ether, 0);
+    pool.borrowSimple(address(tokenA), alice, 800 ether, 0);
+
+    skip(30 days); // skip to accrue interest
+
+    vm.startPrank(alice);
+    tokenA.approve(address(pool), UINT256_MAX);
+    pool.repaySimple(address(tokenA), UINT256_MAX, 0);
+
+    // revert due to underflow
+    vm.expectRevert(abi.encodeWithSignature("Panic(uint256)", 0x11));
+    pool.withdrawSimple(address(tokenA), alice, type(uint256).max, 0); // try to withdraw all supplied asset
+  }
 }
```

Run the test `test_treasuryShares` and console shows:
```bash
Ran 1 test for test/forge/core/pool/PoolBorrowTests.t.sol:PoolBorrowTests
[PASS] test_treasuryShares() (gas: 521348)
```

## Impact
Deposited funds get locked. The maximum locked funds would be approximately equals to total treasury accrued shares.
At least, the last withdrawal will fails.

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L87-L95

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L212

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L83-L84
## Tool used

Manual Review

## Recommendation
Update total supply every time interest accrued to treasury