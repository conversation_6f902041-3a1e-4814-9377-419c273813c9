Joyous Rainbow Shark

High

# Treasury shares are 'burnt' upon but never 'minted' resulting in suppliers not being able to withdraw all of their assets

### Summary

Treasury shares are a way of keeping track of treasury earnings which is a function of the interest paid on debt taken or the premium earned on flash loans. When they're 'minted' they're tracked in their own variable (separate from regular supplyShares), however when they're burnt they're subtracted from the regular reserve supplyShares which will create an underflow in the withdrawal call.

### Root Cause

Each time a pool's reserve state is updated, [`ReserveLogic::_accrueToTreasury()` is called](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L92).
This function incriments `_reserve.accruedToTreasuryShares` by the shares equiavalent of the assets taken as a fee:

```javascript
  function _accrueToTreasury(uint256 reserveFactor, DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    ... SKIP!...

    vars.amountToMint = vars.totalDebtAccrued.percentMul(reserveFactor);

    if (vars.amountToMint != 0) _reserve.accruedToTreasuryShares += vars.amountToMint.rayDiv(_cache.nextLiquidityIndex).toUint128();
  }
```

However, this function does not increment `supplyShares` in the ReserveSupply struct for this reserve asset. 

Note that `_reserve.accruedToTreasuryShares` can also [increment](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/FlashLoanLogic.sol#L116) in the `FlashLoanLogic::_handleFlashLoanRepayment()` function. Again, this accruel of treasury shares does not increment the `supplyShares` in the ReserveSupply struct for this reserve asset.

When any pool withdrawal is executed, [`PoolLogic::executeMintToTreasury()` is called](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolSetters.sol#L84). This [function decrements `totalSupply.supplyShares` by `reserve.accruedToTreasuryShares`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103) but as shown above they were never added. 

```javascript
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
@>    totalSupply.supplyShares -= accruedToTreasuryShares;

      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```

As a result there will be a supplier who will not be able to withdraw all of their supplyShares because the withdrawal flow `PositionBalanceConfiguration::withdrawCollateral()` is called which [decrements `supply.supplyShares`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L95) resulting in the underflow:

```javascript
  function withdrawCollateral(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastSupplyLiquidtyIndex = index;
    self.supplyShares -= sharesBurnt;
@>  supply.supplyShares -= sharesBurnt;
  }
```

### Internal pre-conditions

1. A Pool exists with a non-zero ReserveFactor
2. Interest accrues on the supplied assets due to borrowers
3. A withdrawal of any amount occurs

### External pre-conditions

_No response_

### Attack Path

1. A supplier deposits into a pool with a non-zero ReserveFactor
2. A borrower borrows all available liquidity
3. Some time passes, interest on the debt accrues
4. The borrower repays in full
5. The supplier attempts to withdraw their entire deposit + interest earned, but their withdrawal reverts

### Impact

- After any withdrawal the total number of supply shares on the `ReserveSupplies` struct for a given reserve asset will be less than the sum of all supplier's supplyShares. As a result, not all suppliers will be able to withdraw their balance and their funds are stuck in the protocol.


### PoC

The below coded POC implements the 'Attack Path' described above.

First, add this line to the `CorePoolTests::_setUpCorePool()` function to create a scenario with a non-zero reserve factor:

```diff
  function _setUpCorePool() internal {
    poolImplementation = new Pool();

    poolFactory = new PoolFactory(address(poolImplementation));
+   poolFactory.setReserveFactor(1e3); // 10%
    configurator = new PoolConfigurator(address(poolFactory));

    ... SKIP!...
  }
```

Then create a new file in /test/forge/core/pool and paste the below contents. Take careful note of the emitted events and the revert reasons as they show the revert is due to the described issue.

```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {console2} from 'forge-std/src/Test.sol';
import {PoolLiquidationTest} from './PoolLiquidationTests.t.sol';
import {PoolEventsLib, PoolSetup} from './PoolSetup.sol';

contract AuditTreasurySharesBurntButNotMinted is PoolLiquidationTest {
    event Transfer(address indexed from, address indexed to, uint256 value);

  function test_POC_TreasurySharesBurntButNotMinted() public {
    uint256 aliceMintAmount = 10_000e18;
    uint256 bobMintAmount = 10_000e18;
    uint256 supplyAmount = 1000e18;
    uint256 borrowAmount = 1000e18;

    _mintAndApprove(alice, tokenA, aliceMintAmount, address(pool));         // alice collateral
    _mintAndApprove(bob, tokenB, bobMintAmount, address(pool));             // bob supply
    _mintAndApprove(alice, tokenB, aliceMintAmount, address(pool));         // alice needs some funds to pay interest

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmount, 0); 
    vm.stopPrank();

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, aliceMintAmount, 0);  // alice collateral
    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);     // 100% utilization
    vm.stopPrank();

    vm.warp(block.timestamp + 60 days); // Time passes, interest accrues, treasury shares accrue
    pool.forceUpdateReserves();

    // Alice full repay
    vm.startPrank(alice);
    tokenB.approve(address(pool), type(uint256).max);
    pool.repaySimple(address(tokenB), type(uint256).max, 0);
    
    (uint256 supplyBalance, uint256 supplyShares, , uint256 debtShares) = pool.marketBalances(address(tokenB));
    assert(debtShares == 0); // All debt has been repaid

    bytes32 bobPos = keccak256(abi.encodePacked(bob, 'index', uint256(0)));
    assert(pool.supplyShares(address(tokenB), bobPos) + pool.getReserveData(address(tokenB)).accruedToTreasuryShares > supplyShares); // Bob's supplyShares + accruedToTreasuryShares exceeds the total shares
    
    // Bob withdraw
    vm.startPrank(bob);
    uint256 treasuryAssetsToClaim = pool.getReserveData(address(tokenB)).accruedToTreasuryShares * pool.getReserveData(address(tokenB)).liquidityIndex / 1e27;

    vm.expectEmit(true, true, true, true);
    emit PoolEventsLib.Withdraw(address(tokenB), bobPos, bob, supplyBalance); // Withdrawal is successful

    vm.expectEmit(true, true, true, false);
    emit Transfer(address(pool), address(pool.factory().treasury()), treasuryAssetsToClaim); // Transfer of treasury assets is successful

    vm.expectRevert(abi.encodeWithSignature("Panic(uint256)", 0x11));         // Revert is due to underflow when deducting shares
    
    pool.withdrawSimple(address(tokenB), bob, supplyBalance, 0);
  }

}
```

### Mitigation

Remove the decrement operation in [`executeMintToTreasury()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L83-L103). This should not affect the liquidityIndex as the `reserveFactor` was already accounted for in the calculation of the `liquidityRate` (however this is out of scope so I am not a position to say for sure how this will be implemented in production).

```diff
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];


    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;


    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);


      IERC20(asset).safeTransfer(treasury, amountToMint);
-     totalSupply.supplyShares -= accruedToTreasuryShares;


      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```