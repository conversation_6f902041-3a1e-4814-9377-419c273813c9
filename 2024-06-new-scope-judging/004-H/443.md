Shiny Daisy Osprey

High

# PoolLogic::executeMintToTreasury decreases supplyShares which will lead to insolvency issues

### Summary

`PoolLogic::executeMintToTreasury` decreases the total supply shares by the the shares accrued to treasury, this can lead to insolvency issues as the sum of the individual shares of all the users will be greater than the total supply shares i.e. some users will be unable to withdraw their deposits


### Root Cause

In `PoolLogic::executeMintToTreasury`

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L99

```solidity
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
@>    totalSupply.supplyShares -= accruedToTreasuryShares;
     
      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```
The `totalSupply.supplyShares` is decremented by `accruedToTreasuryShares`, this means that the total supplyShares will be less than the sum of the individual shares of all the users.
For example, 
Assuming Alice and Bob both have 100 supplyShares each and the total supplyShares is 200, and the total accruedToTreasuryShares is 5.
If Alice withdraws her 100 shares and the contract executes mint to treasury, then there'll be 95 supplyShares left and Bob will be unable to withdraw his full 100 shares.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Insolvency risks as some users will be unable withdraw all their shares.

### PoC

_No response_

### Mitigation


```diff
  function executeMintToTreasury(
    DataTypes.ReserveSupplies storage totalSupply,
    mapping(address => DataTypes.ReserveData) storage reservesData,
    address treasury,
    address asset
  ) external {
    DataTypes.ReserveData storage reserve = reservesData[asset];

    uint256 accruedToTreasuryShares = reserve.accruedToTreasuryShares;

    if (accruedToTreasuryShares != 0) {
      reserve.accruedToTreasuryShares = 0;
      uint256 normalizedIncome = reserve.getNormalizedIncome();
      uint256 amountToMint = accruedToTreasuryShares.rayMul(normalizedIncome);

      IERC20(asset).safeTransfer(treasury, amountToMint);
-     totalSupply.supplyShares -= accruedToTreasuryShares;
     
      emit PoolEventsLib.MintedToTreasury(asset, amountToMint);
    }
  }
```