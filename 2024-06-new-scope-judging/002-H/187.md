Sneaky Hazelnut Lizard

High

# The use of Collateral Shares instead of the actual collateral Amount as normally implemented by aave in the Liquidation Calculations will lead to a lot of financial miscalculation.

### Summary

ZeroLend's liquidation function is fundamentally flawed due to the incorrect use of **collateral shares** in place of **actual collateral amounts**. This miscalculation mirrors an existing problem with debt shares and similarly leads to serious financial inaccuracies during liquidation processes. The incorrect use of shares for collateral transfers, liquidation fees, and debt recalculation causes the entire liquidation function to malfunction. All transfers made will be based on shares and not the actual debt amount.
 Aave’s V3 implementation serves as an example of a proper approach, where the actual asset value is utilized instead of shares, ensuring the correct liquidation operations. 

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L143

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L138

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L168

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L181

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L188

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L192


Check Aave's implementation

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/LiquidationLogic.sol#L148C4-L148C79

```solidity
  vars.userCollateralBalance = vars.collateralAToken.balanceOf(params.user);

```
https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/AToken.sol#L128-L132

```solidity
   /// @inheritdoc IERC20
  function balanceOf(
    address user
  ) public view virtual override(IncentivizedERC20, IERC20) returns (uint256) {
    return super.balanceOf(user).rayMul(POOL.getReserveNormalizedIncome(_underlyingAsset));
  }


```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

In ZeroLend, collateral shares are mistakenly used as collateral value in critical sections of the liquidation process. This is particularly evident in the following line of code:

```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```

Using **supplyShares** instead of the actual collateral amount results in serious miscalculations. Shares are essentially scaled representations of the underlying asset and do not reflect the real asset value. This leads to incorrect calculations in various parts of the liquidation process, such as:
1. **Collateral Transfer Back to Liquidator**: The liquidator will receive the wrong amount of collateral because the collateral is represented in shares instead of its actual value.
2. **Recalculation of Liquidation Fees**: Liquidation fees, which are often calculated as a percentage of the collateral, will be miscalculated since they are based on shares instead of the correct collateral value.
3. **Alignment of Liquidation Fee and Debt to Liquidate**: The calculation to align the liquidation fee with the debt being liquidated will be completely incorrect, causing the liquidation to collapse.

This contrasts with Aave V3's approach, where the actual value of the collateral (in terms of the token balance) is used. Aave’s implementation for fetching collateral value looks like this:

```solidity
vars.userCollateralBalance = vars.collateralAToken.balanceOf(params.user);
```

Here, Aave retrieves the correct balance by considering the normalized income of the reserve:
```solidity
function balanceOf(address user) public view virtual override returns (uint256) {
    return super.balanceOf(user).rayMul(POOL.getReserveNormalizedIncome(_underlyingAsset));
}
```

This ensures that the actual collateral value, accounting for any yield or interest accrued, is used in calculations, unlike in ZeroLend, where the use of shares leads to misalignment between the expected and actual collateral amounts.

### Impact

1. **Incorrect Collateral Transfers**: Since shares are used instead of actual amounts, the liquidator may receive either significantly more or less collateral than intended. This miscalculation will result in discrepancies between the collateral liquidated and the actual debt repaid.
   
2. **Underestimated Liquidation Fees**: Fees calculated based on shares will be inaccurate, potentially overcharging users or undercharging liquidators. This could destabilize the protocol by causing unbalanced liquidations.

3. **Misaligned Liquidation Calculations**: The discrepancy between the collateral amount and debt amount will likely cause liquidation processes to collapse, as the protocol will not be able to balance debt repayment against the actual collateral being liquidated.

### Example of the Problem in ZeroLend
In the ZeroLend code, the use of collateral shares is prevalent in liquidation calculations:

```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```

This represents the user's collateral in **shares** rather than the actual collateral value. A failure to convert shares into the true collateral amount leads to various miscalculations downstream, including in the collateral transferred to the liquidator and the calculation of liquidation fees. 

### Comparison with Aave’s Correct Approach
In Aave V3, the protocol uses **actual token balances** rather than shares. For example, the user's collateral balance is correctly calculated as:

```solidity
vars.userCollateralBalance = vars.collateralAToken.balanceOf(params.user);
```

This ensures that the actual value of the collateral is used in liquidation processes, and any interest accrued over time is accounted for through the function `POOL.getReserveNormalizedIncome(_underlyingAsset)`.

### PoC

_No response_

### Mitigation

**Convert Collateral Shares to Actual Asset Value**: Similar to Aave V3, the protocol must convert collateral shares to their actual value before using them in liquidation calculations. This can be achieved by multiplying the shares by the reserve's normalized income, which will reflect the true collateral amount held by the user:
  
Done by Aave 
   ```solidity
   function balanceOf(address user) public view returns (uint256) {
       return super.balanceOf(user).rayMul(POOL.getReserveNormalizedIncome(_underlyingAsset));
   }
   ```

Zeroland should implement

Like the **the WITHDRAWAL** function, get the actual value of the collateral shares.
  
   ```solidity
 vars.userCollateralBalance = balances[params.collateralAsset][params.position].getSupplyBalance(cache.nextLiquidityIndex);
   ```

