Great <PERSON> Shetland

High

# Wrong supply amount used in `LiquidationLogic::executeLiquidationCall`, forcing liquidators to lose funds

### Summary

When liquidating a deeply underwater position, the whole position's collateral will be liquidated and transferred to the liquidator. The liquidated position's collateral consists of 2 parts the supply shares, and the assets that are accumulated from loans taken from this specific collateral, so, with time, when interest is accumulated and paid 1 supply share of a collateral will be worth more than 1 asset of that collateral.

To get the user's collateral balance (in assets) from the supply shares, the following formula is used:
```solidity
supplyShares.rayMul(index);
```
Where `index` represents the current supply index.

However, this is not happening in `LiquidationLogic::executeLiquidationCall`, where the user collateral balance is set to the supply shares instead of assets in the following:
```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```
This will affect the whole calculation done in `_calculateAvailableCollateralToLiquidate`.

As a result, liquidators will not get enough collateral when liquidating positions and the liquidated positions will still hold some collateral, even if they were deeply underwater.

### Root Cause

When liquidating a position, the position's collateral is being set as the supply shares instead of the real assets, [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136).

### Impact

* Liquidators will suffer a loss of funds when liquidating positions, as they'll be expecting some collateral amount in return but they'll be given another lower amount (shares amount).
* Underwater position will still keep some collateral after 100% liquidation.

### PoC

In the following test, we have the borrower too deep underwater, so all his collateral is liquidatable and should be transferred to the liquidator. This can be confirmed by adding a simple log in `_calculateAvailableCollateralToLiquidate`, which shows that `vars.maxCollateralToLiquidate` >> user's collateral.

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract Contest_Pool is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');
  address public borrower = makeAddr('borrower');

  uint256 public constant CLOSE_FACTOR_HF_THRESHOLD = 0.95e18;

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    poolFactory.setConfigurator(address(new PoolConfigurator(address(poolFactory))));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      // ltv: 0,
      ltv: 8000,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _syncOracles();
  }

  function getPositionId(address user, uint256 index) internal returns (bytes32) {
    return keccak256(abi.encodePacked(user, 'index', index));
  }

  function testWrongSupplyAmountUsedLiquidation() public {
    uint256 USDCamount = 1_900e6;
    uint256 WETHamount = 1e18;

    {
      USDC.mint(bob, USDCamount * 10);
      WETH.mint(borrower, WETHamount);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(borrower);
      WETH.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
    }

    // Alice supplies 1 WETH
    vm.prank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);

    // Bob deposits 1.9k USDC
    // Bob borrows 0.1 WETH
    vm.startPrank(bob);
    pool.supplySimple(address(USDC), bob, USDCamount, 0);
    pool.borrowSimple(address(WETH), bob, 0.1 ether, 0);
    vm.stopPrank();

    // Bob deposits 1.9k USDC
    // Bob borrows 1 WETH
    vm.startPrank(borrower);
    pool.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool.borrowSimple(address(USDC), borrower, USDCamount, 0);
    vm.stopPrank();

    // Some time passes
    // Some interest is accumulated
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserves();

    // WETH price drops significantly
    WETHOracle.updateAnswer(1_800e8);

    // HF is very low, lower than the threshold, i.e. 100% of the debt is liquidatable
    (, , , , , uint256 HF) = pool.getUserAccountData(borrower, 0);
    assertEq(HF / 1e17, 7); // ~70%

    uint256 borrowerDebt = pool.getDebt(address(USDC), borrower, 0);

    // Bob liquidates borrower's position while passing all the debt
    vm.prank(bob);
    pool.liquidateSimple(address(WETH), address(USDC), getPositionId(borrower, 0), borrowerDebt);

    // Borrower still has some collateral
    assertGt(pool.getBalance(address(WETH), borrower, 0), 0);
  }
}
```
</details>

### Mitigation

In `LiquidationLogic::executeLiquidationCall`, use the accumulated collateral/assets instead of the supply shares, i.e. replace:
```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```
with:
```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].getSupplyBalance(
  collateralReserve.getNormalizedIncome()
);
```