Great <PERSON> Shetland

High

# Wrong debt amount used in `LiquidationLogic::_calculateDebt`, causing liquidation to always result in wrong results

### Summary

When liquidating a position, there are 2 paths, if the HF of that position is > 95%, the liquidator can only liquidate 50% of that position's debt, else (<= 95%) all of the position's debt is liquidatable. This logic is done in `LiquidationLogic::_calculateDebt`, `userDebt` represents the debt of the position, and is being set in the following:
```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
```
However, this is wrong, as "balances...debtShares" represents the debt shares, not the debt assets (which contain the interest). To get the debt assets, it should be calculated using the next debt index.

Because, `userDebt` will never represent 100% of the position, as shares are always < assets, causing the underwater position never to be fully liquidatable.

### Root Cause

When liquidating a position, the debt shares are being used instead of assets (+ interest), [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264).

### Impact

When the HF of the liquidatable position is < 95%, the liquidation will always be wrong, as it'll never liquidate 100% of the position.

### PoC

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract Contest_Pool is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');

  uint256 public constant CLOSE_FACTOR_HF_THRESHOLD = 0.95e18;

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    poolFactory.setConfigurator(address(new PoolConfigurator(address(poolFactory))));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      ltv: 0,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _syncOracles();
  }

  function getPositionId(address user, uint256 index) internal returns (bytes32) {
    return keccak256(abi.encodePacked(user, 'index', index));
  }

  function testWrongDebtAmountUsedLiquidation() public {
    uint256 USDCamount = 1_900e6;
    uint256 WETHamount = 1e18;

    {
      USDC.mint(bob, USDCamount * 10);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
    }

    // Bob deposits 1.9k USDC
    vm.prank(bob);
    pool.supply(address(USDC), bob, USDCamount, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));

    // Alice supplies 1 WETH
    // Alice borrows 1.9k USDC
    vm.startPrank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);
    pool.borrowSimple(address(USDC), alice, USDCamount, 0);
    vm.stopPrank();

    // Some time passes
    // Some interest is accumulated
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserve(address(USDC));

    // WETH price drops
    WETHOracle.updateAnswer(2_300e8);

    // HF is lower than the threshold, i.e. 100% of the debt is liquidatable
    (, , , , , uint256 HF) = pool.getUserAccountData(alice, 0);
    assertLt(HF, CLOSE_FACTOR_HF_THRESHOLD);

    // Alice debt is > 1.9k USDC
    // Alice debt shares are 1.9k USDC
    uint256 aliceDebt = pool.getDebt(address(USDC), alice, 0);
    uint256 aliceDebtShares = pool.debtShares(address(USDC), getPositionId(alice, 0));
    assertGt(aliceDebt, USDCamount);
    assertEq(aliceDebtShares, USDCamount);

    // Bob liquidates Alice's position while passing all the debt
    vm.prank(bob);
    pool.liquidateSimple(address(WETH), address(USDC), getPositionId(alice, 0), aliceDebt);

    // Alice's decreased by 1.9k USDC (debt shares) instead of going down to 0
    assertEq(pool.getDebt(address(USDC), alice, 0), aliceDebt - aliceDebtShares);
  }
}
```
</details>

### Mitigation

In `LiquidationLogic::_calculateDebt`, use the accumulated debt instead of the shares, i.e. replace:
```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
```
with:
```solidity
uint256 userDebt = balances[params.debtAsset][params.position].getDebtBalance(debtReserveCache.nextBorrowIndex);
```
Note that `debtReserveCache` should be passed from `LiquidationLogic::executeLiquidationCall`.
