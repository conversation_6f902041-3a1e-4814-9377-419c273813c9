Special Macaroon Shetland

High

# Incorrect Variable Usage in Liquidation Logic Can Prevent Positions from Being Fully Liquidated

### Summary

In [LiquidationLogic.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136), `_calculateAvailableCollateralToLiquidate()` function will return a false information because `vars.userCollateralBalance` is set wrong.

### Root Cause

The root cause is sending `supplyShares` variable to function instead of sending assets value of those shares. 

```solidity
    vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;

    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```

This situation will make the output of the function wrong. 


### Internal pre-conditions

N/A

### External pre-conditions

N/A


### Impact

Positions cannot be liquidated fully because of wrong calculation logic. In the function, price is directly multiplied by the `vars.collateralAmount`. But this parameter is sent as shares, not assets. 

```solidity
&>  vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;  // @audit It returns wrong value, it should return collateral amount

    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
&>    vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```


```solidity
    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
      vars.debtAmountNeeded = (
&>      (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }

    if (liquidationProtocolFeePercentage != 0) {
      vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
      vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);
      return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
    } else {
      return (vars.collateralAmount, vars.debtAmountNeeded, 0);
    }
  }
  ```
  
  The `actualCollateralToLiquidate` variable will be lesser than expected. Because shares are lesser than assets. In conclusion, positions become not fully liquidatable. 

### Mitigation

For correct calculation, it should calculate the assets value of the shares.

`supplyShares.rayMul(cache.nextLiquidityIndex);`