Sneaky Hazelnut Lizard

High

# when The user Tries to liquidate the max debt a mistake was made to return Debt Shares instead of Amount as normally implemented by aave in the Liquidation Calculations this will lead to a lot of financial miscalculation.

### Summary

The liquidation function of ZeroLend currently suffers from a significant miscalculation issue, where the function incorrectly uses the user's debt shares instead of converting them to their actual asset value during debt liquidation. This error can lead to financial inaccuracies and cause the liquidation function to fail, leaving the protocol unable to liquidate all debt positions amount effectively, leaving behind some debt shares.  Aave V3 uses a proper conversion mechanism, ensuring the balance is calculated accurately for debt repayment.


### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264   

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L268

Check the correct implementation by Aave 3.

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/logic/LiquidationLogic.sol#L368-L371

```solidity
 function _calculateDebt(
    DataTypes.ReserveCache memory debtReserveCache,
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor
  ) internal view returns (uint256, uint256, uint256) {
    (uint256 userStableDebt, uint256 userVariableDebt) = Helpers.getUserCurrentDebt(
      params.user,
      debtReserveCache
    );
```


https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/libraries/helpers/Helpers.sol#L24

```solidity
  function getUserCurrentDebt(
    address user,
    DataTypes.ReserveCache memory reserveCache
  ) internal view returns (uint256, uint256) {
    return (
      IERC20(reserveCache.stableDebtTokenAddress).balanceOf(user),
      IERC20(reserveCache.variableDebtTokenAddress).balanceOf(user)
    );
```

https://github.com/aave/aave-v3-core/blob/782f51917056a53a2c228701058a6c3fb233684a/contracts/protocol/tokenization/StableDebtToken.sol#L101-L111 

```solidity
 function balanceOf(address account) public view virtual override returns (uint256) {
    uint256 accountBalance = super.balanceOf(account);
    uint256 stableRate = _userState[account].additionalData;
    if (accountBalance == 0) {
      return 0;
    }
    uint256 cumulatedInterest = MathUtils.calculateCompoundedInterest(
      stableRate,
      _timestamps[account]
    );
    return accountBalance.rayMul(cumulatedInterest);
  }
```

Aave returns the balance raymul latest index but zerolend uses shares directly, leading to a lot of miscalculation

The returned values are used for further calculations and token transfer 

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L117 

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

The vulnerability arises from ZeroLend's liquidation function, which attempts to use the user's debt shares (`userDebt`) directly during the debt repayment process. In Aave V3, when calculating a user’s current debt, the balance is derived by converting the shares to their actual value using the following logic:

```solidity
(uint256 userStableDebt, uint256 userVariableDebt) = Helpers.getUserCurrentDebt(
  params.user,
  debtReserveCache
);
```

This function calls `balanceOf()` from `stableDebtTokenAddress`, where the user’s balance is adjusted for interest, thus returning the actual debt amount instead of the shares.

In contrast, ZeroLend uses the user's debt shares without converting them, leading to incorrect values being returned for `vars.userDebt` and `vars.actualDebtToLiquidate` when we try to liquidate the max debt. These incorrect values are subsequently used in further calculations, such as `_calculateAvailableCollateralToLiquidate`, and also to validate the liquidation call itself.

The critical code section in ZeroLend that causes the issue is:
```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
```
By using shares instead of the actual asset value, the liquidation function attempts to transfer shares (scaled by index and ray) rather than the true amount owed. This miscalculation undermines the liquidation process and leads to potential financial loss or a failure to liquidate.


### Impact

The miscalculation in the liquidation function can cause significant financial issues for both the protocol and users. Since the protocol is unable to correctly calculate the actual debt amount during liquidation, the liquidation function could either:

1. **Under-liquidate**, allowing the user to keep collateral that should be liquidated to repay debt.
2. **Fail to liquidate entirely**, leading to a failed debt recovery process and financial instability for the protocol.

This error may result in debt piling up, increased exposure to bad debt, and even liquidity issues for ZeroLend.

### PoC

_No response_

### Mitigation

Convert the debt balance to asset by querying the debt balance with the latest debt liquidity index. As done in the **Repay function** 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/BorrowLogic.sol#L126

```solidity
uint256 userDebt = balances.getDebtBalance(cache.nextBorrowIndex);
```