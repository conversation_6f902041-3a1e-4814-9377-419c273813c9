Joyous Rainbow Shark

High

# `LiquidationLogic::_calculateDebt()` treats debt shares as assets which results in bad debt, invalid health checks, and loss of protocol liquidation fees

### Summary

The pool's liquidation flow incorrectly treats debt values denominated in shares as values denominated in assets. This creates issues such as liquidators being unable to repay the full debt amount due to the `debtIndex` being greater than 1. The reserve asset of the debt is also incorrectly removed from the postiion which breaks future health checks.

### Root Cause

First the root cause is the lack of conversion of debt shares to debt amount in [`LiquidationLogic::_calculateDebt()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273)

```javascript
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
@>  uint256 userDebt = balances[params.debtAsset][params.position].debtShares; // @audit should convert this to debt amount

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

@>  uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover; // @audit compares an amount to shares

@>  return (userDebt, actualDebtToLiquidate); // @audit return units: (shares, amount) causing problems in the liquidation flow
  }
```

The returned `actualDebtToLiquidate` from above will be the amount of debt the liquidator [transfers back to the pool](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L192), so the debt isn't fully repaid (impact 1). 

When the return values in `_calculateDebt()` are equal, the debt is incorrectly [removed from the position](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150-L152), despite the position having outstanding debt shares. This results in the pending debt no longer being considered in health checks and the position looking more 'healthy' than it actually is (impact 2).

And finally, the error of `actualDebtToLiquidate` being in shares propagates to the `liquidationProtocolFeeAmount` through the `LiquidationLogic::_calculateAvailableCollateralToLiquidate()` function. The liquidation flow incorrectly assumes this is an amount, and [attempts to convert it to shares by dividing by the index](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L181). This results in the protocol earning less fees than expected (impact 4).

### Internal pre-conditions

1. Interest needs to have accrued on a debt position. This is trivial because `ReserveLogic::updateState()` is called at the begining of the liquidation flow.

### External pre-conditions

1. Collateral price fluction makes a position liquidatable

### Attack Path

1. Liquidator calls `Pool::liquidate()` or `Pool::liquidateSimple()` on a liquidatable posistion

### Impact

1. All debt will not be repaid even if when liqudator requests to repay all debt, due to the 'number of shares' being repaid as 'amount' of assets. 
2. When `vars.userDebt == vars.actualDebtToLiquidate`, the borrowed token is set to false in the user configuration object. This means despite the position having outstanding debtShares, this debt will be ignored in future health checks.
3. `liquidationProtocolFeeAmount` is scaled down again as the flow assume it's an 'amount' not 'number of shares', resulting in less fees earned for the protocol.

### PoC

To run the test create a new file in `/test/forge/core/pool` and paste the below contents. The below test shows the first two impacts above in the final assert statements

```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {PoolLiquidationTest} from './PoolLiquidationTests.t.sol';

import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';
import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';
import {DataTypes} from './../../../../contracts/core/pool/configuration/DataTypes.sol';

import {console2} from 'forge-std/src/Test.sol';

contract AuditLiquidationFlowCalcsDebtInShares is PoolLiquidationTest {
  using UserConfiguration for DataTypes.UserConfigurationMap;
  using ReserveConfiguration for DataTypes.ReserveConfigurationMap;

function test_POC_LiquidationFlowCalcsDebtInShares() public {
    // Setup
    oracleA.updateAnswer(1e8);
    oracleB.updateAnswer(1e8);

    // Alice takes loan, Bob is supplier/liquidator
    uint256 aliceMintAmount = 1000e18;
    uint256 bobMintAmount = 1000e18;
    uint256 supplyAmount = 150e18;
    uint256 borrowAmount = 70e18;

    _mintAndApprove(alice, tokenA, aliceMintAmount, address(pool));         // alice collateral
    _mintAndApprove(bob, tokenB, bobMintAmount, address(pool));             // bob supply

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmount, 0); 
    vm.stopPrank();

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmount, 0);  // alice collateral
    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);    
    vm.stopPrank();

    // Price of collateral decreases
    vm.warp(block.timestamp + 90 days);
    oracleA.updateAnswer(5e7);
    oracleA.updateRoundTimestamp();
    oracleB.updateRoundTimestamp();
    pool.forceUpdateReserves(); 




    vm.startPrank(bob);
    bytes32 _pos = keccak256(abi.encodePacked(alice, 'index', uint256(0)));
    pool.liquidateSimple(address(tokenA), address(tokenB), _pos, type(uint256).max); // Bob attempts to liquidate Alice (full liquidation)
    vm.stopPrank();

    (, uint256 totalDebtBase, , , , uint256 healthFactor) = pool.getUserAccountData(alice, 0);

    // Impact 1 - Despite attempting to liquidate all of Alice's debt, Bob only liquidates a portion of it. As a result Alice has remaining collateral value, no remaining debt value, but they have debt shares.
    assert(pool.getBalanceRawByPositionId(address(tokenB), _pos).debtShares > 0); // Alice has remaining debtShares

    // Impact 2 - Invalid future health checks on Alice's position
    assert(totalDebtBase == 0); // Alice's debt value is apparently 0 as it was incorrectly removed consideration as debt
    assert(healthFactor == type(uint256).max); // Alice has infinite health factor despite having outstanding debtShares

  }
}
```

### Mitigation

Convert the debt shares to debt amount in `_calculateDebt()`. Note the input variable `userCollateralBalance` to `LiquidationLogic::_calculateAvailableCollateralToLiquidate()` will also need to be converted to amounts (instead of shares).

