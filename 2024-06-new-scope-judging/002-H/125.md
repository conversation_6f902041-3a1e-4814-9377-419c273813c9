Powerful Licorice Elephant

Medium

# When liquidating a position’s  collateralAsset’s interest(earning) and debtasset’s interest(paying) is not accounted.

## Summary
If a debt asset’s(a position’s) full debtshare can be repaid and a liquidator repays the full debtshare, then debt share interest will not be paid. Similarly,  a collateral asset’s(a position’s) full collateral is taken by liquidator,then accumulated   interest is not added to full collateral. As a result, if the debt asset’s interest is bigger than collateral asset’s interest for this position, then protocol will affect/lose interest(this to pay) and if collateral asset’s interest is bigger than debt asset’s interest for this position, then position owner will lose interest for this collateral. 

## root cause
When liquidating a position  collateralAsset’s interest(earning) and debtasset’s interest(paying) is not accounted.

## Vulnerability Detail
liquidator can liquidate an unhealthy position by paying a debt asset’s amounts and taking a collateral asset’s amounts  of a position.

If a debt asset’s(a position’s) full debtshare can be repaid and a liquidator repays the full debtshare, then debt share interest will not be paid. 

Let’s see how? When  function executeRepay(library BorrowLogic) is called to repay the full debt , it considers debt amounts+interest[ payback.assets = balances.getDebtBalance(cache.nextBorrowIndex) i.e function getDebtBalance is called where full debt + interest is calcculated]. But when function executeLiquidationCall is called which calls function _calculateDebt where userDebt = balances[params.debtAsset][params.position].debtShares; i.e userdebt don’t add the interests. As a result, the debt asset’s interest will not be paid.

Similarly,  a collateral asset’s(a position’s) full collateral is taken by liquidator,then accumulated   interest is not added to full collateral.

 Let’s see how? When function executeWithdraw(library SupplyLogic) is called to withdraw the collateral amounts, it considers supply amounts+interests[balance = balances[params.asset][params.position].getSupplyBalance(cache.nextLiquidityIndex); i.e function getSupplyBalance is called where supply amounts+interests is calculated]. But in function executeLiquidationCall,  vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares i.e interest is not added to  vars.userCollateralBalance. 

## Impact
 if the debt asset’s interest is bigger than collateral asset’s interest for this position, then protocol will effect/lose interest(this to pay) and if collateral asset’s interest is bigger than debt asset’s interest for this position, then position owner will lose interest for this collateral. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264

## Tool used

Manual Review

## Recommendation
account  collateralAsset’s interest(earning) and debtasset’s interest(paying) When liquidating a position