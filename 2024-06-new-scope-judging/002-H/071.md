Acrobatic Rainbow Grasshopper

High

# Liquidations will be executed with wrong values

## Summary
Liquidations will be executed with wrong values
## Vulnerability Detail
Upon liquidations, `LiquidationLogic::executeLiquidationCall()` is called. There we do all kinds of computations, checks, etc. For example, this is one of the most important functions being called there:
```solidity
    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate, // @note Wrong
      vars.userCollateralBalance, // @note Wrong
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```
Without going into too much detail, we can see that we calculate the debt to liquidate, the collateral to take and the protocol liquidation fees, the first 2 especially are definitely the most important values during liquidations. The issue is that `vars.actualDebtToLiquidate`, `vars.userCollateralBalance`, (inputs in the function above, also used in other places in the function) and `vars.userDebt` are all wrongly fetched. For example, this is how the user collateral balance is calculated:
```solidity
    vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```
As seen, this actually just takes the supply shares of the user. In "Aave" terms (protocol which this protocol was inspired from), this is the scaled value and it's not the actual balance the user has, it's simply the user balance without any accrued interest, for example a user might have deposited 100 assets but a year later, he might have access to 110 as he has accrued interest. Thus, all calculations in the functions using the values listed above (and other values computed using them) are incorrect. Simply put, we are using shares instead of assets for liquidations which is absolutely wrong.

## Impact
Liquidations will be executed with wrong values
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94
## Tool used

Manual Review

## Recommendation
Compute the balances using the indexes to properly calculate the balance of the user