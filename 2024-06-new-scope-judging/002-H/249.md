Curly Pineapple Armadillo

High

# Borrowers cannot be fully liquidated, due to `executeLiquidationCall` not converting shares into assets

### Summary

The `executeLiquidationCall` function does not convert collateral and debt shares into assets and operates with the assumption that the collateral/debt shares are equal to the collateral/debt assets. As a result, borrowers will not be fully liquidatable.

### Root Cause

- In [`executeLiquidationCall:136`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136) the borrower's collateral balance is presented in shares
- In [`_calculateDebt:264`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264) the borrower's debt balance is presented in shares

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. One user borrows 1 ETH, receiving 1e18 of debt shares. The user has 2000 USDC for collateral equal to 2000e6 of supply shares.
2. Borrow rate accrues and the user owes 1.1 ETH, their debt shares are still 1e18.
3. Interest accrues and the user's collateral is now worth 2200 USDC, their supply shares are still 2000e6.
4. The borrower becomes liquidatable, but their debt to liquidate is limited to `(1 ETH).rayDiv(index)`, instead of `(1.1 ETH).rayDiv(index)`, and their liquidatable collateral is only `(2000 USDC).rayDiv(index)` instead of `(2200 USDC).rayDiv(index)`.

### Impact

Borrowers will not be fully liquidatable, increasing the chances of positions becoming insolvent and bad debt accruing in the pool.

### PoC

_No response_

### Mitigation

In [`executeLiquidationCall:136`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136) and [`_calculateDebt:264`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264) convert the shares to assets, to account for the accrued interest.