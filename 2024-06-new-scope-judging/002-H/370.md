Curly <PERSON>ard

High

# amount `userCollateralBalance` is incorrect in liquidation

### Summary

The `vars.userCollateralBalance` in `contracts/core/pool/logic/LiquidationLogic.sol#L136` should use normalized amount instead of amount in shares. This collateral balance is considered as real asset amount in the following calculation (`_calculateAvailableCollateralToLiquidate()`), not as shares.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

_No response_

### PoC

_No response_

### Mitigation

_No response_