Polished Iris Antelope

High

# ```_calculateDebt``` function of ```LiquidationLogic``` does not convert ```debtShares``` to ```debtAmount``` and returns always ```debtShares``` breaking the functionality of every liquidation.

## Summary
Liquidation procedure is being implemented totally incorrectly since the ```debtShares``` are not converted to ```debtAmount``` from the very start leading to the whole process to be executed with unexpected results.

## Vulnerability Detail
When a liquidator wants to liquidate a borrowers whose ```healthFactor``` is below ```1e18```, he calls the ```liquidate``` function of ```Pool``` passing the ```debtToken```, the ```collateralToken``` and ```debtAmount``` that he will repay (and other parameters). Eventually, the transaction flow will go to ```executeLiquidation``` of ```LiquidationLogic``` and there happens the whole procedure. In start, ```executeLiquidation``` validates that the borrower is under collateralized and then, based on the ```debtAmount``` that the liquidator gave and the ```healthFactor``` of the borrower, the actual debt that will be repayed is calculated. We can see the implementation and the way that it is calculated here :
```solidity
  function executeLiquidationCall(
    // ...
  ) external {
    LiquidationCallLocalVars memory vars;

    DataTypes.ReserveData storage collateralReserve = reservesData[params.collateralAsset];
    DataTypes.ReserveData storage debtReserve = reservesData[params.debtAsset];
    DataTypes.UserConfigurationMap storage userConfig = usersConfig[params.position];
    vars.debtReserveCache = debtReserve.cache(totalSupplies[params.debtAsset]);
    debtReserve.updateState(params.reserveFactor, vars.debtReserveCache);

    (,,,, vars.healthFactor,) = GenericLogic.calculateUserAccountData(
      balances,
      reservesData,
      reservesList,
      DataTypes.CalculateUserAccountDataParams({userConfig: userConfig, position: params.position, pool: params.pool})
    );

@>    (vars.userDebt, vars.actualDebtToLiquidate) = _calculateDebt(
      // vars.debtReserveCache,
      params,
      vars.healthFactor,
      balances
    );

    // ...

    // ...

    // ...
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94)

However, if we see the ```_calculateDebt``` function, we will understand that it compares the ```userDebt``` which is ```debtShares``` witht the ```debtToCover``` which is in token terms :
```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
@>  uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

@>    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```
As a result, since ```shares``` are ```amount``` / ```borrowIndex```, almost always, the ```debtToCover``` will be bigger than the ```maxLiquidatableDebt```. This leads to ```actualDebtToLiquidate``` being the total ```debtShares``` of the borrower. This will totally mess up the liquidation process since, ```actualDebtToLiquidate``` is handled like it is in ```debtAmount``` terms from this point and after while it is in ```shares``` term. Look this example :
```solidity
  function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
@>  uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    // ...

    // This is the base collateral to liquidate based on the given debt to cover
@>  vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

@>    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
      vars.debtAmountNeeded = (
        (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }

    // ...
  }
```
As we can understand, ```debtToCover``` variable is in ```shares``` terms but it is converted to value based on the price of the ```debtToken```.

## Impact
The impact of this vulnerability is critical since it is making the liquidation process(one of the most important procedures of a lending protocol) to be executed with unexpected results. Most liquidations will revert and others in other scenarios will produce unintented for the liquidator and the protocol results. After all, it undermines the solvency of the protocol since it opnes the door to bad debt accrued with liquidations basically unusable.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94

## Tool used
Manual review

## Recommendation
Consider refactoring the whole liquidation procedure with firstly converting the ```debtShares``` and ```supplyShares``` to ```amounts``` for the calculations and transfers that is required :
```diff
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

+   forceUpdateReserves();    
+   userDebt /= balances[params.debtAsset][params.position].borrowIndex;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```