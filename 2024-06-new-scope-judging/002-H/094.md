Steep Aqua Yak

High

# Liquidation does not take into account the interest

## Summary
In liquidation flow, position debt does not take into account the interest.

## Vulnerability Detail
Position's [risk data is calculated with interest taken into account through function `GenericLogic#calculateUserAccountData`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L69-L152). 
In the function [`LiquidationLogic#_calculateDebt()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273), position debt is calculated based on its health factor. However, only the [debt principal is taken into account](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264) without interest. 
The following function in liquidation flow [`Liquidation#_calculateAvailableCollateralToLiquidate`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L374) will also function incorrectly. So far, [the parameter `debtToCover`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L331) is lower than actual, causing the returned value `vars.debtAmountNeeded` (which is the final debt amount to be covered in liquidation) lower than actual. And in case that [amount equals to position's principal debt, the user debt for that reserve is closed](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150-L152), which is also unexpected.

#### PoC
Update the test file `test/forge/core/pool/PoolLiquidationTests.t.sol` as below:
```diff
diff --git a/zerolend-one/test/forge/core/pool/PoolLiquidationTests.t.sol b/zerolend-one/test/forge/core/pool/PoolLiquidationTests.t.sol
index e770be3..9d627ba 100644
--- a/zerolend-one/test/forge/core/pool/PoolLiquidationTests.t.sol
+++ b/zerolend-one/test/forge/core/pool/PoolLiquidationTests.t.sol
@@ -6,6 +6,7 @@ import './PoolSetup.sol';
 import {ReserveConfiguration} from './../../../../contracts/core/pool/configuration/ReserveConfiguration.sol';
 
 import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

 
 contract PoolLiquidationTest is PoolSetup {
   using UserConfiguration for DataTypes.UserConfigurationMap;
@@ -32,18 +33,85 @@ contract PoolLiquidationTest is PoolSetup {
 
+
+  function test_liquidation_without_interest() external {
+    _generateLiquidationCondition();
+
+    uint256 debtBeforeAccrued = pool.getDebt(address(tokenB), alice, 0);
+    pool.forceUpdateReserve(address(tokenB));
+    uint256 debt = pool.getDebt(address(tokenB), alice, 0);
+
+    // debt get accrued
+    assertGt(debt, debtBeforeAccrued);
+
+    vm.startPrank(bob);
+    pool.liquidateSimple(address(tokenA), address(tokenB), pos, debt); // liquidate amount = accrued debt
+
+    vm.stopPrank();
+
+    (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);
+    uint256 finalDebt = pool.getDebt(address(tokenB), alice, 0);
+    uint256 debtShare = pool.debtShares(address(tokenB), pos);
+    assertEq(totalDebtBaseNew, 0); // total debt in base currency is zero because of borrowing status is false
+    assertGt(finalDebt, 0); // final debt is still not zero
+    assertGt(debtShare, 0); // debt share is still not zero
   }
 
   function testLiquidationSimpleRevertWhenCollateralNotActive() external {
@@ -80,6 +148,18 @@ contract PoolLiquidationTest is PoolSetup {
 
     assertEq(tokenB.balanceOf(alice), borrowAmountB);
 
-    oracleA.updateAnswer(5e3);
+    address chad = makeAddr('chad');
+    _mintAndApprove(chad, tokenA, 100 * supplyAmountA, address(pool));
+    vm.startPrank(chad);
+    pool.supplySimple(address(tokenA), chad, 100 * supplyAmountA, 0);
+    pool.borrowSimple(address(tokenB), chad, borrowAmountB, 0);
+    vm.stopPrank();
+
+    skip(365 days);
+
+    oracleA.updateAnswer(1e8);
+    oracleA.updateRoundTimestamp();
+    oracleB.updateRoundTimestamp();
+    oracleB.updateAnswer(5e8);
   }
 }

```

 
## Impact
1. Position can only be liquidated up to principal
2. Can not liquidate bad debt because there will be debt in the position even if all its collateral is taken
3. Position borrowing status goes wrong

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L69-L152
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273
(https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L374
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L331
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150-L152
## Tool used

Manual Review

## Recommendation
Update the function `_calculateDebt` as below:

```diff
function _calculateDebt(
+DataTypes.ReserveCache debtReserveCache memory cache,
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
-uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
+uint256 userDebt = balances[params.debtAsset][params.position].getDebtBalance(cache.currBorrowIndex);

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```