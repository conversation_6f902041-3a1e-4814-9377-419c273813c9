Powerful Licorice Elephant

High

# vars.baseCollateral/ vars.debtAmountNeeded/vars.collateralAmount calculations are incorrect in the  function _calculateAvailableCollateralToLiquidate.

## Summary
the debt share is not converted to debt amounts and supplyshare of collateral asset and this is not converted to supply amounts. vars.collateralAmount and vars.debtAmountNeeded are not converted from oracle price to normal amounts.
As a result, _calculateAvailableCollateralToLiquidate accounting willbe incorrect and ncorrect amounts of debt will be transferred to the pool  by the liquidator and incorrect amounts of collateral will be transferred to the liquidator from the protocol.


## root cause
the debt share is not converted to debt amounts and supplyshare of collateral asset and this is not converted to supply amounts. vars.collateralAmount and vars.debtAmountNeeded are not converted from oracle price to normal amounts.

## Vulnerability Detail

see  function _calculateDebt, here userDebt is debt share.maxLiquidatableDebt is calculated based on userDebt share. Now if params.debtToCover > maxLiquidatableDebt,then actualDebtToLiquidate is maxLiquidatableDebt which debt share(the debt share is not converted to debt amounts).

vars.actualDebtToLiquidate(which is debt share) return from function _calculateDebt puts as input value(i.e debtToCover) and also vars.userCollateralBalance as input value(i.e userCollateralBalance) in function _calculateAvailableCollateralToLiquidate.

See function _calculateAvailableCollateralToLiquidate, when  vars.baseCollateral is calculated, it calculates based on  debtToCover which is debtshare (debtshare is not converted to debt amount).  Similarly, vars.debtAmountNeeded is calculated based on vars.collateralAmount(userCollateralBalance which is supplyshare of collateral asset and this is not converted to supply amounts).

As  supplyshare and debtshare is not converted to actual amount, so  vars.baseCollateral/vars.debtAmountNeeded calculation will be unexpected and _calculateAvailableCollateralToLiquidate accounting willbe incorrect.
When vars.collateralAmount and vars.debtAmountNeeded are returned from function _calculateAvailableCollateralToLiquidate,  vars.collateralAmount and vars.debtAmountNeeded are not converted from oracle price amounts  to normal amounts(protocol supported amount for those asset)

Let’s see when? A.  vars.maxCollateralToLiquidate > userCollateralBalance, then vars.debtAmountNeeded is calculated based on oracle price but  vars.maxCollateralToLiquidate  is not converted back to normal amounts and also  vars.collateralAmount is not converted from share to actual amount.

B. When vars.maxCollateralToLiquidate <= userCollateralBalance, then vars.collateralAmount = vars.maxCollateralToLiquidate which is calculated based on oracle price but not converted to normal amounts and also vars.debtAmountNeeded = debtToCover which is also not converted from share to actual amount.

As a result incorrect amounts of debt will be transferred to the pool  by the liquidator and incorrect amounts of collateral will be transferred to the liquidator from the protocol.


 
## Impact
a. As  supplyshare and debtshare is not converted to actual amount, so  vars.baseCollateral/vars.debtAmountNeeded calculation will be unexpected and _calculateAvailableCollateralToLiquidate accounting willbe incorrect.
b.  incorrect amounts of debt will be transferred to the pool  by the liquidator and incorrect amounts of collateral will be transferred to the liquidator from the protocol.


## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328
## Tool used

Manual Review

## Recommendation
convert the debt share to debt amounts, convert the supply share to supply amounts