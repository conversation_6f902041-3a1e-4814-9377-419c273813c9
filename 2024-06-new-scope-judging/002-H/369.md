Curly <PERSON>

High

# _calculateDebt() return amount in incorrect uint

### Summary

The `_calculateDebt()` used in `contracts/core/pool/logic/LiquidationLogic.sol` should return two normalized amount instead of amount in shares. These two amount will be considered as normalized in the rest calculation in `executeLiquidationCall()` and also used as the transfer amount of underlying tokens.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

_No response_

### PoC

_No response_

### Mitigation

_No response_