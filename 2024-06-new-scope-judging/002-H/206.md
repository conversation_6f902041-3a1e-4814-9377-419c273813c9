Joyous Cedar Tortoise

High

# Not converting user collateral from shares to assets in LiquidationLogic

## Summary

## Vulnerability Detail
[Here](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136), in `LiquidationLogic`, it calculates `vars.userCollateralBalance` as supply shares, but its not converted to assets by multiplying by normalized income.

```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```
## Impact
[This](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L156-L159) calc will be wrong, so its sets the reserve asset as ‘not collateral’ even though the user still has collateral left.

This means they can now withdraw the remaining collateral without a health factor check. (See [`SupplyLogic.executeWithdraw()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L149-L151))
## Code Snippet

## Tool used

Manual Review

## Recommendation
```diff
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;

+vars.userCollateralBalance = vars.userCollateralBalance.rayMul(reserve.getNormalizedIncome());
```