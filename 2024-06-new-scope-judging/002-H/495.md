Massive Glass Crane

High

# actualDebtToLiquidate is incorrectly calculated by assigning the share amount instead of the underlying assets , affecting the liquidator.

### Summary

The [function](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273) `_calculateDebt()` is calculating the `actualDebtToLiquidate` by considering the healthFactor of the position. If the amount `params.debtToCover` which is provided by the liquidator is greater than the `maxLiquidatableDebt` then the `actualDebtToLiquidate` is assigned the value of `maxLiquidatableDebt`.

But the `params.debtToCover` indicates the `underlying assets`  that liquidator want to cover from the users position while `maxLiquidatableDebt` indicates the total balance `shares`  held by the position.

By trying to compare `params.debtToCover` with `maxLiquidatableDebt` without making an attemt to convert 
`maxLiquidatableDebt` to its asset value , `actualDebtToLiquidate` will always result to an incorrect amount which inflicts loss on the user who is getting liquidated.

### Root Cause
Inside the function  `_calculateDebt()` ,
`maxLiquidatableDebt` is calculated by multiplying `userDebt` with `closeFactor` where the `userDebt` stores the value of `debtShares` hold by the `user` in that `reserve`.
[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264-L268)
```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);
```

Hence `maxLiquidatableDebt` now has the mximum value of `SHARES` that the liquidator can liquidate.

But in the comparison , `maxLiquidatableDebt` is compared with `params.debtToCover` which is actually the amount of `ASSETS` the liquidator wanted to liquidate.
[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L270)
```solidity
 uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;
 ```


 This same behaviour is repeated while calculating the userCollateralBalance where the collaterals supplyShares is taken and compared with debtToCover instead of supplyAssets.[code](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136).

These issues are also affecting the calculation of `baseCollateral` , since `share tokens` are used for the calculation isntead of the underlying `asset token`.

### Internal pre-conditions

Liquidator called liquidation for a user who

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact
Due to the wrong comparison of share token with asset token , liquidators can only liquidate the less than the specified amount.


### PoC


### Mitigation
`maxLiquidatableDebt` should be multiplied by the liquityIndex to get the real time asset value  before coparing with the `debtToCover`.