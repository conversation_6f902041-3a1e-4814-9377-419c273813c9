Smooth Carbon Narwhal

High

# The liquidation process is not functioning correctly

## Summary
When the `health factor` of a `position` falls below `1`, it becomes eligible for `liquidation`.
`Liquidation` is a critical process in the `Lending Protocol`, but currently, it operates incorrectly.
The problem arises because the calculation of the `liquidated debt amount` and `collateral balance` is based on `shares` rather than the actual `asset` amounts.
## Vulnerability Detail
The `liquidation` process takes place in the `executeLiquidationCall` function. 
On `line 117`, the `_calculateDebt` function is called to determine the user's `debt balance` and the amount to be `liquidated`. 
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
117:  (vars.userDebt, vars.actualDebtToLiquidate) = _calculateDebt(
    params,
    vars.healthFactor,
    balances
  );
}
```
However, in the `_calculateDebt` function, both the user's `debt balance` and the `liquidated amount` are calculated using `shares` instead of actual `assets` (`lines 264, 270`). 
```solidity
function _calculateDebt(
  DataTypes.ExecuteLiquidationCallParams memory params,
  uint256 healthFactor,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
) internal view returns (uint256, uint256) {
264:  uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

  uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;
  uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);
  
270:  uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

  return (userDebt, actualDebtToLiquidate);
}
```
It's worth to note that there’s a difference between `shares` and `assets`.
```solidity
assets = shares * borrow index
```
Returning to the `executeLiquidationCall` function, we see on `line 136` that the user's `collateral balance` is also calculated in `shares`. 
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
117:  (vars.userDebt, vars.actualDebtToLiquidate) = _calculateDebt(
    params,
    vars.healthFactor,
    balances
  );

136:  vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;

138:  (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
  _calculateAvailableCollateralToLiquidate(
    collateralReserve,
    vars.debtReserveCache,
    vars.actualDebtToLiquidate,
    vars.userCollateralBalance,
    vars.liquidationBonus,
    IPool(params.pool).getAssetPrice(params.collateralAsset),
    IPool(params.pool).getAssetPrice(params.debtAsset),
    IPool(params.pool).factory().liquidationProtocolFeePercentage()
  );
}
```
`Shares` are always less than the actual `asset balance`.
On `line 138`, the `_calculateAvailableCollateralToLiquidate` function is called to determine how much `debt` can be `liquidated` and how much `collateral` the `liquidator` can receive.
In `line 352`, we multiply the `debt price` by the `debt shares`(`debtToCover` represents `shares`, not `assets`). 
```solidity
function _calculateAvailableCollateralToLiquidate(
  DataTypes.ReserveData storage collateralReserve,
  DataTypes.ReserveCache memory debtReserveCache,
  uint256 debtToCover,
  uint256 userCollateralBalance,
  uint256 liquidationBonus,
  uint256 collateralPrice,
  uint256 debtAssetPrice,
  uint256 liquidationProtocolFeePercentage
) internal view returns (uint256, uint256, uint256) {
352:  vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
}
```
The problem here is that the `debt price` corresponds to `1 debt asset`, not `1 debt share`.

For example, if the `debt shares` amount to `100`, and the current `borrow index` is `1.5`, the actual `debt` in `assets` would be `150`. 
In this case, the `liquidation` process should cover `150`, but instead, it only handles up to `100` because the calculation is incorrectly capped at the number of `shares`.
## Impact
As a result, the entire `liquidation` process is flawed because it calculates based on `shares` rather than the actual `asset` amounts. 
This makes the liquidation process inaccurate.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L117-L122
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L352
## Tool used

Manual Review

## Recommendation
We should use `asset balances` instead of `shares` for calculating `debts` and `collateral`.