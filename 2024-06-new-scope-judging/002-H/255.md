Careful Fleece Pike

High

# Incorrect conversion between debt shares and supply collateral shares will cause incorrect calculation of liquidation rewards or debt deduction

### Summary

Incorrect conversion between debt shares and supply collateral shares will cause incorrect calculation of liquidation rewards or debt deduction.

### Root Cause

During the liquidation process, the amount of collateral (`collateralAmount`) to take from a position and the amount of debt (`debtAmountNeeded`) to erase from a position are calculated in _calculateAvailableCollateralToLiquidate

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328

```solidity
function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    AvailableCollateralToLiquidateLocalVars memory vars;

    vars.collateralPrice = collateralPrice; // oracle.getAssetPrice(collateralAsset);
    vars.debtAssetPrice = debtAssetPrice; // oracle.getAssetPrice(debtAsset);

    vars.collateralDecimals = collateralReserve.configuration.getDecimals();
    vars.debtAssetDecimals = debtReserveCache.reserveConfiguration.getDecimals();

    unchecked {
      vars.collateralAssetUnit = 10 ** vars.collateralDecimals;
      vars.debtAssetUnit = 10 ** vars.debtAssetDecimals;
    }

    // This is the base collateral to liquidate based on the given debt to cover
>>  vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
>>    vars.debtAmountNeeded = (
>>      (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
>>    ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }

    if (liquidationProtocolFeePercentage != 0) {
      vars.bonusCollateral = vars.collateralAmount - vars.collateralAmount.percentDiv(liquidationBonus);
      vars.liquidationProtocolFee = vars.bonusCollateral.percentMul(liquidationProtocolFeePercentage);
      return (vars.collateralAmount - vars.liquidationProtocolFee, vars.debtAmountNeeded, vars.liquidationProtocolFee);
    } else {
      return (vars.collateralAmount, vars.debtAmountNeeded, 0);
    }
  }
```

It is evident that `debtToCover` and `userCollateralBalance` are in shares from the previous code

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264

```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
>>  uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L117

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

```solidity
  function executeLiquidationCall(
    ...
  ) external {
    ...
>>  (vars.userDebt, vars.actualDebtToLiquidate) = _calculateDebt(
      // vars.debtReserveCache,
      params,
      vars.healthFactor,
      balances
    );
    ...

>>  vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;

    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
>>    vars.actualDebtToLiquidate,
>>    vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
```

We believe the calculation for `vars.baseCollateral` and `vars.debtAmountNeeded` is wrong

```solidity
>>  vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
>>    vars.debtAmountNeeded = (
>>      (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
>>    ).percentDiv(liquidationBonus);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }
```

because one shares of debt is not equal to one shares of supply collateral, because the `borrowIndex` of the debt is different than the `liquidityIndex` of the supply collateral. The above calculation is assuming one shares of debt is equal to one shares of supply collateral.

### Internal pre-conditions

A position is liquidated.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

- In case of `vars.maxCollateralToLiquidate > userCollateralBalance`, the debt deduction will be wrong.
- In case of `vars.maxCollateralToLiquidate <= userCollateralBalance`, the liquidation rewards will be wrong.

### PoC

In this PoC, `tokenA` is a collateral token, `tokenB` is a debt token.

Run command: `forge test --match-path test/PoC/PoC.t.sol -vv`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {PoolSetup} from 'test/forge/core/pool/PoolSetup.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is PoolSetup {
  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  function setUp() public {
    _setUpPool();
  }

  function testPoC() external {
    uint256 mintAmount = 3 ether;
    uint256 borrowAmount = 1 ether;

    _mintAndApprove(alice, tokenA, mintAmount, address(pool));

    _mintAndApprove(bob, tokenB, mintAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenB), bob, borrowAmount, 0);

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, mintAmount, 0);
    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);
    vm.stopPrank();

    skip(10 days);

    pool.forceUpdateReserves();

    console.log("borrowIndex of tokenB: %e", pool.getReserveData(address(tokenB)).borrowIndex);
    console.log("liquidityIndex of tokenA: %e", pool.getReserveData(address(tokenA)).liquidityIndex);
  }
}
```

Logs:
```bash
  borrowIndex of tokenB: 1.01018853909204689720779463e27
  liquidityIndex of tokenA: 1e27
```

`borrowIndex` of the debt token is different than `liquidityIndex` of the collateral token.

### Mitigation

Fix the conversion from debt shares to supply collateral shares

```diff
-   vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
+   uint256 debtAssets = debtToCover.rayMul(debtReserveCache.nextBorrowIndex);
+   vars.baseCollateral = ((vars.debtAssetPrice * debtAssets * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
+   vars.baseCollateral = vars.baseCollateral.rayDiv(collateralReserve.liquidityIndex);

    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

    if (vars.maxCollateralToLiquidate > userCollateralBalance) {
      vars.collateralAmount = userCollateralBalance;
-     vars.debtAmountNeeded = (
-       (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
-      ).percentDiv(liquidationBonus);
+     uint256 collateralAssets = vars.collateralAmount.rayMul(collateralReserve.liquidityIndex);
+     vars.debtAmountNeeded = (
+       (vars.collateralPrice * collateralAssets * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
+     ).percentDiv(liquidationBonus);
+     vars.debtAmountNeeded = vars.debtAmountNeeded.rayDiv(debtReserveCache.nextBorrowIndex);
    } else {
      vars.collateralAmount = vars.maxCollateralToLiquidate;
      vars.debtAmountNeeded = debtToCover;
    }
```