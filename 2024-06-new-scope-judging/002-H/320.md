Clever Ebony Halibut

High

# Incorrect Calculation of Collateral During Liquidation in `LiquidationLogic` leads to bad debts

## Summary

The `_calculateAvailableCollateralToLiquidate` function in the `LiquidationLogic` contract incorrectly calculates the collateral amount by using shares instead of assets, potentially leading to protocol insolvency.

## Vulnerability Detail

The vulnerability arises from a unit mismatch when calculating collateral during liquidation:

1. User's collateral balance is fetched in shares:

```js
  function executeLiquidationCall(
   mapping(address => DataTypes.ReserveData) storage reservesData,
   mapping(uint256 => address) storage reservesList,
   mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
   mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
   mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
   DataTypes.ExecuteLiquidationCallParams memory params
  ) external {
   ---
   // NOTE userCollateralBalance = supply shares @audit-issue: this is in shares not assets
   vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```

2. This share-based balance is then passed to `_calculateAvailableCollateralToLiquidate`:

```js
_calculateAvailableCollateralToLiquidate(
  // ... other params ...
  vars.userCollateralBalance // This is in shares, but should be in assets
  // ... other params ...
);
```

3. Inside `_calculateAvailableCollateralToLiquidate`, the share-based balance is compared with asset-based calculations:

```js
vars.baseCollateral =
  (vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit) /
  (vars.collateralPrice * vars.debtAssetUnit);

vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);

// @audit-issue wrong comparison here , as userCollateralBalance is in shares, so a liquidator can't liquidate the whole position even with borrower have the amount
// needed to cover liquidation , this because shares are always more in value than assets , thus baddebt
if (vars.maxCollateralToLiquidate > userCollateralBalance) {
  vars.collateralAmount = userCollateralBalance;
  vars.debtAmountNeeded = (
    (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) /
    (vars.debtAssetPrice * vars.collateralAssetUnit)
  ).percentDiv(liquidationBonus);
} else {
  vars.collateralAmount = vars.maxCollateralToLiquidate;
  vars.debtAmountNeeded = debtToCover;
}
```

This mismatch leads to undervaluation of the collateral to be seized and cap the amount of asset in collateral to the numbers of shares, as shares are typically worth more than their corresponding assets due to accrued interest.

- another thing to cnsider The code may incorrectly mark a user's collateral as fully liquidated and remove it from user collateral map while a significant value remains. This occurs because the comparison is made between asset amounts and share amounts ,which are not equivalent , which lead to this user able to withdraw their collateral , even though he have bad debt left : 

```js
   if (vars.actualCollateralToLiquidate + vars.liquidationProtocolFeeAmount == vars.userCollateralBalance) {
     userConfig.setUsingAsCollateral(collateralReserve.id, false);
     emit PoolEventsLib.ReserveUsedAsCollateralDisabled(params.collateralAsset, params.position);
   }
```

For example:

- If a user has 100 shares that are worth 200 assets (liquidityindex = 2):
  1. The liquidator can liquidate a maximum of 100 assets, despite the user having 200 assets worth of collateral.
  2. When 100 assets are liquidated, this collateral will be removed from the user's configuration map.
  3. As a result, the user can withdraw the remaining 100 assets worth of collateral, even he leave bad debt.

This scenario demonstrates how the mismatch between share and asset calculations can lead to incomplete liquidations and exploitation of the system.

## Impact

1. Incorrect liquidation amounts: Liquidators will not be able to liquidate the full required amount.
2. Accumulation of bad debt: The protocol will be unable to fully recover borrowed assets.
3. Premature collateral removal: User's collateral might be incorrectly marked as fully liquidated when significant value remains.

## Code Snippet

- [executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136-L148)
- [remove assets from user map](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L156-L159)
- [calculateAvailableCollateralToLiquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L356-L361)

## Tool used

Manual Review

## Recommendation

To fix this issue we need to convert the `userCollateralBalance` to assets as this is needed to compute the correct amount of collateral to liquidate.

```diff
function executeLiquidationCall(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
    mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
    DataTypes.ExecuteLiquidationCallParams memory params
  ) external {
-------
--    vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
++    vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares.getSupplyBalance(supplyIndex);
```