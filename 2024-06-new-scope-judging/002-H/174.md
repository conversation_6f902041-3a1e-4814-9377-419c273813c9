Mythical Alabaster Snake

High

# When liquidating debt and collateral amount params are used in shares instead of in amounts

when liquidating, params like user debt and actualLiquidatable dont account for index 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273

the actual user debt should be
```solidity
 uint256 userDebt = balances[params.debtAsset][params.position].debtShares * index 
```
same for collateral 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

when collateralAmount,debtAmountNeeded are calculated, they are calculated using this amounts 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L373

hence not accounting for interest accrued from either borrowing or lending 

here is an example of how it can be exploited assume index = 1.1 for bioth borrows and supply 
bob deposit 1eth at 4000 price , ltv is 75% and liquidation threshold is 80%
bob borrows 3000 dai 
bob now has 9.0909091e+17 supply eth shares and 2.7272727e+21 debt dai shares 
eth price drops to 3500 making bob  bob max liquidatable 

walking through the code 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273

userDebt =2.7272727e+21
3500 * .8 / 3000 = 0.933 which is less than 0.95 so bob is max liquidatable hence 
actualDebtToLiquidate = userDebt = 2.7272727e+21

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L134
userCollateralBalance = 9.0909091e+17  
 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L138-L148
    
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L373

  assume dai is base 
  base collateral = 1e18 * 2.7272727e+21 * 1e18  / (3500e18 * 1e18) =  7.7922077e+17 eth 
  liquidation bonus of 105% = maxCollateralToLiquidate = 8.1818181e+17
  8.1818181e+17 is < 1e18 hence debtAmountNeeded = debtToCover = full debt, collateralAmount = maxCollateralToLiquidate
  
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150C1-L152C6

  this condition therefore passes and bobs config is set as no longer borrowing from this reserve hence bob is effectively not borrowing from this reserve 
  
  https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161C5-L161C21
  
  https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247
  
  for simplicity sake, assume index now at 1.102
  effectively leaving bob with 2.7272727e+21 - (2.7272727e+21/1.102) = 2.5243359e+20 debt tokens 
  
  https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L207-L230
  
  for simplicity assume same index of  1.102 
  bob is left with 9.0909091e+17  - ( 8.1818181e+17/1.102)= 1.6663918e+17 eth shares 
  
  after all is set and done, bobs collateral is worth 583 dai and bobs debt is 252 dai, issue is bob can no longer be liquidated as he is technically not borrowing from any reserve 
  
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/GenericLogic.sol#L124-L128

bobs config has been set to not borrowing so his debt is technically zero but he can still withdraw 
bob is left with the 3000 dai he borrowed and 583 dai in eth 
bob deposited one eth now worth 3500 dai 
bobs dai value = 3000 + 583 = 3583 
bob is left with 83 dai profit and the contract with 83 dai loss