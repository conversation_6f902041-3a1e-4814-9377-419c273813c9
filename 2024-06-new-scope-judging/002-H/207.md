Joyous Cedar Tortoise

High

# Not converting user debt from shares to assets in LiquidationLogic

## Summary
Incorrect calculation leads to funds stolen from the protocol.

## Vulnerability Detail
In [`LiquidationLogic._calculateDebt()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259), it gets `userDebt`  via `balances.debtShares` but does not convert it to assets by multiplying by `getNormalisedDebt()`

```solidity
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
```

Then, in the check [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L150-L152), the asset is removed from the list of borrowed assets if the liquidated debt is equal to `vars.userDebt`

```solidity
if (vars.userDebt == vars.actualDebtToLiquidate) {
    userConfig.setBorrowing(debtReserve.id, false);
}
```

Since `vars.userDebt` is smaller than the true debt, the debt repaid will be less than the total borrowed debt, however the remaining debt is incorrectly cleared.
## Impact
User’s debt is cleared before it has been completely paid off. The user will not have to repay the debt, so these are effectively stolen funds from the suppliers.

## Code Snippet

## Tool used

Manual Review

## Recommendation
Convert userDebt to assets in the following way:
```diff
uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
+if (userDebt != 0) userDebt = userDebt.rayMul(reserve.getNormalizedDebt());
```