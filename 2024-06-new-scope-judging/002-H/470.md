Ancient Gingerbread Rooster

High

# `executeLiquidationCall` utilizes wrong debt and collateral balance, disrupting whole liquidation process

## Summary
In `executeLiquidationCall` function, there's an error in calculation of the debt and collateral balance of a position, which would ultimately disrupt the whole liquidiation process.

## Vulnerability Detail
In `LiquidiationLogic.executeLiquidationCall` function, `userDebt` and `userCollateralBalance` are calculated in order to execute a successful liquidation.

`userDebt` is calculated in `_calculateDebt` as follows:
```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
>   uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
  ...
```

`userCollateralBalance` is calculated as follows:
```solidity
>   vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
```

These calculations are wrong because they represent `debtShares` and `supplyShares` of the position respectively while the function expect them to be actual amount of debt, collateral assets.
This becomes evident as these values are used to determine actual debt and collateral amount to be liquidiated in `_calculateAvailableCollateralToLiquidate`.

## Impact
As a result of this error, the liquidation process becomes completely flawed, leading to unexpected outcomes.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L134

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L264

## Tool used
Manual Review

## Recommendation
Calculate the correct `userDebt` and `userCollateralBalance` by adopting this formula: `balance = shares * index`. `liquidityIndex` and `borrowIndex` should be utilized for respective asset reserves.