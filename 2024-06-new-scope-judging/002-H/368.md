Innocent Chartreuse Parrot

Medium

# Collateral shares are mistaken for collateral assets during liquidations

## Summary
Collateral shares are mistaken for collateral assets during liquidations

## Vulnerability Detail
During liquidations, the collateral shares of the violator are mistakenly used to represent their available collateral balance:

[LiquidationLogic::executeLiquidationCall](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136-L157)
```solidity
    vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares; // @audit: shares mistaken for assets

    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );
...
    // If the collateral being liquidated is equal to the user balance,
    // we set the currency as not being used as collateral anymore
    if (vars.actualCollateralToLiquidate + vars.liquidationProtocolFeeAmount == vars.userCollateralBalance) {
      userConfig.setUsingAsCollateral(collateralReserve.id, false);
```

Therefore, there are scenarios in which the undervalued `userCollateralBalance` is used as the total collateral to seize. This results in the protocol believing that all collateral was seized and removes the reserve from the violator's config, allowing them to freely withdraw any remaining collateral. 

Note that the collateral to seize is calculated with respect to the debt being repaid and so the liquidator will always get a "correct" collateral value proportional to the debt being repaid and liquidation bonus. However, in the cases where the position is underwater and the debt being repaid corresponds to a collateral value greater than the undervalued `userCollateralBalance` (available collateral), only an undervalued amount of the user's collateral will be seized:

[LiquidationLogic::_calculateAvailableCollateralToLiquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L352-L360)
```solidity
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);


    vars.maxCollateralToLiquidate = vars.baseCollateral.percentMul(liquidationBonus);


    if (vars.maxCollateralToLiquidate > userCollateralBalance) { // @audit: position underwater so debt repaid corresponds to more collateral than available
      vars.collateralAmount = userCollateralBalance; // @audit: only `shares` amount of collateral seized, not all collateral assets
      vars.debtAmountNeeded = ( // @audit: therefore, less debt repaid
        (vars.collateralPrice * vars.collateralAmount * vars.debtAssetUnit) / (vars.debtAssetPrice * vars.collateralAssetUnit)
      ).percentDiv(liquidationBonus);
```

As we can see above, this results in only `shares` amount of the violator's collateral being seized and therefore less debt being repaid. As a result, the underwater violator incorrectly retains a portion of their collateral and the bad debt created is larger than it is supposed to be. 

## Proof of Concept
Modify the following lines in `PoolLiquidationTests.t.sol` and run test with `forge test --mc PoolLiquidationTest --mt testLiquidationSimple1 -vvv`:

```diff
diff --git a/./test/forge/core/pool/PoolLiquidationTests.t.sol b/./test/forge/core/pool/PoolLiquidationTests.t.sol
index e770be3..fc3bd2e 100644
--- a/./test/forge/core/pool/PoolLiquidationTests.t.sol
+++ b/./test/forge/core/pool/PoolLiquidationTests.t.sol
@@ -31,19 +31,79 @@ contract PoolLiquidationTest is PoolSetup {
   }

   function testLiquidationSimple1() external {
+    // preserve state
+    uint256 snapshot = vm.snapshot();
+
+    // -- ideal state: liquidityIndex for collateral is 1 -- //
+    assertEq(pool.getReserveNormalizedIncome(address(tokenA)), 1e27);
+
     _generateLiquidationCondition();
     (, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);

     vm.startPrank(bob);
     vm.expectEmit(true, true, true, false);
     emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
-    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 10 ether);
+    pool.liquidateSimple(address(tokenA), address(tokenB), pos, type(uint256).max);

     vm.stopPrank();

-    (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);
+    (uint256 totalCollateralBaseExpected, uint256 totalDebtBaseExpected,,,,) = pool.getUserAccountData(alice, 0);

-    assertTrue(totalDebtBase > totalDebtBaseNew);
+    assertTrue(totalDebtBase > totalDebtBaseExpected);
+
+    // all collateral seized in ideal state
+    uint256 remainingSupplySharesExpected = pool.getBalanceRaw(address(tokenA), alice, 0).supplyShares;
+
+    assertEq(totalCollateralBaseExpected, 0);
+    assertEq(remainingSupplySharesExpected, 0);
+
+    emit log_named_uint("collateral base remaining in ideal state", totalCollateralBaseExpected);
+    emit log_named_uint("remaining supply shares in ideal state", remainingSupplySharesExpected);
+    emit log_named_uint("debt base remaining in ideal state (bad debt)", totalDebtBaseExpected);
+
+    // revert to preserved state
+    console.log("");
+    vm.revertTo(snapshot);
+
+    // -- problematic state: liquidityIndex for collateral is above 1 -- //
+    address user = address(0x69420);
+    _mintAndApprove(user, tokenA, 100e18, address(pool));
+    vm.startPrank(user);
+    pool.supplySimple(address(tokenA), user, 100e18, 0);
+    pool.borrowSimple(address(tokenA), user, 70e18, 0);
+    vm.stopPrank();
+
+    vm.warp(block.timestamp + 10 days);
+    assertGt(pool.getReserveNormalizedIncome(address(tokenA)), 1e27);
+
+    // update oracle answers to avoid stale price error for tests
+    oracleA.updateAnswer(oracleA.latestAnswer());
+    oracleB.updateAnswer(oracleB.latestAnswer());
+
+    _generateLiquidationCondition();
+
+    vm.startPrank(bob);
+    vm.expectEmit(true, true, true, false);
+    emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
+    pool.liquidateSimple(address(tokenA), address(tokenB), pos, type(uint256).max);
+
+    vm.stopPrank();
+
+    (uint256 totalCollateralBaseActual, uint256 totalDebtBaseActual,,,,) = pool.getUserAccountData(alice, 0);
+    uint256 remainingSupplySharesActual = pool.getBalanceRaw(address(tokenA), alice, 0).supplyShares;
+
+    // collateral removed from violator's config
+    assertEq(totalCollateralBaseActual, 0);
+
+    // violator still has collateral that was not seized (protocol thinks all collateral was seized)
+    assertGt(remainingSupplySharesActual, 0);
+
+    // therefore, more bad debt created than expected
+    assertGt(totalDebtBaseActual, totalDebtBaseExpected);
+
+    emit log_named_uint("collateral base remaining in problematic state", totalCollateralBaseActual);
+    emit log_named_uint("remaining supply shares in problematic state", remainingSupplySharesActual);
+    emit log_named_uint("debt base remaining in problematic state (bad debt)", totalDebtBaseActual);
   }

   function testLiquidationSimpleRevertWhenCollateralNotActive() external {
@@ -80,6 +140,6 @@ contract PoolLiquidationTest is PoolSetup {

     assertEq(tokenB.balanceOf(alice), borrowAmountB);

-    oracleA.updateAnswer(5e3);
+    oracleA.updateAnswer(0.35e8);
   }
```

Output from logs: 
```js
[PASS] testLiquidationSimple1() (gas: 2258960)
Logs:
  collateral base remaining in ideal state: 0
  remaining supply shares in ideal state: 0
  debt base remaining in ideal state (bad debt): 1666666666

  collateral base remaining in problematic state: 0
  remaining supply shares in problematic state: 2095458418585328922
  debt base remaining in problematic state (bad debt): 1736783445
```

## Impact
Once the `liquidityIndex` is above 1 (inevitable), all future liquidations of underwater positions will produce more bad debt than expected, worsening the insolvency of the protocol. 

Additionally, for normal liquidations (not underwater) it will be impossible to perform full liquidations as only an undervalued amount of the violator's collateral can be seized. This translates to a lower incentive for liquidators. This is a lesser impact than the one above. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

## Tool used

Manual Review

## Recommendation
Calculate the violator's up to date collateral balance properly by multiplying their supply shares by the updated liquidity index:

```solidity
vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares.rayMul(collateralReserve.getNormalizedIncome());
```