Steep Aqua Yak

High

# Wrong collateral balance used in liquidation

## Summary
In liquidation flow, principal supply shares is used, instead of accrued interest supply balance.

## Vulnerability Detail
The supply shares is used as user collateral balance in liquidation `vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares`, which maybe lower than actual collateral balance the user has if there is interest accrued. 
 
## Impact
The maximum amount of collateral to be liquidated will be lower than actual

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L136

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L355-L361
## Tool used

Manual Review

## Recommendation
```diff
-vars.userCollateralBalance = balances[params.collateralAsset][params.position].supplyShares;
+vars.userCollateralBalance = balances[params.collateralAsset][params.position].getSupplyBalance(collateralReserve.nextLiquidityIndex)
```