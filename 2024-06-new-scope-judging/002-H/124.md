Powerful Licorice Elephant

High

# vars.maxCollateralToLiquidate > userCollateralBalance validation is incorrect in the function _calculateAvailableCollateralToLiquidate.

## Summary
when  (vars.maxCollateralToLiquidate > userCollateralBalance) is checked, userCollateralBalance is not converted to the oracle asset based price. As a result , the validation/accounting will be incorrect.


## root cause 
userCollateralBalance is not converted to the oracle asset based price
## Vulnerability Detail
vars.userCollateralBalance is used  as input value(i.e userCollateralBalance) in function _calculateAvailableCollateralToLiquidate.

 vars.baseCollateral is the base collateral to liquidate based on the given debt to cover. It is calculated based on oracle price.

vars.maxCollateralToLiquidate is vars.baseCollateral+liquidation bonus. So vars.maxCollateralToLiquidate value is also based on oracle asset price.

But when  (vars.maxCollateralToLiquidate > userCollateralBalance) is checked, userCollateralBalance is not converted to the oracle asset based price. As a result , the validation/accounting will be incorrect.

## Impact
the validation/accounting will be broken.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L356
## Tool used

Manual Review

## Recommendation
convert userCollateralBalance to the oracle asset based price