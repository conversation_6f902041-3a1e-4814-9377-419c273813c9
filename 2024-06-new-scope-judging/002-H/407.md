Clever Ebony Halibut

High

# Liquidation Function Incorrectly Uses Shares Instead of Actual Debt Amount

## Summary

The `executeLiquidationCall` function in the liquidation logic incorrectly uses debt shares instead of the actual debt amount. This leads to an undervaluation of the debt that can be liquidated and makes it impossible to liquidate the entire debt.

## Vulnerability Detail

The vulnerability lies in the `_calculateDebt` function:

```js
function _calculateDebt(
DataTypes.ExecuteLiquidationCallParams memory params,
uint256 healthFactor,
mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
) internal view returns (uint256, uint256) {
uint256 userDebt = balances[params.debtAsset][params.position].debtShares; // Incorrect: uses shares instead of actual debt
uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;
uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor); // Incorrect: calculates based on shares
uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover; // Mixes shares and asset amounts
return (userDebt, actualDebtToLiquidate);
}
```

The issues in this function are:

1. `userDebt` is set to `debtShares` instead of the actual debt amount.
2. `maxLiquidatableDebt` is calculated using `debtShares`, resulting in an incorrect value.
3. `actualDebtToLiquidate` compares `params.debtToCover` (in asset amount) with `maxLiquidatableDebt` (in shares), leading to incorrect comparisons.

This incorrect calculation propagates through the rest of the liquidation process:

```js
(vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) = _calculateAvailableCollateralToLiquidate(
collateralReserve,
vars.debtReserveCache,
vars.actualDebtToLiquidate, // Incorrect: This is in shares, not asset amount
vars.userCollateralBalance,
vars.liquidationBonus,
IPool(params.pool).getAssetPrice(params.collateralAsset),
IPool(params.pool).getAssetPrice(params.debtAsset),
IPool(params.pool).factory().liquidationProtocolFeePercentage()
);
```

In the `_calculateAvailableCollateralToLiquidate` function, `debtToCover` is used as if it were in asset amount, but it's actually in shares:

```js
vars.baseCollateral = ((vars.debtAssetPrice _ debtToCover _ vars.collateralAssetUnit)) / (vars.collateralPrice * vars.debtAssetUnit);
```

This miscalculation leads to an incorrect `baseCollateral` value and subsequently an incorrect `maxCollateralToLiquidate`.

## Impact

- Full liquidation of a debt is not possible as the `maxLiquidatable` amount is capped to the amount of shares, which is less than the actual asset amount (potentially significantly less).
- In `LiquidationLogic.sol`, when liquidating an amount equal to the `sharesDebt`, the debtAsset is incorrectly removed from the userConfig map as a borrowed asset. This allows the user to withdraw the remaining asset even though they have not collaterall to back it.

```js
// @audit-issue 6: you are comparing an amount of shares (userDebt) with an amount of asset(actualDebtToLiquidate) , which can remove the asset from user borrowed asset
// map , even thought the user still have debt (in case 1shareValue > 1asset)
if (vars.userDebt == vars.actualDebtToLiquidate) {
  userConfig.setBorrowing(debtReserve.id, false);
}
```

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L259-L273

## Tool used

Manual Review

## Recommendation

Update the `_calculateDebt` function to correctly handle the conversion between debt shares to the actual debt amount:

```diff
  function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    uint256 borrowIndex
  ) internal view returns (uint256, uint256) {
-    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;
+    uint256 userDebt = balances[params.debtAsset][params.position].getDebtBalance(borrowIndex);
```

