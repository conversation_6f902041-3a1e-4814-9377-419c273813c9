Shiny Seaweed Mongoose

Medium

# Position that should not be fully liquidated can be fully liquidated due to wrong comparison.

### Summary

According to the [docs](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L68), if the health factor falls below the `CLOSE_FACTOR_HF_THRESHOLD`, only then can the position be fully liquidated.
`CLOSE_FACTOR_HF_THRESHOLD = 0.95`, so if the health factor is equal or greater than 0.95 then the liquidator can only pay 50% of the debt.
But this is not correct due to the wrong comparison in the codebase. Positions with a health factor of 0.95 can be fully liquidated. 

### Root Cause

The root cause of this issue starts from a wrong comparison made in the `_calculateDebt` function, 
the closeFactor determines how much debt the liquidator can cover, in the function below, when the health factor is greater than 0.95,  the liquidator can only cover 50% of the debt, but when it is equal to 0.95 the liquidator can cover 100% of the debt this is wrong because positions with a health factor of 0.95 should only be liquidated partially as explained in the docs.
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L266

```js
function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

@-> uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
}
```

### Internal pre-conditions

A position with a Health Factor of exactly 0.95.

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Position that should not be fully liquidated will be liquidated leading to loss of collateral by the position owner.

### PoC

_No response_

### Mitigation

Effect the following changes.

```diff
function _calculateDebt(
    DataTypes.ExecuteLiquidationCallParams memory params,
    uint256 healthFactor,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances
  ) internal view returns (uint256, uint256) {
    uint256 userDebt = balances[params.debtAsset][params.position].debtShares;

-   uint256 closeFactor = healthFactor > CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;
+   uint256 closeFactor = healthFactor >= CLOSE_FACTOR_HF_THRESHOLD ? DEFAULT_LIQUIDATION_CLOSE_FACTOR : MAX_LIQUIDATION_CLOSE_FACTOR;

    uint256 maxLiquidatableDebt = userDebt.percentMul(closeFactor);

    uint256 actualDebtToLiquidate = params.debtToCover > maxLiquidatableDebt ? maxLiquidatableDebt : params.debtToCover;

    return (userDebt, actualDebtToLiquidate);
}
```
