Clever Ebony Halibut

High

# Attacker Can Steal User Funds by front-runing `supply` with `tokenId==0`

## Summary

The `NFTPositionManager` contract allows users to deposit assets by setting the `tokenId` to 0, indicating that the user wants to supply to the last token ID they own. However, an attacker can manipulate the `tokenId` and redirect the victim's liquidity to a malicious pool they control.

## Vulnerability Detail

The vulnerability lies in the `_supply` function in `NFTPositionManagerSetters.sol`. When a user supplies assets, they can set the `tokenId` to 0 to indicate that they want to supply to the last token ID they own. The relevant code snippet is as follows:

```js
function _supply(AssetOperationParams memory params) internal nonReentrant {
if (params.amount == 0) revert NFTErrorsLib.ZeroValueNotAllowed();
if (params.tokenId == 0) {
if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();
params.tokenId = _nextId - 1;
}
// ...
}
```

The issue arises when a user supplies assets with `tokenId` set to 0. The `_supply` function checks if the caller (`msg.sender`) is the owner of the last minted token ID (`_nextId - 1`). If the caller is the owner, the function proceeds to supply the assets to the pool associated with the last minted token ID.as each token id when minted assign to a specific pool :

```js
  function mint(address pool) external returns (uint256 tokenId) {
    require(factory.isPool(pool), 'not a pool');
    tokenId = _nextId;
    _nextId++;
 >>   _positions[tokenId].pool = pool;
    _mint(msg.sender, tokenId);
  }
```

An attacker can exploit this vulnerability by performing the following steps:

1. The attacker deploys a malicious pool with manipulated configurations, such as a malicious oracle or a 0 LTV...ect
2. The attacker calls the `mint` function in `NFTPositionManager.sol` to mint a new token ID, passing the address of their malicious pool.
3. The attacker transfers the minted token ID to the victim using the `transfer` function.
4. The victim attempts to supply assets with `tokenId` set to 0, assuming they are supplying to their own pool.
5. The `_supply` function checks the ownership of the last minted token ID (`_nextId - 1`), which now belongs to the victim due to the attacker's transfer.
6. The victim's assets are supplied to the attacker's malicious pool.
7. The attacker can then steal or lock the victim's funds by manipulating their own pool's configurations.

- an example of how an attacker can steal user funds , is using a malicious oracle , for some wothrless token and supply it to his pool , and borrow the whole amount the victim supplied

## Impact

- The vulnerability allows an attacker to redirect a victim's liquidity to a malicious pool, then The attacker can steal or lock the victim's funds .

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L44-L49

## Tool used

Manual Review

## Recommendation

- it is recommended to remove the code block that allows users to supply assets with `tokenId` set to 0. Users should explicitly provide the desired `tokenId` when supplying assets to ensure they are interacting with their intended pool.