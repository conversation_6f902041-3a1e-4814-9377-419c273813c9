Acrobatic Rainbow Grasshopper

High

# Malicious users can get the funds of a user stuck in a certain case

## Summary
Malicious users can get the funds of a user stuck in a certain case
## Vulnerability Detail
Upon different operations like supplying using the NFT position manager, we have this piece of code:
```solidity
if (params.tokenId == 0) {
      if (msg.sender != _ownerOf(_nextId - 1)) revert NFTErrorsLib.NotTokenIdOwner();
      params.tokenId = _nextId - 1;
    }
```
If the user has provided a token ID of 0, we get the last minted NFT ID and store that in `params.tokenId`. Before that we have a check that guarantees that the `msg.sender` is the owner of the NFT, this is to avoid a critical issue reported in the Halborn audit where a malicious user can frontrun the supply by minting his own token and thus, the victim would supply to his position as that is the last one. However, the current implementation still allows for a serious issue:
1. <PERSON><PERSON><PERSON>'s token ID is the last one and he supplies using an ID of 0
2. Malicious user frontruns him and does the following:
     - Deploy a pool where one of the allowed assets is the asset the victim is supplying
     - As the pool params, add a hook address which always reverts upon withdrawing
     - Mint an NFT using the newly created pool address
     - Transfer the NFT to the victim
3. Now, the check will pass as the victim is the owner of the NFT and he will supply to the newly deployed malicious pool
4. Upon withdrawing, he will have his funds stuck as the function will revert due to the hook

I believe hooks were mentioned to be out-of-scope, however this issue does not really rely on the hooks functionality, they are just the finishing touch and one of the ways the function can revert, there are numerous different ways for the withdrawal to revert if the deployer of the pool wants to do so and they do not rely on hooks. For example, he can simply add a malicious address as the oracle that returns `type(uint256).max`. Then, we would always revert here due to overflow:
```solidity
uint256 balance = (_balance.supplyShares.rayMul(normalizedIncome)) * assetPrice;
```
## Impact
Malicious users can get the funds of a user stuck in a certain case
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/positions/NFTPositionManagerSetters.sol#L46-L48
## Tool used

Manual Review

## Recommendation
You can add the expected pool as an input and check whether they are equal if you want to keep that functionality