Careful Fleece Pike

Medium

# Missing update for the collateral reserve will allow liquidator to liquidate a healthy position

### Summary

The missing update for collateral reserve will cause the `healthFactor` to be calculated basing on a stale `liquidityIndex` of the collateral reserve, which will allow liquidator to liquidate a healthy position.

### Root Cause

The `liquidityIndex` of a reserve is only updated when `updateState` is called. The `liquidityIndex` will increase over time. At the beginning of the liquidation process, `updateState` is not called for `collateralReserve`, which will cause the `liquidityIndex` of `collateralReserve` will be less than the actual value

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L94

The stale `liquidityIndex` is later used for calculating the `healthFactor` of the position

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L110

Since the `liquidityIndex` is less than the actual value, the `totalCollateralInBaseCurrency` is less than the actual value, which could cause a healthy position to be liquidatable.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

Liquidate a position.

### Impact

A healthy position will be liquidatable, which will benefit the liquidator, and unfair for the borrower.

### PoC

_No response_

### Mitigation

Update the collateral reserve at the beginning of the liquidation process

`Pool.sol`

```diff
  function liquidate(address collat, address debt, bytes32 pos, uint256 debtAmt, DataTypes.ExtraData memory data) public {
+   forceUpdateReserve(collat);
    _liquidate(collat, debt, pos, debtAmt, data);
  }
```