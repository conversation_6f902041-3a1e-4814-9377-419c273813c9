Polished Iris Antelope

High

# Outdated ```supplyIndex``` usage during ```getBalanceByPosition()``` call on ```CuratedVault::totalAssets``` leads to wrong ```lastTotalAssets```.

## Summary
```lastTotalAssets``` variable of ```CuratedVaults``` is not reflecting the present state of the total assets that belong to the ```CuratedVault``` since it uses outdated ```supplyIndexes``` to calculate the assets from ```Pools```.

## Vulnerability Detail
Every time a deposit or a withdraw is made on a ```CuratedVault```, ```_accrueFee()``` is called to 1. update ```lastTotalAssets``` and 2. charge fee on interest generated. We can the implementation here :
```solidity
  function _accrueFee() internal returns (uint256 newTotalAssets) {
    uint256 feeShares;
    (feeShares, newTotalAssets) = _accruedFeeShares();
    if (feeShares != 0) _mint(feeRecipient, feeShares);
    emit CuratedEventsLib.AccrueInterest(newTotalAssets, feeShares);
  }

  function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
    newTotalAssets = totalAssets();

    uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets);
    if (totalInterest != 0 && fee != 0) {
      // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
      uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
      // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
      // that total assets is already increased by the total interest (including the fee assets).
      feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
    }
  }

   function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
@>      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368C1-L372C4)

In this whole prodecure, the ```getBalanceByPosition``` call on every ```Pool``` is crucial to return the actual present assets that belong to the ```CuratedVault```. However, if we examine the ```getBalanceByPosition``` function, we will understand that this is not the case :
```solidity
  function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
  }
```

As we can see, the ```liquidityIndex``` passed is not the present one but the last index updated which is when the last action on this reserve happened. This, totally, doesn't reflect the present state of the assets belonging to the ```CuratedVault``` and this results to ```lastTotalAssets``` to be outdated.

## Impact
The use of outdated ```liquidityIndexes``` during the calculation of ```lastTotalAssets``` results in incorrect minting of shares for the ```CuratedVault``` admins when ```_accrueFee()``` is called. Since ```lastTotalAssets``` is not accurately reflecting the current state of the assets, the interest fee charged to the admins is either over or under-calculated, leading to potential financial discrepancies. This could cause loss of fees or incorrect distribution of shares, impacting the vault's overall accounting and fairness. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368C1-L372C4

## Tool used
Manual Review

## Recommendation
Consider calling the ```forceUpdateReserve()``` function on the every ```Pool``` on ```withdrawQueue``` during ```totalAssets()``` :
```diff
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
+     withdrawQueue[i].forceUpdateReserve(asset());
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```