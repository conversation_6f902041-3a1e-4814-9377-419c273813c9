Clever Ebony Halibut

High

# Incorrect Fee Accrual in `CuratedVault` Contract Lead to Incorrect Shares Accounting

# Summary

The `CuratedVault` contract relies on the `_accrueFee()` function to accrue fees based on the total assets owned by the vault. However, the calculation of total assets is incorrect due to the use of outdated `liquidityIndex` values stored in the `Pool` contract. This leads to an underestimation of the vault's total assets, resulting in incorrect minting and withdrawal of shares. Attackers can exploit this vulnerability to steal funds from the vault by depositing a large amount and immediately withdrawing, taking advantage of the inconsistency between the minted shares and the actual assets.

## Vulnerability Detail

- The `CuratedVault` contract is designed to accrue fees based on the total assets it owns across different pools. The accrual of fees is crucial for maintaining the integrity of the vault and ensuring that the minted shares accurately represent the underlying assets. Before each action, such as minting or withdrawing shares, the vault needs to accrue fees to reflect the latest state of its assets.

- The `_accrueFee()` function in `CuratedVaultGetters.sol` is responsible for calculating the total assets of the vault, including any accrued fees. This function calls `totalAssets()`, which iterates through all pools in the `withdrawQueue`. For each pool, it invokes `getBalanceByPosition()` to determine the vault's balance in that specific pool. The sum of these balances represents the vault's total assets.

```js
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```

- However, the `getBalanceByPosition()` function in the `PoolGetters.sol` contract uses the `liquidityIndex` value stored in the `_reserves` mapping to calculate the vault's balance. The problem is that this stored `liquidityIndex`  is out-dated and doesn't reflect the actual `liquidityIndex` , leading to an incorrect calculation of the vault's balance (less than it should).

```js
function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
}

// @note it's wrong how they calculate balance , but we assume it's correct for now
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
return self.supplyShares + increase;
}
```

- As a result, the `_accrueFee()` function in `CuratedVaultGetters.sol` underestimates the total assets owned by the vault. This underestimation leads to incorrect minting of shares when users deposit/withdraw assets into the vault which can be exploited to steal funds from the vault.

Here's an example scenario to illustrate the exploit:

1. Assume the stored `liquidityIndex` is $1$, the latest `liquidityIndex` is $1.5$, the vault has $10,000 PoolShares$, and $10,000 VaultShares$ are minted to userA.
2. A malicious userB deposits $100,000$ assets into the vault.

3. The `_accrueFee()` function is called, and `newTotalAssets` is calculated using the outdated `liquidityIndex` value of $1$. As a result, `newTotalAssets` is underestimated and returns a value of $10,000$ (we assume feeShares = $0$ for simplicity).
4. Based on the underestimated `newTotalAssets`, userB is minted $100,000 VaultShares$ ($100,000 * 10,000 / 10,000 = 100,000$). and the vault is minted $66666 PoolShares$ ($100000/1.5)
5. Immediately after the supply function, userB initiates a withdrawal. During the previous supply process, the `liquidityIndex` is updated to the latest value of $1.5$.

6. With the updated index, `newTotalAssets` is calculated as $(10,000 + 66666) * 1.5 =  114999$.
7. userB's assets to withdraw are calculated based on their shares and the updated `newTotalAssets`: $100000 * 114999 / 110000 = 104544$.
8. As a result, userB was able to withdraw $104544$ assets, effectively stealing $4544$ assets from the vault.
   The attacker exploits this vulnerability by depositing a large amount of assets and immediately withdrawing, taking advantage of the underestimated total assets during the minting process and the updated total assets during the deposit process. This discrepancy allows them to withdraw more assets than they initially deposited in one tx, draining funds from other users in the vault.

## Impact

The incorrect accrual of fees in the `CuratedVault` contract leads to:

1. Inaccurate accounting of the vault's total assets.
2. Theft of yeild by attackers exploiting the vulnerability.

## Code Snippet

- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L185-L197

## Tool used

Manual Review

## Recommendation

To mitigate this vulnerability, ensure that the `CuratedVault` contract uses updated `liquidityIndex` values when accruing fees by either:

1. Forcing an update of the indexes before accruing fees.
2. Modifying the `getBalanceByPosition()` function to return the latest balance based on the updated `liquidityIndex`.