Steep Aqua Yak

Medium

# Lender withdrawals burn more vault shares than expected

## Summary
The function `CuratedVault#withdraw()` calculates share amount to be burnt based on the current total asset, not including interest accrued from Pool, causing withdrawals burn more shares than expected
The function `CuratedVault#redeem()` is similar that returns less asset amount than expected with the same amount of shares to redeem

## Vulnerability Detail
The function `CuratedVault#withdraw()` first calculates share to burn based on [current total asset supplied to pool](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368-L372). In case there is no actions to update Pool reserve (such that `supply()/withdraw()/repay()/borrow()` or `forceUpdateReserve`), then the function `CuratedVault#totalAssets()` will not include the interest accrued on Pool (but not yet updated to reserve state). 
So the [`newTotalAssets`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L186) is lower than actual, which causes the [burnt share](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L348) to be higher than expected. The `redeem` flow is similar such that it will returns less assets than expected.

#### PoC
Add the test below to test file `ERC4626Test.sol`
```solidity
  function testWithdrawBurnMoreShares() public {
    _setCap(allMarkets[0], 5000 ether);
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    uint256 supplyAmount = 100 ether;
    loanToken.mint(supplier, 1000 ether);


    vm.startPrank(supplier);
    vault.deposit(supplyAmount, supplier);
    vm.stopPrank();

    loanToken.mint(borrower, 1000 ether);
    collateralToken.mint(borrower, 200 ether);

    vm.startPrank(borrower);
    collateralToken.approve(address(pool), type(uint256).max);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 100 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 30 ether, 0);

    skip(10 days);
    // HERE: interest accrued but state is not updated yet
    uint id = vm.snapshot();
    vm.startPrank(supplier);
    uint256 shareBurnt1 = vault.withdraw(50 ether, supplier, supplier);

    vm.revertTo(id);
    allMarkets[0].forceUpdateReserve(vault.asset()); // force update reserve
    uint256 shareBurnt2 = vault.withdraw(50 ether, supplier, supplier);

    assertGt(shareBurnt1, shareBurnt2);
  }
```

Run the test and console shows:
```bash
Ran 1 test for test/forge/core/vaults/ERC4626Test.sol:ERC4626Test
[PASS] testWithdrawBurnMoreShares() (gas: 1175595)
```

## Impact
Some lenders (who withdraw from vault before reserve is updated on Pool contract) have to burn more share than expected. With redeem flow, it is similar that lenders receive less assets than expected

## Code Snippet

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L344-L348

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L176-L181

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L185-L195

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368-L372

## Tool used

Manual Review

## Recommendation
Should call to `Pool#forceUpdateReserve` so that the function `CuratedVault#totalAssets()` can account for interests