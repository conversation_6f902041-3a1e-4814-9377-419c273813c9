Innocent Chartreuse Parrot

Medium

# Pending interest from pools not accounted for during vault operations

## Summary
Pending interest from pools not accounted for during vault operations

## Vulnerability Detail
The `totalAssets` function in vaults does not update the pool reserves and uses a getter function which uses the cached `liquidityIndex` instead of an up to date `liquidityIndex`:

[CuratedVault::totalAssets](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368-L370)
```solidity
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId); // @audit: function call does not take into account pending interest
```

[PoolGetters::getBalanceByPosition](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L47-L48)
```solidity
  function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex); // @audit: calculation does not use updated liquidityIndex
```

This results in an underestimation of `totalAssets` in the vault when pools have pending interest.

## Proof of Concept

Place the following test inside `ERC4626Test.sol` and run with `forge test --mc ERC4626Test --mt testPendingInterestNotAccountedForInVault -vvv`:

```solidity
  function testPendingInterestNotAccountedForInVault() public {
    _setCap(allMarkets[0], 1000 ether);

    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    loanToken.mint(supplier, 10 ether);
    collateralToken.mint(borrower, 20 ether);

    vm.prank(supplier);
    allMarkets[0].supplySimple(address(loanToken), supplier, 10 ether, 0);

    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 20 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 5 ether, 0);
    vm.stopPrank();
    
    // preserve state
    address user = address(0x69420);
    _mintAndApprove(user, loanToken, 1 ether, address(allMarkets[0]));
    assertEq(allMarkets[0].getReserveNormalizedIncome(address(loanToken)), 1e27);
    uint256 snapshot = vm.snapshot();

    // --- user deposits directly into pool, interest accrues in pool, user withdraws from pool --- //

    // deposits into pool
    vm.startPrank(user);
    allMarkets[0].supplySimple(address(loanToken), user, 1 ether, 0);
    vm.stopPrank();
    
    // pool accumulates interest
    vm.warp(block.timestamp + 10 days);
    assertGt(allMarkets[0].getReserveNormalizedIncome(address(loanToken)), 1e27);

    // user withdraws from pool
    DataTypes.SharesType memory burnt;
    vm.prank(user);
    burnt = allMarkets[0].withdrawSimple(address(loanToken), user, type(uint256).max, 0);
    uint256 assetsWithdrawnViaPool = burnt.assets;
    emit log_named_uint("assets withdrawn from pool", assetsWithdrawnViaPool);
    
    // -- user deposits into vault, interest accrued in pool, user withdraws from vault -- //

    // deposits into vault
    vm.revertTo(snapshot);
    vm.startPrank(user);
    loanToken.approve(address(vault), type(uint256).max);
    uint256 shares = vault.deposit(1 ether, user);
    vm.stopPrank();
    
    // pool accumulates interest
    vm.warp(block.timestamp + 10 days);
    assertGt(allMarkets[0].getReserveNormalizedIncome(address(loanToken)), 1e27);

    // user withdraws from vault
    vm.prank(user);
    uint256 assetsWithdrawnViaVault = vault.withdraw(shares, user, user);

    emit log_named_uint("assets withdrawn from vault", assetsWithdrawnViaVault);
    
    // user receives less assets when withdrawing from the vault 
    assertLt(assetsWithdrawnViaVault, assetsWithdrawnViaPool);
  }
```

## Impact
When configured pools have pending interest, depositors in Curated Vaults will receive a better exchange rate than they are supposed to, while redeemers will receive a worse exchange rate. This translates to redeemers/withdrawers losing any pending interest.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368-L371

## Tool used

Manual Review

## Recommendation
The `totalAssets` function in Curated Vaults should either explicitly update the reserve for each pool or use a getter function for the Pool that returns the updated collateral balance, including pending interest.