Steep Aqua Yak

Medium

# A lender can get more vault shares than expected

## Summary
When a market reserve is not updated, a lender can get more vault shares than expected due to incorrect share calculation

## Vulnerability Detail
The function [`CuratedVault#deposit()` computes shares for the depositors before executing logic in function `_deposit()` to call to update pool reserve and supply to the pool](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L321-L330). The calculated share amount is calculated based on the old market reserve data, meaning that calculated the vault supplied amount will be lower than actual amount at that timestamp because interest not accrued yet. 
So the vault share amount minted for the lender will be higher than expected.
The issue also happens with `mint()` function, similarly.

#### PoC
Add this test to the test file `test/vaults/ERC4626Test.sol`
```solidity
function testDepositGetMoreShares() public {
    vm.startPrank(owner);
    vault.setFee(0.3e18);
    vm.stopPrank();

    _setCap(allMarkets[0], 100 ether);

    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = allMarkets[0];

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);

    loanToken.mint(supplier, 1000 ether);
    collateralToken.mint(borrower, 200 ether);

    vm.startPrank(supplier);
    loanToken.approve(address(vault), 1000 ether);
    uint256 share = vault.deposit(50 ether, supplier);

    vm.startPrank(borrower);
    allMarkets[0].supplySimple(address(collateralToken), borrower, 50 ether, 0);
    allMarkets[0].borrowSimple(address(loanToken), borrower, 30 ether, 0);

    skip(10 days);

    address lender = makeAddr('lender');
    loanToken.mint(lender, 100 ether);
    vm.startPrank(lender);
    loanToken.approve(address(vault), 100 ether);

    // TRY TO DEPOSIT WITH THE SAME AMOUNT

    // WITHOUT FORCE UPDATE RESERVE
    uint256 snapshot = vm.snapshot();
    share = vault.deposit(1 ether, lender);
    uint256 totalAssets = vault.totalAssets();

    // WITH FORCE UPDATE RESERVE
    vm.revertTo(snapshot);
    allMarkets[0].forceUpdateReserve(vault.asset());
    uint256 share_2 = vault.deposit(1 ether, lender);
    uint256 totalAssets2 = vault.totalAssets();

    
    // Shares are not equal in two scenarios
    assertGt(share, share_2); // SHARES NOT EQUAL
    assertEq(totalAssets, totalAssets2); // TOTAL VAULT ASSETS EQUAL
  }
```

Run the test and console shows:
```bash
Ran 1 test for test/forge/core/vaults/ERC4626Test.sol:ERC4626Test
[PASS] testDepositGetMoreShares() (gas: 1216233)
```

## Impact
With the same deposit amount, some lenders could get more vault shares than expected if deposit funds before market interest is accrued

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L321-L330

## Tool used

Manual Review

## Recommendation
Calling to `Pool#forceUpdateReserve()` before any vault actions like deposit, mint, withdraw, redeem