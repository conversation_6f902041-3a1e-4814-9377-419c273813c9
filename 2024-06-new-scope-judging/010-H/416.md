Polite Garnet Swift

High

# No mechanism to ensure reserve data is updated before critical operations in the CuratedVault


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/PoolGetters.sol#L42

 in the function `getReserveData`


```solidity
function getReserveData(address asset) external view virtual override returns (DataTypes.ReserveData memory) {
  return _reserves[asset];
}
```

The potential issue stems from the fact that the `CuratedVault` relies on up-to-date reserve data from the `Pool`, but there's no guarantee that this data is always current. This could lead to a situation where the vault makes decisions based on stale data, potentially resulting in incorrect asset allocations or risk assessments.

### impact / PoC flow:

1. The `CuratedVault` uses `pool.getReserveData(asset)` to fetch reserve information.
2. This data is used in various functions, including `_accruedSupplyBalance`, which is critical for calculating asset values and making allocation decisions.
3. The `Pool` contract updates reserve data in functions like `updateState` and `updateInterestRates`, but these aren't always called before `getReserveData`.

Potential Impact:

1. Incorrect asset valuations
2. Mispriced deposits or withdrawals
3. Inaccurate risk assessments
4. Potential for exploitation by users who can predict or manipulate update timings

PoC Flow:

1. User observes that a particular pool's reserve data hasn't been updated recently.
2. User waits for a significant change in market conditions that would affect the reserve's interest rates or other parameters.
3. Before the pool data is updated, the user interacts with the `CuratedVault` (e.g., depositing or withdrawing).
4. The vault uses the stale data from `getReserveData` to calculate share prices or make allocation decisions.
5. The user gains an unfair advantage due to the mismatch between the actual market conditions and the stale data used by the vault.


### To mitigate this:

1. Implement a mechanism to ensure reserve data is updated before critical operations in the `CuratedVault`.
2. Consider adding a timestamp to the reserve data and implementing a "freshness" check in the vault.
3. Implement a push mechanism where the pool notifies vaults of significant changes, rather than relying solely on pull requests for data.
