Special Macaroon Shetland

High

# Vault's `totalAssets()` implementation causing wrong amount of share minting

### Summary

In [CuratedVault.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVault.sol#L368), `totalAssets()` function is returning out-dated value because pool's state indexes should be updated before calling `getBalanceByPosition()`. 

### Root Cause

In CuratedVault.sol, `totalAssets()` is implemented as follows:

```solidity
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```
`getBalanceByPosition()` is implemented as follows:

```solidity
  function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
  }
```

We know that every pool action ( supply, borrow, repay, liquidate, etc... ) is calling `updateState()` function in order to calculate everything correctly. 

CuratedVaults implementation doesn't call any function which trigger `updateState()` function and it also doesn't call force update. 

### Internal pre-conditions

N/A

### External pre-conditions

N/A

### Impact

Due to out-dated output of `totalAssets()` function it can return wrong amount of assets. This function is used many important implementation such as `deposit()` function. It affects total minted shares to the users because of out-dated value.
```solidity
  function deposit(uint256 assets, address receiver) public override returns (uint256 shares) {
&>  uint256 newTotalAssets = _accrueFee();

    // Update `lastTotalAssets` to avoid an inconsistent state in a re-entrant context.
    // It is updated again in `_deposit`.
    lastTotalAssets = newTotalAssets;
&>  shares = _convertToSharesWithTotals(assets, totalSupply(), newTotalAssets, MathUpgradeable.Rounding.Down);
    _deposit(_msgSender(), receiver, assets, shares);
  }
```
It's used in '_accrueFee()` function and while calculation of the shares in deposit function. This issue causes minting wrong amount of shares. 

### Mitigation

In order to prevent out-dated output, we need to add force update for all the pools:


```solidity
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      withdrawQueue[i].forceUpdateReserve(asset());
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```