Massive Glass Crane

High

# accrueFee() calculates incorrect fees and totalAssets since the  pool is not updated to the latest

### Summary

The `totalAssets()` is not considering the interest accumulated by each markets in the `withdrawQueue` and thereby resusulting in incorrect calculation of fees when the `accrueFees()` is called.

### Root Cause
the `accrueFees()` is called whenever owner changes Fees or feeRecipient or when the user deposits/mints/withdraw/redeem.

`accrueFees()` then calls ` _accruedFeeShares()` to calculate the accumulated interest from the last time lastTotalAsset is calculated..

This is achieved using the function `totalAssets()`. [code](https://vscode.dev/github/zerolend/zerolend-one/blob/master/contracts/core/vaults/CuratedVaultGetters.sol#L185-L188).

And inside the overriden function `totalAssets()` , `getBalanceByPosition(asset(), positionId)` for each pool in the `withdrawQueue`.

https://vscode.dev/github/zerolend/zerolend-one/blob/master/contracts/core/vaults/CuratedVault.sol#L370-L371

```solidity
  function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) { //@audit-info (not a) supplyQueue instead of withdrawQueue
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId); //@audit doenst inlude the currentInterest in each pools (forceUpdatePool.)
    }
  }
```
But the function doesnt update the liquidityIndex to the latest but simply returns the supplyBalance.
https://vscode.dev/github/zerolend/zerolend-one/blob/master/contracts/core/pool/PoolGetters.sol#L47-L50

This will result in incorrect calculation of remianingInterest and thereby affects the fee calculation for feeRecipients and shares to be minted/redeemed for users

### Internal pre-conditions


The owner calls `setFeeRecipient()`.

`accruedFees()` is called to distribute the fees for the accumulated interest till then to the FeeRecipient before changing to the new address.


### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact


1) Old `FeeRecipient` will not receive the  fees for the remaining interest  when the feeRecipient is changed to the new.
2) The `fees` are calculated using the newFees instead of OldFees even though accrueFees is called prior changing.
3) Users share is calculated incorrectly within the vault when depositing / withdrawing /redeeming /minting.
4) Doesnt follow the [ERC4626](https://github.com/OpenZeppelin/openzeppelin-contracts/blob/55d69573fc6c9cf2924efeb6265a4f4423a6c17e/contracts/interfaces/IERC4626.sol#L32-L40) ("SHOULD include any compounding that occurs from yield.")

### PoC


### Mitigation


Call   `pool.forceUpdateReserve(asset())` before calling `pool.getBalanceByPosition(asset(), positionId)`