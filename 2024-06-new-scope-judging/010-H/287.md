Magnificent Scarlet Dove

High

# reserves state  of pool in which the vault has position is not updated before accruing fees shares.

## Summary
Reserves state  of pool in which the vault has position is not updated before accruing fees shares which leads to old liquidity index being used thus not accruing fee shares.
## Vulnerability Detail
_accrueFees function is called multiple times in the contract
```solidity
 function _accrueFee() internal returns (uint256 newTotalAssets) {
    uint256 feeShares;
    (feeShares, newTotalAssets) = _accruedFeeShares();
    if (feeShares != 0) _mint(feeRecipient, feeShares);
    emit CuratedEventsLib.AccrueInterest(newTotalAssets, feeShares);
  }
```
_accruedFeeShares function is as follows
```solidity
function _accruedFeeShares() internal view returns (uint256 feeShares, uint256 newTotalAssets) {
    newTotalAssets = totalAssets();

    uint256 totalInterest = newTotalAssets.zeroFloorSub(lastTotalAssets);
    if (totalInterest != 0 && fee != 0) {
      // It is acknowledged that `feeAssets` may be rounded down to 0 if `totalInterest * fee < WAD`.
      uint256 feeAssets = totalInterest.mulDiv(fee, 1e18);
      // The fee assets is subtracted from the total assets in this calculation to compensate for the fact
      // that total assets is already increased by the total interest (including the fee assets).
      feeShares = _convertToSharesWithTotals(feeAssets, totalSupply(), newTotalAssets - feeAssets, MathUpgradeable.Rounding.Down);
    }
  }
```
totalAssets() function is as follows
```solidity
function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```
Now as can be seen that the total assets are calculated as above and getBalance by position function is as follows
```solidity 
function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
  }
```
So if the liquidity index of a reserve is not updated before accruing fees then fees won't be accrued.
That is the main issue because in several instance like some are as follows
```solidity
function setFee(uint256 newFee) external onlyOwner {
    if (newFee == fee) revert CuratedErrorsLib.AlreadySet();
    if (newFee > MAX_FEE) revert CuratedErrorsLib.MaxFeeExceeded();
    if (newFee != 0 && feeRecipient == address(0)) revert CuratedErrorsLib.ZeroFeeRecipient();

    // Accrue fee using the previous fee set before changing it.
    _updateLastTotalAssets(_accrueFee());

    // Safe "unchecked" cast because newFee <= MAX_FEE.
    fee = uint96(newFee);

    emit CuratedEventsLib.SetFee(_msgSender(), fee);
  }

  /// @inheritdoc ICuratedVaultBase
  function setFeeRecipient(address newFeeRecipient) external onlyOwner {
    if (newFeeRecipient == feeRecipient) revert CuratedErrorsLib.AlreadySet();
    if (newFeeRecipient == address(0) && fee != 0) revert CuratedErrorsLib.ZeroFeeRecipient();

    // Accrue fee to the previous fee recipient set before changing it.
    _updateLastTotalAssets(_accrueFee());

    feeRecipient = newFeeRecipient;

    emit CuratedEventsLib.SetFeeRecipient(newFeeRecipient);
  }
```
as seen above that no where in _accrue fee function reserve state is updated to latest liquidity index so fees wouldn't be accrued at all.

## Impact
fees is not accrued.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultSetters.sol#L178
## Tool used

Manual Review

## Recommendation
Update the reserve state before performing any calculation regarding fees accruel
One way can be by doing so in totalAssets function
```solidity
function totalAssets() public view override returns (uint256 assets) {
    for (uint256 i; i < withdrawQueue.length; ++i) {
      withdrawQueue[i].forceUpdateReserve(asset());
      assets += withdrawQueue[i].getBalanceByPosition(asset(), positionId);
    }
  }
```