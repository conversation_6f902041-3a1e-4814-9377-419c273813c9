Curly Pineapple Armadillo

High

# `CuratedVault` depositors are able to get more shares than intended by the protocol, stealing from other depositors

### Summary

The `_accrueFee` function of `CuratedVault`, called inside of `deposit` is used to update the total assets and set the `newTotalAssets` variable. In order to get the total assets `_accrueFee` calls `totalAssets`, which goes through the withdrawal queue and sums together the results of `getBalanceByPosition(asset(), positionId)` for all pools in the queue. The issue is that the pools that are looped through are yet to be updated, and there is accrued interest that is yet to be accounted for. As a result `newTotalAssets` will be less than in reality and the depositors will be minted more shares than they should. Furthermore, the extra shares, minted to the new depositors, will essentially allow new depositors to steal interest that should belong to the previous depositors.
It is also important to note that `newTotalAssets` may also be stale when funds are withdrawn.

To add to that, due to the same issue with `_accrueFee` fee recipients will not receive the fees they are entitled to when they are changed, as `_accrueFee` will use stale `totalAssets` to calculate the number of fees the fee recipients must receive. As a result, the new fee recipient will steal from the previous.

### Root Cause

- In [`_accruedFeeShares`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L186) stale `totalAssets` is used to set the value of `newTotalAssets`.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. A curated vault has one depositor that has deposited 10 ETH, and has been minted 10e18 of shares.
2. The 10 ETH accrues interest in `Pool.sol`, and the depositor should be able to redeem their shares for 11 ETH.
3. Another user deposits 10 ETH into the pool, but the reserve is yet to be updated and `totalAssets` will return 10 ETH instead of 11 ETH.
4. Therefore, the second depositor is also minted 10e18 of shares and can now withdraw a part of the interest accrued by the first depositor.

### Impact

Interest is stolen from curated vault depositors.

### PoC

_No response_

### Mitigation

Make sure that `totalAssets` forces an update on the reserves it is querying data from.