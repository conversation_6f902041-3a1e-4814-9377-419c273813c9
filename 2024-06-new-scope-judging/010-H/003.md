Great <PERSON> Shetland

Medium

# Supply pool reserves are not updated on an early stage, leading to incorrect calculations of shares and fees

### Summary

When users deposit into Curated Vaults, interest is accrued and the total assets is updated by fetching the deposited assets in the corresponding supply pools. Just before supplying to the pools, reserves are being forcibly updated, however, this is being updated late, and it should be updated beforehand.

Let's take the following scenario:
1. <PERSON> deposits some funds into a vault -> pool
2. <PERSON><PERSON><PERSON> borrows some funds from the same pool
3. Some time passes by and interest is accumulating (without the pool reserves being updated), <PERSON> deposits some funds into the same vault
4. The minted shares for <PERSON> are the same as <PERSON>'s

The above is wrong, as <PERSON>'s position has been there for way more, and <PERSON> gained some shares at the same price as <PERSON>, where the price of a share should be higher, as the pool gained some profit, and the vault in return gained some profit.

This is happening because, when new total assets is calculated [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVaultGetters.sol#L185-L196) that is being called from [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/vaults/CuratedVault.sol#L323) it is not calling the reserve update on the pools, so any profit gained on the pool is not registered in the vault's context. So the 

This results in incorrect/unfair calculation of both shares and fees.

### Root Cause

Supply pool reserves are not updated at an early stage, i.e. before any deposit/withdrawal action or before accruing fees.

### Impact

* Invalid/unfair calculation of new positions in the Curated Vault.
* Wrong calculation of the vault fees.

### PoC



<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract ContestTest is Test {
  // Core
  PoolFactory public poolFactory;
  Pool public poolImplementation;
  PoolConfigurator public configurator;
  DefaultReserveInterestRateStrategy public irStrategy;
  ICuratedVault internal vault;
  ICuratedVaultFactory internal vaultFactory;
  IPool internal pool;

  // Tokens
  WETH9Mocked public WETH;
  USDCMocked public USDC;

  // Oracles
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public owner = makeAddr('owner');
  address internal allocator = makeAddr('allocator');
  address internal curator = makeAddr('curator');
  address internal guardian = makeAddr('guardian');
  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');
  address public borrower = makeAddr('borrower');

  function _setUpCore() internal {
    poolImplementation = new Pool();
    poolFactory = new PoolFactory(address(poolImplementation));
    configurator = new PoolConfigurator(address(poolFactory));
    poolFactory.setConfigurator(address(configurator));

    WETH = new WETH9Mocked();
    USDC = new USDCMocked();

    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);

    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);

    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);

    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);

    DataTypes.InitReserveConfig memory config = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });

    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = config;
    configurationLocal[1] = config;

    address[] memory admins = new address[](1);
    admins[0] = address(this);

    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _setUpCuratedVault() internal {
    CuratedVault instance = new CuratedVault();
    vaultFactory = ICuratedVaultFactory(new CuratedVaultFactory(address(instance)));

    address[] memory admins = new address[](1);
    address[] memory curators = new address[](1);
    address[] memory guardians = new address[](1);
    address[] memory allocators = new address[](1);
    admins[0] = owner;
    curators[0] = curator;
    guardians[0] = guardian;
    allocators[0] = allocator;

    vault = vaultFactory.createVault(
      ICuratedVaultFactory.InitVaultParams({
        revokeProxy: true,
        proxyAdmin: owner,
        admins: admins,
        curators: curators,
        guardians: guardians,
        allocators: allocators,
        timelock: 1 weeks,
        asset: address(USDC),
        name: 'Vault',
        symbol: 'VLT',
        salt: keccak256('salty')
      })
    );
  }

  function _setCap(IPool pool_, uint256 newCap) internal {
    uint256 cap = vault.config(pool_).cap;
    bool isEnabled = vault.config(pool_).enabled;
    if (newCap == cap) return;

    PendingUint192 memory pendingCap = vault.pendingCap(pool_);
    if (pendingCap.validAt == 0 || newCap != pendingCap.value) {
      vm.prank(curator);
      vault.submitCap(pool_, newCap);
    }

    if (newCap < cap) return;

    vm.warp(block.timestamp + vault.timelock());

    vault.acceptCap(pool_);

    assertEq(vault.config(pool).cap, newCap, '_setCap');

    if (newCap > 0) {
      if (!isEnabled) {
        IPool[] memory newSupplyQueue = new IPool[](vault.supplyQueueLength() + 1);
        for (uint256 k; k < vault.supplyQueueLength(); k++) {
          newSupplyQueue[k] = vault.supplyQueue(k);
        }
        newSupplyQueue[vault.supplyQueueLength()] = pool_;
        vm.prank(allocator);
        vault.setSupplyQueue(newSupplyQueue);
      }
    }
  }

  function _setSupplyPool() internal {
    IPool[] memory supplyQueue = new IPool[](1);
    supplyQueue[0] = pool;

    vm.prank(allocator);
    vault.setSupplyQueue(supplyQueue);
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _setUpCuratedVault();

    _setCap(pool, type(uint184).max);
    _setSupplyPool();

    _syncOracles();
  }

  function testInvalidShareCalculation() public {
    uint256 USDCamount = 1_000e6;
    uint256 WETHamount = 1e18;

    USDC.mint(bob, USDCamount);
    USDC.mint(alice, USDCamount);
    WETH.mint(borrower, WETHamount);

    // Bob deposits 1k USDC
    vm.startPrank(bob);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, bob);
    vm.stopPrank();

    // Borrower supplies 1 WETH and borrows 500 USDC
    vm.startPrank(borrower);
    WETH.approve(address(pool), type(uint256).max);
    pool.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool.borrowSimple(address(USDC), borrower, USDCamount / 2, 0);
    vm.stopPrank();

    vm.warp(block.timestamp + 30 days);

    // Alice deposits 1k USDC
    vm.startPrank(alice);
    USDC.approve(address(vault), type(uint256).max);
    vault.deposit(USDCamount, alice);
    vm.stopPrank();

    // Alice's shares == Bob's shares
    // Alice's max withdraw == Bob's max withdraw
    // Knowing that Bob's position has been there for longer (30 days)
    assertEq(vault.maxWithdraw(bob), vault.maxWithdraw(alice));
    assertEq(vault.balanceOf(bob), vault.balanceOf(alice));
  }
}
```
</details>

### Mitigation

Force update reserves for all supply pools, before any interest accruing or any deposit/withdraw action, by adding something like the following in `CuratedVaultSetters::_accrueFee`, and remove any subsequent pool reserve updates.
```diff
function _accrueFee() internal returns (uint256 newTotalAssets) {
+ for (uint256 i; i < supplyQueue.length; ++i) supplyQueue[i].forceUpdateReserve(asset());
  uint256 feeShares;
  (feeShares, newTotalAssets) = _accruedFeeShares();
  if (feeShares != 0) _mint(feeRecipient, feeShares);
  emit CuratedEventsLib.AccrueInterest(newTotalAssets, feeShares);
}
```