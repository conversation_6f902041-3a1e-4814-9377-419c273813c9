Massive Nylon Vulture

Medium

# Pool Admin when updates the reserve strategy will mix indexes and rates affecting 7 pool operations

### Summary

Mixed `liquidityIndex` and `borrowIndex` will cause 7 important operations in zaros  not  to work properly as pool admin will change the interest rate strategy of a reserve but the problem is that it doesn't update properly for the old strategy before updating the new one.

`PoolConfigurator::setReserveInterestRateStrategyAddress` is used to change the interest rate strategy for a reserve of a pool at any time. However before applying the change the interest should accrue for the old strategy to avoid inconsistencies in most of the critical operation of Zerolend over the affected pool:


| Operation      | Reason                                       |
|----------------|----------------------------------------------|
| Accrue To Treasury | Mixed `borrowIndex`                    |
| Borrow         | Mixed `borrowIndex`                    |
| Repay          | Mixed `borrowIndex`                    |
| Flashloan      | Mixed `liquidityIndex`                 |
| Liquidation    | Mixed Both `borrowIndex` and `liquidationIndex`    |
| Supply         | Mixed `liquidityIndex`                 |
| Withdraw       | Mixed `liquidityIndex`       

### Root Cause

`PoolConfigurator.sol:105 setReserveInterestRateStrategyAddress` change one strategy for another updating the field `interestRateStrategyAddress`, then is used in `ReserveLogic::updateInterestRates` to update `_reserve.liquidityRate` and `_reserve.borrowRate`. Those 2 fields are use then by `ReserveLogic::updateState` to update the `borrowIndex` and the `liquidityIndex` in a **time range  [lastUpdateTimestamp, now]**The time range is important. The problem is that the time range will be t0 + t1 where t0 has liquidityRate and borrowRate from the old strategy and t1 liquidityRate and borrowRate from the new strategy leading to inconsistencies in most of the operation of zerolend.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/manager/PoolConfigurator.sol#L105-L111

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/PoolLogic.sol#L150


### Internal pre-conditions

1. Pool Admin needs to call `PoolConfigurator::setReserveInterestRateStrategyAddress`


### External pre-conditions

None

### Attack Path


1. User Creates Pool
2. Admin pool update an interest strategy address as result each liquidityIndex and borrowIndex will be mixed.

### Impact

`liquidityIndex` and `borrowIndex` will be incorrect as will be a result of a mixing rates  of the old reserve strategy and the new one leading to 7 pool operations working with incorrect values: 
1. Accrue To Treasury
2. Borrow
3. Repay
4. Flashloan
5. Liquidation
6. Supply
7. Withdraw

### PoC

_No response_

### Mitigation


Update indexes and Rates before updating the new interest strategy using:

**The flow (cache -> updateState -> updateInterestRates)**

