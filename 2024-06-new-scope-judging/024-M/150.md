Powerful Licorice Elephant

Medium

# Repaid interest is not accounted properly between  supplier(deposier) and reserve treasury.

## Summary
some shares(based on reserve factor) are minted from the repaid interest to the reserve treasury for a specific asset. But after minting the shares to the treasury ,   _reserve.liquidityIndex is not decreased based on minted shares which are minted to treasury. As a result, there will always be less amount to withdraw for the asset in the pool.


## Vulnerability Detail
1. Let’s assume, Alice supplies 250e18 and <PERSON> supplies 250e18 for an asset in a pool.the asset’s current liquidityindex = 0, borrowindex = 0 and liquidity rate and borrowrate is same  and their self.lastSupplyLiquidtyIndex is 0.
 
2. John borrows 500e18 of this asset from this pool.   
 
3. Aftersometimes, function updateState is called for this asset which calls  _updateIndexes where the asset’s liquidity index is updated to 0.02 and borrow index is updated to 0.02. In  function updateState, after calling _updateIndexes ,  _accrueToTreasury function is called where vars.prevtotalDebt = 500,  vars.currtotalDebt = 510(with interest), vars.totalDebtAccrued(interest accrued) = 510-500 = 10, so  vars.amountToMint = 10*0.2 = 2(assume  reservefactor 0.2). So the share of amount 2 is added to the asset's TreasuryShare.

4. Now alice calls withdraw function to withdraw full amount, so alice will withdraw 250+5= 255 with interest(as liquidity index = 0.02, so interest = 250*0.02-250*0 = 5).
 
5. Similarly Bob withdraws 255 with interest.
 
6. Now this asset fund is 0 in the pool. This asset’s  treasury has not withdrawn the share of amount 2(TreasuryShare) from the pool. But as there is no asset fund, so TreasuryShare can’t be withdrawn by  treasury address which is unfair.


## Impact
there will always be less amount to withdraw for the asset in the pool
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L212
## Tool used

Manual Review

## Recommendation
decrease _reserve.liquidityIndex based on minted shares which are minted to treasury.
