Curly Pineapple Armadillo

Medium

# Treasury fees are not considered when calculating interest rates after withdrawals

### Summary

When a withdrawal occurs and interest rates are updated, the calculation of the new interest rates does not consider that treasury fees will also be withdrawn. This causes the interests rates to be higher than intended.

### Root Cause

In [`executeWithdraw`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L125-L134) when `updateInterestRates` is called, the value for the `_liquidityTaken` parameter is set to `params.amount`. This is an issue as it does not include the value of the fees, transferred to the treasury. 
Those fees need to also be a part of `_liquidityTaken` as `IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress).calculateInterestRates` will calculate the new interest rate based on the pool's balance of the asset. As the fee transfer is performed after the rates are updated, the asset balance of the pool will be incorrectly inflated by the value of the fees, causing `calculateInterestRates` to return a higher value and interest rates to be higher than intended.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. There is 1e18 of fees, yet to be withdrawn to the treasury.
2. User withdraws assets from a pool.
3. When calculating what the interest rates should be after the withdrawal, the pool's balance of the asset is inflated by 1e18 of fees which are going to be transferred from the pool to the treasury when `executeMintToTreasury` is called.
4. Interest rates are wrongly calculated and are more than intended as `_liquidityTaken`  does not include the 1e18 of treasury fees

### Impact

As a result, when a withdrawal occurs interest rates will be higher than what the protocol intends.

### PoC

_No response_

### Mitigation

In [`executeWithdraw`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/SupplyLogic.sol#L125-L134) when `updateInterestRates` is called, the value for the `_liquidityTaken` parameter should be set to `params.amount + reserve.accruedToTreasuryShares.rayMul(reserve.getNormalizedIncome())` to account for the treasury fees.