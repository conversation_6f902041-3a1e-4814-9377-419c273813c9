Clever Ebony Halibut

Medium

# Incorrect Calculation of Collateral to Seize in Liquidation

## Summary
The function `_burnCollateralTokens` calls `collateralReserve.updateIndex()` after the amount of collateral to seize is calculated in `_calculateAvailableCollateralToLiquidate()`. This order of operations results in an incorrect collateral valuation, as the updated index is not used in the calculation, leading to a potential undervaluation of collateral and an incorrect collateral amount being seized.

## Vulnerability Detail
The issue lies in the following sequence of calls in the `executeLiquidationCall`:
1. `_calculateAvailableCollateralToLiquidate` is invoked which uses the amount of collateral to seize is calculated. (There is a bug here that we discussed in another report in which the shares are not converted to assets)

2. `collateralReserve.updateIndex` is called after the available collateral is calculated in `_burnCollateralTokens`.

Here is an example of the problem:

```solidity
//@audit-issue amount of collateral to seize is calculated here, before the state for the seized reserved is updated.
    (vars.actualCollateralToLiquidate, vars.actualDebtToLiquidate, vars.liquidationProtocolFeeAmount) =
    _calculateAvailableCollateralToLiquidate(
      collateralReserve,
      vars.debtReserveCache,
      vars.actualDebtToLiquidate,
      vars.userCollateralBalance,
      vars.liquidationBonus,
      IPool(params.pool).getAssetPrice(params.collateralAsset),
      IPool(params.pool).getAssetPrice(params.debtAsset),
      IPool(params.pool).factory().liquidationProtocolFeePercentage()
    );

    if (vars.userDebt == vars.actualDebtToLiquidate) {
      userConfig.setBorrowing(debtReserve.id, false);
    }

    // If the collateral being liquidated is equal to the user balance,
    // we set the currency as not being used as collateral anymore
    if (vars.actualCollateralToLiquidate + vars.liquidationProtocolFeeAmount == vars.userCollateralBalance) {
      userConfig.setUsingAsCollateral(collateralReserve.id, false);
      emit PoolEventsLib.ReserveUsedAsCollateralDisabled(params.collateralAsset, params.position);
    }

    _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

    debtReserve.updateInterestRates(
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );
@audit-issue the seized reserve state is updated for the first time in the end in `_burnCollateralTokens()`. After the liquidity calculations are already calculated.
@>>    _burnCollateralTokens(
      collateralReserve, params, vars, balances[params.collateralAsset][params.position], totalSupplies[params.collateralAsset]
    );
```
The reserve state is only updated there
```solidity
  function _burnCollateralTokens(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    DataTypes.PositionBalance storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    DataTypes.ReserveCache memory collateralReserveCache = collateralReserve.cache(totalSupplies);
@>>    collateralReserve.updateState(params.reserveFactor, collateralReserveCache);
```
In order to have a correct calculations of the seized assets we need to update state at the start of the function, as we need an updated state to calculate the amount of collateral to seize in `_calculateAvailableCollateralToLiquidate()`
## Impact
This bug causes the liquidation process to seize less collateral than intended, thereby not reflecting the accurate debt coverage. This may lead to a lower system stability as the seized collateral may be undervalued, and the remaining debt may not be adequately covered. This discrepancy affects the protocol's financial integrity and the users who might benefit unfairly from being liquidated with less collateral seized.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L139C1-L176C7
## Tool used

Manual Review

## Recommendation
To mitigate this issue, update the state and accrue the interest rates  in `_calculateAvailableCollateralToLiquidate()`
```diff
  function _calculateAvailableCollateralToLiquidate(
    DataTypes.ReserveData storage collateralReserve,
    DataTypes.ReserveCache memory debtReserveCache,
    uint256 debtToCover,
    uint256 userCollateralBalance,
    uint256 liquidationBonus,
    uint256 collateralPrice,
    uint256 debtAssetPrice,
    uint256 liquidationProtocolFeePercentage
  ) internal view returns (uint256, uint256, uint256) {
    AvailableCollateralToLiquidateLocalVars memory vars;

    vars.collateralPrice = collateralPrice; // oracle.getAssetPrice(collateralAsset);
    vars.debtAssetPrice = debtAssetPrice; // oracle.getAssetPrice(debtAsset);

+    DataTypes.ReserveCache memory collateralReserveCache = collateralReserve.cache(totalSupplies);
+    collateralReserve.updateState(params.reserveFactor, collateralReserveCache);
```