Calm Graphite Ram

Medium

# LiquidationLogic::_repayDebtTokens() incorrectly assigns vars.debtReserveCache.nextDebtShares

## Summary

`LiquidationLogic::_repayDebtTokens()` is called during a liquidation call, however in it's current state it sets `vars.debtReserveCache.nextDebtShares` to an incorrect value, causing the `_reserve.liquidityRate` and `_reserve.borrowRate` to be incorrectly calculated.

## Vulnerability Detail
[LiquidationLogic::_repayDebtTokens()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247)
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    vars.debtReserveCache.nextDebtShares = burnt; 
  }
```
In `LiquidationLogic::_repayDebtTokens()` `burnt` will be equal to the number of burnt shares when liquidating debt, however the function assigns the `nextDebtShares` to this value, which will make downstream calculations use the wrong value. `nextDebtShares` is meant to equal the number of total debt shares, which will be used downstream to calculate the next borrowing index, however in this case the number of total shares will be equal to the shares burnt in the liquidation call, rather than the `totalDebtShares - burnt`.

[LiquidationLogic.sol#L163-L172](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163-L172)
```solidity
    debtReserve.updateInterestRates( 
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );
```
When updating the `debtReserve` interest rate, the incorrect value will be passed through `vars.debtReserveCache`:

```solidity
  function updateInterestRates( 
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ReserveCache memory _cache,
    address _reserveAddress,
    uint256 _reserveFactor,
    uint256 _liquidityAdded,
    uint256 _liquidityTaken,
    bytes32 _position,
    bytes memory _data
  ) internal {
    UpdateInterestRatesLocalVars memory vars;

>>  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress) // OOS file
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
>>      totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );

    _reserve.liquidityRate = vars.nextLiquidityRate.toUint128();
    _reserve.borrowRate = vars.nextBorrowRate.toUint128();
```
When calculating the next `liquidityRate` and `borrowRate` after a liquidation has been executed, the `totalDebt` is calculated using the number of shares that was burnt within this liquidation call, rather than the total debt shares without the newly burnt shares that reduced the total debt.

## POC

This coded POC shows how the new `borrowRate` is corrolated closely to the amount that is liquidated, showing how when `0.5e18` and `1e18` of debt are liquidated, that the new rates are 2x apart, as the new `borrowRate` is treating the new borrow total supply to be the amount that is being burnt during the liquidation.

<details>
<summary>POC</summary>
Add the following function to `PoolLiquidationTests.t.sol`
```solidity
  function testLiquidationBorrowRate() external {
    _generateLiquidationCondition();
    uint256 aliceDebtBefore = pool.getDebtByPosition(address(tokenB), pos);
    uint256 totalCollateralBase = pool.getBalanceByPosition(address(tokenA), pos);
    uint128 oldBorrowRate = pool.getReserveData(address(tokenB)).borrowRate;

    uint256 liquidationAmount = 0.5e18;

    vm.startPrank(bob);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, liquidationAmount);
    vm.stopPrank();

    uint256 totalCollateralBaseNew = pool.getBalanceByPosition(address(tokenA), pos);
    uint256 aliceDebtAfter = pool.getDebtByPosition(address(tokenB), pos);

    console.log("previous total collat:", totalCollateralBase);
    console.log("new total collat:     ", totalCollateralBaseNew);   

    console.log("previous alice debt:", aliceDebtBefore);
    console.log("after alice debt:   ", aliceDebtAfter);

    uint128 newBorrowRate = pool.getReserveData(address(tokenB)).borrowRate;
    console.log("old borrow rate:", oldBorrowRate);
    console.log("new borrow rate:", newBorrowRate);
  }
```

Also change the oracle price update to something more reasonable than the current price, this does not affect the validity of the finding, it is solely to use a more realistic liquidation scenario and keep the values more reasonable:
```diff
  function _generateLiquidationCondition() internal {
    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 1000 tokenA
    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 550 tokenA alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, borrowAmountB, 0); // 100 tokenB alice borrow
    vm.stopPrank();

    assertEq(tokenB.balanceOf(alice), borrowAmountB);

-  oracleA.updateAnswer(5e3);
+ oracleA.updateAnswer(40e6);
  }
```
To run the test simply run the following command:
`forge test --match-test testLiquidationBorrowRate -vv`
</details>

Utilising `0.5e18` and `1e18` as test cases for liquidation amounts:

`liquidationAmount = 0.5e18`
```solidity
Logs:
  previous total collat: 550000000000000000000
  new total collat:      547375000000000000000
  previous alice debt: 100000000000000000000
  after alice debt:    99500000000000000000
  old borrow rate:   19858156028368794326241134
  new borrow rate:   114390299702585220773279
```
`liquidationAmount = 1e18`
```solidity
Logs:
  previous total collat: 550000000000000000000
  new total collat:      544750000000000000000
  previous alice debt: 100000000000000000000
  after alice debt:    99000000000000000000
  old borrow rate:   19858156028368794326241134
  new borrow rate:   228429708915285210807989
```
As demonstrated in the logs from the tests, the borrow rate is set utilising the burnt shares, rather than the totalShares - burnt.

## Impact

When a liquidation occurs, the `borrowIndex`storage value for the asset is set to a value orders of  magnitude lower than intended due to the use of the number of burnt shares during the liquidation as the `totalDebt` value when calling `calculateInterestRates`.

### Health factor
`GenericLogic::calculateUserAccountData()` calculates a user's `healthFactory` utilising the user's total debt which will be calculated using `getNormalizedDebt()` which utilises the `borrowRate`, therefore it will undervalue the user's debt potentially stopping the liquidation (or blocking a 100% liquidation and capping the liquidation at 50%) of an unhealthy position as the calculated health factory will be higher than it is inreality due to the lower total debt amount. This can occur if a user front-runs their own liquidation by liquidating another users minimal amount of debt to set the `borrowRate` to a low amount. 

## Code Snippet

[LiquidationLogic::_repayDebtTokens()](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247)

## Tool used

Manual Review

## Recommendation

In `LiquidationLogic::_repayDebtTokens()` set `vars.debtReserveCache.nextDebtShares` to total debt minus the burnt shares.

