Curly Pineapple Armadillo

High

# Borrow rates are easily manipulatable due to `nextDebtShares` being set to a wrong value

### Summary

In `executeLiquidationCall` when [`_repayDebtTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239) is called `vars.debtReserveCache.nextDebtShares` is set to the debt tokens that are repaid by the liquidator. This is wrong as `nextDebtShares` needs to represent all of the remaining reserve debt after the liquidation. This value is used when updating the borrow rates after the debt is repaid, allowing a malicious user to decrease significantly the borrow rates. Furthermore, whenever a liquidaition is made the borrow rates will be wrongly updated even if the liquidator is not malicious.

### Root Cause

- In [`_repayDebtTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246) `vars.debtReserveCache.nextDebtShares` is set to the debt tokens that are repaid by the liqudiator, when they should be set to all of the remainging debt shares.

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

1. A liquidator repays the minimum possible amount of debt from a liquidatable position.
2. This will cause the new borrow rate to be significantly smaller as when it is calculated in `calculateInterestRates` `totalDebt` will be set to the amount repaid by the liquidator and not the actual remaining debt.
3. The attack does not need to be performed by a malicious user, as the issue will occur whenever a liqudation is made.

### Impact

A reserve's borrow rate will always be updated wrongly when a position is liquidated, allowing users to decrease the borrow rate.

### PoC

_No response_

### Mitigation

In [`_repayDebtTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246) `vars.debtReserveCache.nextDebtShares` should be set to `totalSupplies.debtShares`, just as it is in `executeBorrow`.