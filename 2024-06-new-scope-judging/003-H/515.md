Low Lemon Dove

High

# wrong logic lead to wrong calculation of  interest rate

### Summary

in LiquidationLogic function _repayDebtTokens line 245  
`    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);` in **PositionBalanceConfiguration.repayDebt**
 it will try to first change actualDebtToLiquidate in to share amount and subtract and return the subtracted  amount 
which means burnt is the subtracted share amount  but the thing is that in line 246  it will update  **vars.debtReserveCache.nextDebtShares** to burnt  instead it should subtract but it will just overwrite into it  and **vars.debtReserveCache.nextDebtShares** is used in line 163 calculate and update  the interest rate which will  cause for a wrong calculation of interest rate 


### Root Cause

in LiquidationLogic:239  repayDebt is  the subtracted share amount  and it is updated  **vars.debtReserveCache.nextDebtShares** instead it should be subtracted  **burnt** from **vars.debtReserveCache.nextDebtShares**

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

leads to wrong calculation of interest rate 

### PoC

_No response_

### Mitigation

_No response_