Acrobatic Banana Poodle

High

# The function `LiquidationLogic::_repayDebtTokens()` may undervalue the total supply of debt shares

### Summary

The `LiquidationLogic::_repayDebtToken` overwrites the `nextDebtShares` to `burnt`, which may lead to various issues, including underestimating the interest rates.


### Root Cause

In `LiquidationLogic::_repayDebtTokens()`, the amount `burnt` from the user is incorrectly assigned to `nextDebtShares` (line 246 LiquidationLogic.sol):

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246


```solidity
    vars.debtReserveCache.nextDebtShares = burnt;
```

As a result, the total supply of debt shares `nextDebtShares` may be set to a wrong value and may be undervalued.

This cache's `nextDebtShares` will be used to calculate the next interest rates in the `updateInterestRates`. Depending on the logic in the interest strategy, the protocol might assign wrong value of the interest rate for users. Especially when the lower total debt will result in lower interest rate, this bug will lower interest rate.

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

After liquidation, the interest rate might be underestimated.

### Impact

The total supply of debt shares may be undervalued due to this issue. As the result, any following calculation using the cache's `nextDebtShares` will be incorrect, for example, the interest rate might be underestimated.

### PoC

_No response_

### Mitigation

It is recommended to subtract `burnt` from `nextDebtShares`, in order to reduce the total supply of debt shares.

```solidity
// LiquidationLogic.sol
246    vars.debtReserveCache.nextDebtShares -= burnt;
```

