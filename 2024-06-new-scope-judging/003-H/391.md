Special Macaroon Shetland

Medium

# Borrowing rate is dropping significantly due to wrong interest rate update

### Summary

In [LiquidationLogic.sol](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246), the `nextDebtShares` is set to burnt amount of shares which is a wrong action. `nextDebtShares` demonstrate the pool's total debt shares after this execution.

### Root Cause

In liquidation action, following logic is used for debt repayment and burning the shares:

```solidity
_repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

debtReserve.updateInterestRates(
  totalSupplies[params.debtAsset],
  vars.debtReserveCache,
  params.debtAsset,
  IPool(params.pool).getReserveFactor(),
  vars.actualDebtToLiquidate,
  0,
  '',
  ''
);

_burnCollateralTokens(
  collateralReserve, params, vars, balances[params.collateralAsset][params.position], totalSupplies[params.collateralAsset]
);
```

It burns the shares and then updates the interest rate. In `repayDebtTokens` function following implementation is used and it is wrong because `nextDebtShares` is set to burnt shares. Normally, it should demonstrate the total remainning debt shares on the reserve.

```solidity
function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
&>  vars.debtReserveCache.nextDebtShares = burnt; // @audit nextDebtShares shouldn't be burnt, instead it should be total debt
  }
```

```solidity
  function repayDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
&>  sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastDebtLiquidtyIndex = index;
    self.debtShares -= sharesBurnt;
    supply.debtShares -= sharesBurnt;
  }
```
### Impact

Borrowing rate will drop significantly due to wrong interest rate update. It uses total debt token amount in order to calculate the borrowing rate. If the total debt token amount is reduced significantly, we will see significant drop on borrowing rate.

```solidity
_repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

debtReserve.updateInterestRates(
  totalSupplies[params.debtAsset],
  vars.debtReserveCache,
  params.debtAsset,
  IPool(params.pool).getReserveFactor(),
  vars.actualDebtToLiquidate,
  0,
  '',
  ''
);
```

```solidity
function updateInterestRates(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ReserveCache memory _cache,
    address _reserveAddress,
    uint256 _reserveFactor,
    uint256 _liquidityAdded,
    uint256 _liquidityTaken,
    bytes32 _position,
    bytes memory _data
  ) internal {
    UpdateInterestRatesLocalVars memory vars;

    vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
```

In lending protocols, we know that borrowing rate is changed based on the usage ratio. If `totalDebt` is so low, borrowing rate is reduced in order to encourage people to borrow. This is why this issue change borrowing rate significantly.

### Mitigation

Instead of setting burnt amount of shares to `nextDebtShares` variable following implementation will be correct:

```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;
  }

```