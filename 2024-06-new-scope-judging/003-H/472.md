Ancient Gingerbread Rooster

High

# `_repayDebtTokens` wrongly sets `vars.debtReserveCache.nextDebtShares` to incorrect value, leading to false `liquidityRate`, `borrowRate`

## Summary
`_repayDebtTokens` wrongly sets `vars.debtReserveCache.nextDebtShares` to `burnt` shares instead of `remaining` shares and this contributes to setting false `liquidityRate` and `borrowRate` of debt reserve.

## Vulnerability Detail
In `LiquidiationLogic.executeLiquidationCall` function, `_repayDebtTokens` function is called to burn debt shares of the position. After that, `liquidityRate` and `borrowRate` are updated for debt reserve based on new debt shares of `vars.debtReserveCache`.

```solidity
  function executeLiquidationCall(
    ...
  ) external {
    ...

>   _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

>   debtReserve.updateInterestRates(
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );
    ...
```

In `_repayDebtTokens`, `vars.debtReserveCache.nextDebtShares` is expected to be refreshed with remaining shares. But as seen in the following code snippet, it is set to `burnt`. Hence, this leads to setting false `liqudityRate` and `borrowRate` of debt reserve.
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
>   vars.debtReserveCache.nextDebtShares = burnt;
  }
```

In `PositionManagerConfiguration.repayDebt`, it clearly returns burnt share amount:
```solidity
  function repayDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
>   sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastDebtLiquidtyIndex = index;
    self.debtShares -= sharesBurnt;
    supply.debtShares -= sharesBurnt;
  }
```

## Impact
Incorrectly setting the `liquidityRate` and `borrowRate` for the debt reserve will adversely affect lending operations involving that reserve.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246

## Tool used
Manual Review

## Recommendation
Set `vars.debtReserveCache.nextDebtShares` to correct remaining share value.
```diff
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
-   vars.debtReserveCache.nextDebtShares = burnt;
+   vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares
  }
```