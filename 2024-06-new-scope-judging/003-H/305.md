Polished Iris Antelope

High

# Incorrect ```totalDebt``` calculation during the liquidation process since ```repayDebt``` function returns the ```burnt``` shares, not the total debt shares after the burn.

## Summary
Interests will be updated incorrectly during a liquidation, since ```totalDebt``` passed in``` calculateInterestRates``` of ```ReserveInterestRateStrategy``` is the ```debtShares``` burnt, not the total ```debtShares``` after the burn.

## Vulnerability Detail
During a liquidation the ```interestRates``` of the reserve are supposed to be change from the ```ReserveInterestRateStrategy```. This contract expects the ```totalDebt``` so to perform calculations, but instead of this, only the burnt debt are provided to it. We can the implementation here :
```solidity
  function executeLiquidationCall(
    mapping(address => DataTypes.ReserveData) storage reservesData,
    mapping(uint256 => address) storage reservesList,
    mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
    mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
    mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
    DataTypes.ExecuteLiquidationCallParams memory params
  ) external 
    // ...

@>    _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

    debtReserve.updateInterestRates(
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );

    // ...
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163)

Now, let's see what the ```updateInterestRates``` do :
```solidity
  function updateInterestRates(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ReserveCache memory _cache,
    address _reserveAddress,
    uint256 _reserveFactor,
    uint256 _liquidityAdded,
    uint256 _liquidityTaken,
    bytes32 _position,
    bytes memory _data
  ) internal {
    UpdateInterestRatesLocalVars memory vars;

@>  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
@>        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
    
    // ...
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145C1-L182C4)

As we can see, it takes the ```nextDebtShares``` of the ```ReserveCache``` and considers it as ```totalDebt``` passing it to ```calculateInterestRates()```. However, in the ```_repayDebtTokens()``` the ```nextDebtShares``` are assigned to the ```burnt``` shares, not the actual total debt shares :
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
@>  vars.debtReserveCache.nextDebtShares = burnt;
  }
```
[Link to code](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239C1-L247C4)

## Impact
The incorrect calculation of ```totalDebt``` during liquidation can lead to inaccurate interest rate updates. Since the ```totalDebt``` is incorrectly set to the burnt debt shares rather than the actual total debt shares after the burn, the interest rates might be calculated based on inaccurate debt figures. Most common way of interest calculation is based on the utilization ratio, so the incorrect ```totalDebt``` calculation can be fatal. This could, also, result in improperly adjusted interest rates, leading to potential undercharging or overcharging of interest to the users. Over time, these inaccuracies can cause significant imbalances in the reserve, affecting the protocol’s liquidity and user incentives.

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239C1-L247C4
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163

## Tool used
Manual Review

## Recommendation
Consider making this change so the accountings to be right :
```diff
function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
-    vars.debtReserveCache.nextDebtShares = burnt;
+    vars.debtReserveCache.nextDebtShares -= burnt;
  }
```