Great Jade Shetland

High

# `LiquidationLogic::_repayDebtTokens` is wrongly setting `nextDebtShares` to the burnt shares, messing up all loans accumulated debt

### Summary

When a user repays his debt, `BorrowLogic::executeBorrow` sets the `nextDebtShares` as the new total debt shares after loan repayment, which is true and is used to calculate the borrowing rate `ReserveLogic::updateInterestRates`.
```solidity
(isFirstBorrowing, borrowed.shares) = b.borrowDebt(totalSupplies, params.amount, cache.nextBorrowIndex);
cache.nextDebtShares = totalSupplies.debtShares;
```
Upon liquidation, debt repayment is also done in `LiquidationLogic::_repayDebtTokens`, however, `nextDebtShares` is being set to the burn debt shares and not the new total debt shares.
```solidity
uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
vars.debtReserveCache.nextDebtShares = burnt;
```
This messes up the whole borrowing rate calculation in a later step. This forces borrowers to repay more amount than expected. This could vary depending on the external logic used in `IReserveInterestRateStrategy.calculateInterestRates`.

### Root Cause

In `LiquidationLogic::_repayDebtTokens`, `vars.debtReserveCache.nextDebtShares` is being set as the burnt/repaid debt shares instead of the new total debt shares, [here](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246).

### Impact

All active loans with the debt token as the liquidated debt asset will suffer from incorrect accumulated debt calculations.

### PoC

The below POC shows 2 test cases where the first (`testWrongNextDebtSharesAmountUsed`) is affected by the wrong borrow rate calculation showing that the borrower is supposed to repay an amount, that is higher than what it should be in case that wrong calculation never took place. The correct scenario amount is shown in the second test (`testNormalBorrowerWithoutLiquidation`).

<details>
<summary>Test/POC</summary>

```solidity
contract USDCMocked is ERC20, ERC20Permit {
  constructor() ERC20('USDC', 'USDC') ERC20Permit('USDC') {}

  function mint(address account, uint256 value) public returns (bool) {
    _mint(account, value);
    return true;
  }

  function decimals() public pure override returns (uint8) {
    return 6;
  }
}

contract Contest_Pool is Test {
  PoolFactory public poolFactory;
  DefaultReserveInterestRateStrategy public irStrategy;
  IPool internal pool;
  WETH9Mocked public WETH;
  USDCMocked public USDC;
  MockV3Aggregator public WETHOracle;
  MockV3Aggregator public USDCOracle;

  address public bob = makeAddr('bob');
  address public alice = makeAddr('alice');
  address public borrower = makeAddr('borrower');

  uint256 public constant CLOSE_FACTOR_HF_THRESHOLD = 0.95e18;
  uint256 public constant HEALTH_FACTOR_LIQUIDATION_THRESHOLD = 1e18;

  function _setUpCore() internal {
    poolFactory = new PoolFactory(address(new Pool()));
    poolFactory.setConfigurator(address(new PoolConfigurator(address(poolFactory))));
    WETH = new WETH9Mocked();
    USDC = new USDCMocked();
    WETHOracle = new MockV3Aggregator(8, 2_600e8);
    USDCOracle = new MockV3Aggregator(8, 1e8);
    irStrategy = new DefaultReserveInterestRateStrategy(47 * 1e25, 0, 7 * 1e25, 30 * 1e25);
    poolFactory.setReserveFactor(500);
  }

  function _basicPoolInitParams() internal view returns (DataTypes.InitPoolParams memory p) {
    address[] memory assets = new address[](2);
    assets[0] = address(WETH);
    assets[1] = address(USDC);
    address[] memory rateStrategyAddresses = new address[](2);
    rateStrategyAddresses[0] = address(irStrategy);
    rateStrategyAddresses[1] = address(irStrategy);
    address[] memory sources = new address[](2);
    sources[0] = address(WETHOracle);
    sources[1] = address(USDCOracle);
    DataTypes.InitReserveConfig[] memory configurationLocal = new DataTypes.InitReserveConfig[](2);
    configurationLocal[0] = DataTypes.InitReserveConfig({
      ltv: 7500,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 18,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    configurationLocal[1] = DataTypes.InitReserveConfig({
      // ltv: 0,
      ltv: 8000,
      liquidationThreshold: 8000,
      liquidationBonus: 10_500,
      decimals: 6,
      frozen: false,
      borrowable: true,
      borrowCap: 0,
      supplyCap: 0
    });
    address[] memory admins = new address[](1);
    admins[0] = address(this);
    p = DataTypes.InitPoolParams({
      proxyAdmin: address(this),
      revokeProxy: false,
      admins: admins,
      emergencyAdmins: new address[](0),
      riskAdmins: new address[](0),
      hook: address(0),
      assets: assets,
      rateStrategyAddresses: rateStrategyAddresses,
      sources: sources,
      configurations: configurationLocal
    });
  }

  function _setUpPool() internal {
    poolFactory.createPool(_basicPoolInitParams());
    IPool poolAddr = poolFactory.pools(0);
    pool = IPool(address(poolAddr));
  }

  function _syncOracles() internal {
    WETHOracle.updateRoundTimestamp();
    USDCOracle.updateRoundTimestamp();
  }

  function setUp() public {
    _setUpCore();
    _setUpPool();
    _syncOracles();
  }

  function getPositionId(address user, uint256 index) internal returns (bytes32) {
    return keccak256(abi.encodePacked(user, 'index', index));
  }

  uint256 USDCamount = 1_900e6;
  uint256 WETHamount = 1e18;

  function testWrongNextDebtSharesAmountUsed() public {
    {
      USDC.mint(bob, USDCamount * 10);
      WETH.mint(borrower, WETHamount);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(borrower);
      WETH.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
    }

    // Bob deposits 3.8k USDC
    vm.prank(bob);
    pool.supply(address(USDC), bob, USDCamount * 2, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));

    // Borrower deposits 1 WETH
    // Borrower borrows 1.9k USDC
    vm.startPrank(borrower);
    pool.supplySimple(address(WETH), borrower, WETHamount, 0);
    pool.borrowSimple(address(USDC), borrower, USDCamount, 0);
    vm.stopPrank();

    // Alice deposits 1 WETH
    // Alice borrows 500 USDC
    vm.startPrank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);
    pool.borrowSimple(address(USDC), alice, 500e6, 0);
    vm.stopPrank();

    // WETH price drops
    WETHOracle.updateAnswer(2_200e8);

    // Borrower's position HF < 95% (100% liquidatable)
    (, , , , , uint256 HF) = pool.getUserAccountData(borrower, 0);
    assertLt(HF, CLOSE_FACTOR_HF_THRESHOLD);

    // Alice's position HF > 100% (healthy)
    (, , , , , HF) = pool.getUserAccountData(alice, 0);
    assertGt(HF, HEALTH_FACTOR_LIQUIDATION_THRESHOLD);

    // Bob liquidates borrower's position
    vm.startPrank(bob);
    pool.liquidateSimple(address(WETH), address(USDC), getPositionId(borrower, 0), pool.getDebt(address(USDC), borrower, 0));
    vm.stopPrank();

    // Borrower's position is fully liquidated
    assertEq(pool.getDebt(address(USDC), borrower, 0), 0);

    // Some time passes
    // Some interest is accumulated
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserves();

    // Alice's debt is 502.2 USDC
    assertEq(pool.getDebt(address(USDC), alice, 0), 502_241_405);
  }

  function testNormalBorrowerWithoutLiquidation() public {
    {
      USDC.mint(bob, USDCamount * 10);
      WETH.mint(alice, WETHamount);

      vm.prank(bob);
      USDC.approve(address(pool), type(uint256).max);
      vm.prank(alice);
      WETH.approve(address(pool), type(uint256).max);
    }

    // Bob deposits 3.8k USDC
    vm.prank(bob);
    pool.supply(address(USDC), bob, USDCamount * 2, 0, DataTypes.ExtraData({interestRateData: '', hookData: ''}));

    // Alice deposits 1 WETH
    // Alice borrows 500 USDC
    vm.startPrank(alice);
    pool.supplySimple(address(WETH), alice, WETHamount, 0);
    pool.borrowSimple(address(USDC), alice, 500e6, 0);
    vm.stopPrank();

    // Some time passes
    // Some interest is accumulated
    vm.warp(block.timestamp + 30 days);
    _syncOracles();
    pool.forceUpdateReserves();

    // Alice's debt is 500.8 USDC
    assertEq(pool.getDebt(address(USDC), alice, 0), 500_805_999);
  }
}
```

</details>

### Mitigation

In `LiquidationLogic::_repayDebtTokens`, use the new total debt shares instead of the burnt shares, by replacing:
```solidity
vars.debtReserveCache.nextDebtShares = burnt;
```
with:
```solidity
vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;
```