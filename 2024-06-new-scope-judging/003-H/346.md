Innocent Chartreuse Parrot

Medium

# Interest rates are severely underestimated after every liquidation

## Summary
Interest rates are severely underestimated after every liquidation

## Vulnerability Detail
During liquidations, the `nextDebtShares` for the reserve is incorrectly set to only the shares burnt during debt repayment:

[LiquidationLogic::_repayDebtTokens](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L246)
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex); // @audit: returns shares burnt
    vars.debtReserveCache.nextDebtShares = burnt; // @audit: sets total debt shares to shares burnt
```

[PositionBalanceConfiguration::repayDebt](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L117)
```solidity
  function repayDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastDebtLiquidtyIndex = index;
    self.debtShares -= sharesBurnt;
    supply.debtShares -= sharesBurnt;
```

This severely underestimated `debtReserveCache.nextDebtShares` value is then used when updating the interest rates:

[LiquidationLogic::executeLiquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161-L172)
```solidity
    _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);


    debtReserve.updateInterestRates(
      totalSupplies[params.debtAsset],
      vars.debtReserveCache,
      params.debtAsset,
      IPool(params.pool).getReserveFactor(),
      vars.actualDebtToLiquidate,
      0,
      '',
      ''
    );
```

[ReserveLogic::updateInterestRates](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L170)
```solidity
    vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex); // @audit: total debt is severely underestimated


    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
```

The result is a severe underestimation of the new interest rates, which will persist until a state changing call occurs for the affected reserve. 

## Proof of Concept
Modify the followings lines in `PoolLiquidationTests.t.sol` and run test with `forge test --mc PoolLiquidationTest --mt testLiquidationSimple1 -vvv`:

```diff
diff --git a/./test/forge/core/pool/PoolLiquidationTests.t.sol b/./test/forge/core/pool/PoolLiquidationTestst.sol
index e770be3..3a179dd 100644
--- a/./test/forge/core/pool/PoolLiquidationTests.t.sol
+++ b/./test/forge/core/pool/PoolLiquidationTests.t.sol
@@ -34,16 +34,22 @@ contract PoolLiquidationTest is PoolSetup {
     _generateLiquidationCondition();
     (, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);

+    emit log_named_uint("borrow rate before liquidation", pool.getReserveData(address(tokenB)).borrowRate);
+    emit log_named_uint("liquidity rate before liquidation", pool.getReserveData(address(tokenB)).liquidityRate);
+
     vm.startPrank(bob);
     vm.expectEmit(true, true, true, false);
     emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
-    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 10 ether);
+    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 1); // small repay for testing purposes

     vm.stopPrank();

     (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);

     assertTrue(totalDebtBase > totalDebtBaseNew);
+
+    emit log_named_uint("borrow rate after liquidation", pool.getReserveData(address(tokenB)).borrowRate);
+    emit log_named_uint("liquidity rate after liquidation", pool.getReserveData(address(tokenB)).liquidityRate);
   }

   function testLiquidationSimpleRevertWhenCollateralNotActive() external {
```

Output from logs:

```js
[PASS] testLiquidationSimple1() (gas: 1005478)
Logs:
  borrow rate before liquidation: 19858156028368794326241134
  liquidity rate before liquidation: 2647754137115839243498818
  borrow rate after liquidation: 229132
  liquidity rate after liquidation: 0
```

## Impact
Interest rates will be underestimated after every liquidation. This underestimation is more severe the smaller the `debt repaid` is in comparison to the actual `total debt` (i.e. the liquidation of small positions or partial liquidations). The interest rates will only be normalized when a state changing action occurs for the affected reserve (i.e. supply, withdraw, borrow, repay). Until that action occurs, suppliers will be losing interest. 

Therefore, suppliers are guaranteed to lose some interest after every liquidation. 

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L246

## Tool used

Manual Review

## Recommendation
`vars.debtReserveCache.nextDebtShares` should be set equal to `totalSupplies.debtShares` to reflect the actual total debt shares for the reserve. 
