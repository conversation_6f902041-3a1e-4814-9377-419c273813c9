Acrobatic Rainbow Grasshopper

High

# Wrong interest rate after a liquidation

## Summary
Wrong interest rate after a liquidation
## Vulnerability Detail
Upon a liquidation, one of the functions we call is `LiquidationLogic::_repayDebtTokens()`. There, we have the following 2 lines
```solidity
uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
vars.debtReserveCache.nextDebtShares = burnt;
```
Here is the `repayDebt()` function being called:
```solidity
  function repayDebt(
    DataTypes.PositionBalance storage self,
    DataTypes.ReserveSupplies storage supply,
    uint256 amount,
    uint128 index
  ) internal returns (uint256 sharesBurnt) {
    sharesBurnt = amount.rayDiv(index);
    require(sharesBurnt != 0, PoolErrorsLib.INVALID_BURN_AMOUNT);
    self.lastDebtLiquidtyIndex = index;
    self.debtShares -= sharesBurnt;
    supply.debtShares -= sharesBurnt;
  }
```
There we see that the value returned is the shares that are burned. However, as seen above, that amount is stored as the next debt shares:
```solidity
vars.debtReserveCache.nextDebtShares = burnt;
```
This is clearly not correct as these are the shares that were burnt, not the new shares. For example, 10 shares minus 4 burned shares equals to 6 shares but with this implementation, we will store the burned 4 shares. Then, that value is used upon calling `ReserveLogic::updateInterestRates()`:
```solidity
vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex); <@

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
        totalDebt: vars.totalDebt, <@
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );
```
Thus, the new interest rates will be wrong.
## Impact
Wrong interest rate after a liquidation
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247
## Tool used

Manual Review

## Recommendation
Use the correct value 