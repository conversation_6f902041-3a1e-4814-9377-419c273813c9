Shiny Daisy Osprey

High

# Use of incorrect NextDebtShares during liquidation will result in incorrect interest rate calculations

### Summary

During liquidation `_repayDebtTokens` sets the `nextDebtShares` to the shares burnt instead of decreasing it, this leads to incorrect interst rate calculations as the total debt (calculated as `nextDebtShares.rayMul(nextBorrowIndex)`) is actually the debt being liquidated not the total debt left.


### Root Cause

In `LiquidationLogic::_repayDebtTokens`

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246

```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
@>  vars.debtReserveCache.nextDebtShares = burnt;
  }
```
The `nextDebtShares` is set to the `burnt` shares, insread of decreasing by the burnt shares. The `nextDebtShares` is then later used in `ReserveLogic::updateInterestRates` to calculate the borrow/liquidity rates.

```solidity
  function updateInterestRates(
    DataTypes.ReserveData storage _reserve,
    DataTypes.ReserveSupplies storage totalSupplies,
    DataTypes.ReserveCache memory _cache,
    address _reserveAddress,
    uint256 _reserveFactor,
    uint256 _liquidityAdded,
    uint256 _liquidityTaken,
    bytes32 _position,
    bytes memory _data
  ) internal {
    UpdateInterestRatesLocalVars memory vars;

@>  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);

    (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
      .calculateInterestRates(
      _position,
      _data,
      DataTypes.CalculateInterestRatesParams({
        liquidityAdded: _liquidityAdded,
        liquidityTaken: _liquidityTaken,
@>      totalDebt: vars.totalDebt,
        reserveFactor: _reserveFactor,
        reserve: _reserveAddress
      })
    );

    _reserve.liquidityRate = vars.nextLiquidityRate.toUint128();
    _reserve.borrowRate = vars.nextBorrowRate.toUint128();

    if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
    else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();

    emit PoolEventsLib.ReserveDataUpdated(
      _reserveAddress, vars.nextLiquidityRate, vars.nextBorrowRate, _cache.nextLiquidityIndex, _cache.nextBorrowIndex
    );
  }
```

The `totalDebt` which is supposed to be the total debt left in the contract is now the total debt being liquidated ,this means the borrow/liquidity rates calculated will be incorrect as well.


### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

Incorrect borrow/liquidity rate calculations and as a result incorrect liquidity/borrow indexes

### PoC

_No response_

### Mitigation

```diff
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
-   vars.debtReserveCache.nextDebtShares = burnt;
+   vars.debtReserveCache.nextDebtShares -= burnt;
  }
```