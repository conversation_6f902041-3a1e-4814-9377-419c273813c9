Smooth Carbon Narwhal

High

# The interest rate for debt assets is being incorrectly updated during the liquidation process

## Summary
In the protocol, `depositors` can supply `assets` as `collateral` to the `pool` and earn `interest` from `borrowers`. 
`Borrowers`, in turn, can `borrow` `assets` and are required to pay `interest`. 
It's essential that `interest rates` are correctly calculated for both `debt` and `collateral`.
However, during `liquidation`, the `interest rate` for `debt` `assets` is updated incorrectly.
## Vulnerability Detail
When `liquidity` changes (such as during `deposits`, `repayments`, `borrowing`, or `withdrawals`), the `interest rate` adjusts. 
```solidity
function updateInterestRates(
  DataTypes.ReserveData storage _reserve,
  DataTypes.ReserveSupplies storage totalSupplies,
  DataTypes.ReserveCache memory _cache,
  address _reserveAddress,
  uint256 _reserveFactor,
  uint256 _liquidityAdded,
  uint256 _liquidityTaken,
  bytes32 _position,
  bytes memory _data
) internal {
158:  vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);
  
  (vars.nextLiquidityRate, vars.nextBorrowRate) = IReserveInterestRateStrategy(_reserve.interestRateStrategyAddress)
    .calculateInterestRates(
    _position,
    _data,
    DataTypes.CalculateInterestRatesParams({
165:      liquidityAdded: _liquidityAdded,
166:      liquidityTaken: _liquidityTaken,
      totalDebt: vars.totalDebt,
      reserveFactor: _reserveFactor,
      reserve: _reserveAddress
    })
  );

  _reserve.liquidityRate = vars.nextLiquidityRate.toUint128();
  _reserve.borrowRate = vars.nextBorrowRate.toUint128();

  if (_liquidityAdded > 0) totalSupplies.underlyingBalance += _liquidityAdded.toUint128();
  else if (_liquidityTaken > 0) totalSupplies.underlyingBalance -= _liquidityTaken.toUint128();
  );
}
```
Specifically, in `line 158`, `totalDebt` represents the current `total debt` of the `asset reserve`.
When `liquidity` increases (e.g., `deposits` or `repayments`), the `_liquidityAdded` value is positive (`line 165`). 
Conversely, when `liquidity` decreases (e.g., `borrowing` or `withdrawals`), the `_liquidityTaken` value is positive (`line 166`). 
These values are used to calculate new `interest rates` for both `debt` and `collateral`.

The `liquidation` process occurs in the `executeLiquidationCall` function. 
In `line 161`, the `_repayDebtTokens` function is called to `repay` `debt` during `liquidation`. 
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
161:  _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);
}
```
The issue arises because the `nextDebtShares` in the `cache` is updated based on the amount of `debt` burned during `repayment`, which is much smaller than the actual new `total debt shares`. 
```solidity
function _repayDebtTokens(
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  mapping(bytes32 => DataTypes.PositionBalance) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
) internal {
  uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
  vars.debtReserveCache.nextDebtShares = burnt;
}
```
The correct new `total debt shares` should be the `current total debt shares` minus the `burned amount`.
In `line 163`, the `interest rate` for the `debt asset` is updated, but this leads to incorrect values.
```solidity
function executeLiquidationCall(
  mapping(address => DataTypes.ReserveData) storage reservesData,
  mapping(uint256 => address) storage reservesList,
  mapping(address => mapping(bytes32 => DataTypes.PositionBalance)) storage balances,
  mapping(address => DataTypes.ReserveSupplies) storage totalSupplies,
  mapping(bytes32 => DataTypes.UserConfigurationMap) storage usersConfig,
  DataTypes.ExecuteLiquidationCallParams memory params
) external {
161:  _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

163:  debtReserve.updateInterestRates(
    totalSupplies[params.debtAsset],
    vars.debtReserveCache,
    params.debtAsset,
    IPool(params.pool).getReserveFactor(),
    vars.actualDebtToLiquidate,
    0,
170:    '',
171:    ''
  );
}
```
In `lines 170` and `171`, it's unclear if the intention was to pass empty data. 
However, I believe it would make more sense to pass the `position` and `interest rate data` from the parameters instead.
## Impact
`Interest rates` are a critical part of the `lending protocol`, and `liquidations` can happen frequently. 
Each time a `liquidation` occurs, the `interest rates` for `debt assets` are being incorrectly recalculated.
The `total debt` has a significant `impact` on the calculation of `interest rates`. 
Typically, when the `total debt shares` are small, the `borrowing interest rate` is lower. 
For example, if the current `total debt shares` are `100,000` and `100` `shares` are burned during `liquidation`, the system incorrectly calculates the `borrowing interest rate` as if there are only `100 shares` of `borrowing` in the `asset reserve`. 
This results in a `borrowing interest rate` that is much lower than it should be. 
As a result, current `borrowers` benefit from this artificially low `interest rate`.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L177
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L245-L246
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163
## Tool used

Manual Review

## Recommendation

```solidity
function _repayDebtTokens(
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  mapping(bytes32 => DataTypes.PositionBalance) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
) internal {
  uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);

-  vars.debtReserveCache.nextDebtShares = burnt;
+  vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;
}
```