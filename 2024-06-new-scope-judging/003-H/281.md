Jumpy Watermelon Mockingbird

Medium

# Incorrect setting of nextDebtShares leads to inaccurate interest rate updates

### Summary

In the LiquidationLogic contract, the `executeLiquidationCall()` function incorrectly sets the `nextDebtShares` value during debt token repayment. This error causes subsequent interest rate calculations to be based on incorrect data, which severely impacts the economic model of the protocol.

### Root Cause

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246

In `executeLiquidationCall()`, the function `_repayDebtTokens()` is invoked. 

```solidity
  function executeLiquidationCall(
    // params
  ) external {

    // code

   // @pattern burns the debtShares used for  internal accounting
    // @bug vars.debtReserveCache.nextDebtShares is set to amount of shares burnt, but not updated amount after burn
    _repayDebtTokens(params, vars, balances[params.debtAsset], totalSupplies[params.debtAsset]);

   // code
  }

```


In the `_repayDebtTokens()` function, the `nextDebtShares` field of the `debtReserveCache` is set to the number of `debtShares` burnt during liquidation, rather than the updated total number of debtShares in the reserve after the liquidation.


```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    vars.debtReserveCache.nextDebtShares = burnt;
  }
```

### Internal pre-conditions

1. A user must have a borrowing position that is eligible for liquidation (health factor < 1)
2. Another user must submit a liquidation call

### External pre-conditions

_No response_

### Attack Path

This is not an exploitable vulnerability, but rather a logical error in the code that leads to unintended behavior.

### Impact

The incorrect setting of `nextDebtShares` results in inaccurate data being used for interest rate calculations in the `updateInterestRate() `function. This leads to interest rates that do not accurately reflect the current state of the reserve, causing:

1. Unfavourable lending/borrowing conditions for users
2. Imbalances in the protocol's economic model
3. Increased risk of insolvency if interest rates are consistently miscalculated

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163-L172

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L171

### PoC

_No response_

### Mitigation

Modify the `_repayDebtTokens()` function to correctly update the `nextDebtShares`.


```solidity
function _repayDebtTokens(
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  mapping(bytes32 => DataTypes.PositionBalance) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
) internal {
  uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
  vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;  // Correct update
}
```