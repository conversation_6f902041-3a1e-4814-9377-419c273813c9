Innocent Yellow Cheetah

High

# Logical error causes incorrect calculation of liquidityIndex and borrowIndex

### Summary

A logical error in `LiquidationLogic.sol` leads to incorrect `liquidityIndex` and `borrowIndex` calculations, which breaks accounting and disincentivizes suppliers from lending on the protocol.

### Root Cause

In `LiquidationLogic.sol:246` the reserve's cached `nextDebtShares` are set to the burnt shares amount instead of subtracting the burnt shares from `DataTypes.ReserveSupplies debtShares`.

<https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246>

### Internal pre-conditions

1. A borrower has a health score < 1
2. A liquidator successfully calls `liquidate()` on that borrower

### External pre-conditions

N/A

### Attack Path

1. When a liquidator calls `liquidate()`, `_repayDebtTokens()` repays the borrowers debt (burning debt shares, etc.) and then updates the state of `debtReserveCache.nextDebtShares`.
2.  `debtReserveCache.nextDebtShares` is incorrectly set to the `burnt` shares. 
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247
3. Next, the `debtReserveCache` is used as a parameter in `updateInterestRates()`:
<https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L163-L172>
4. `_cache.nextDebtShares` is used to calculate `totalDebt`, which is used to calculate `borrowRate` and `liquidityRate` for the reserve: 
<https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L174>
5. In most situations, the burnt shares will likely be less than the remaining total debt shares after repayment, which means the `borrowRate` and `liquidityRate` will be much lower lower than what the protocol intended. In most situations this means that suppliers will end up receiving less interest income, which will disincentivize them from lending on the protocol. 

### Impact

Incorrectly calculating `borrowRate` and `liquidityRate` leads to incorrect `liquidityIndex` and `borrowIndex` calculations. In most situations this means that suppliers will end up receiving less interest income, which will disincentivize them from lending on the protocol.

### PoC

_No response_

### Mitigation

Update `LiquidationLogic.sol:246` to the following:
```solidity
vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares - burnt;
```