Joyous Rainbow Shark

Medium

# `LiquidationLogic::_repayDebtTokens()` incorrectly assigns burnt debt tokens to `nextDebtShares`, resulting in incorrect interest rate updates

### Summary

In the liquidation flow `_repayDebtTokens()` treats the burnt debt shares as the total remaining debt shares. This error is carried into the interest rate update calculations which depend on accurate total debt amount in order to calculate usage ratios (sometimes called utilization).

### Root Cause

`LiquidationLogic::_repayDebtTokens()` is called in the [liquidation flow](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L161) and assigns the amount of debtShares burnt to the `vars.debtReserveCache.nextDebtShares` variable:

```javascript
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
@>  vars.debtReserveCache.nextDebtShares = burnt;
  }
```

This is problematic because the liquidation flow then [passes](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L165) the `vars.debtReserveCache` object to the `ReserveLogic::updateInterestRates()` function.

The `updateInterestRate()` function then assumes `_cache.nextDebtShares` is the entire remaining debtShare balance as it is [used to calculate `vars.totalDebt`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158).

The `vars.totalDebt` is then [passed](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L167) to `calculateInterestRates()` and treated as the remaining total debt for the purposes of utilization calculations. This can lead to wild fluctuations in interest rates for all suppliers and borrowers for this reserve on the pool.

`calculateInterestRates()` is a function from an out of scope library for this audit, but the bug has already occured in a function from an in-scope contract (`_repayDebtTokens()`).

### Internal pre-conditions

1. Any position gets liquidated
2. The amount of debt shares burnt in the liquidation flow cannot equal the amount of remaining debt shares for this pool and reserve asset.

### External pre-conditions

1. Collateral price fluction makes a position liquidatable

### Attack Path

1. Alice and Bob supply to and borrow from a pool
2. The price of their collateral declines, and Alice becomes liquidatable
3. Bob liquidates Alice, burning 1e18 debt tokens in the process
4. The pool's borrow rate is incorrectly calculated (shown in POC)

### Impact

- Any interest rate strategy that depends on reserve 'utilization ratios' will be calculated incorrectly as the `vars.totalDebt` passed does not represent the total remaining debt. Note both the borrowRate and supplyRates will be affected by this issue.
- The magnitude of the impact varies based on the relative error between the debt shares burnt in a liquidation and the total debt shares remaining for that pool and reserve. 

### PoC

The below coded POC shows the following scenario:
1. Alice and Bob supply to and borrow from a pool
2. The price of their collateral declines, and Alice becomes liquidatable
3. Bob liquidates Alice, burning 1e18 debt tokens in the process
4. The pool's borrow rate is incorrectly calculated at 1.655% (noting the test interest rate settings here https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/test/forge/core/pool/CorePoolTests.sol#L65).



```javascript
// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.19;

import {PoolLiquidationTest} from './PoolLiquidationTests.t.sol';
import {console2} from 'forge-std/src/Test.sol';

contract POC_LiquidationBreaksInterestRates is PoolLiquidationTest {

    function test_POC_LiquidationBreaksInterestRate() public {
        // 1. Alice and Bob supply to and borrow from a pool
        uint256 _mintAmountA = 10e18;
        uint256 _mintAmountB = 20e18;   // Bob has extra tokenB to liquidate Alice
        uint256 _supplyAmountA = 5e18;
        uint256 _supplyAmountB = 10e18; // Bob supplies more collateral than Alice
        uint256 _borrowAmountA = 1e18; 
        uint256 _borrowAmountB = 2e18;

        _mintAndApprove(alice, tokenA, _mintAmountA, address(pool)); 
        _mintAndApprove(bob, tokenB, _mintAmountB, address(pool)); 

        vm.startPrank(bob);
        pool.supplySimple(address(tokenB), bob, _supplyAmountB, 0);
        pool.borrowSimple(address(tokenB), bob, _borrowAmountB, 0);
        vm.stopPrank();

        vm.startPrank(alice);
        pool.supplySimple(address(tokenA), alice, _supplyAmountA, 0);
        pool.borrowSimple(address(tokenB), alice, _borrowAmountA, 0);
        vm.stopPrank();

        // 2. The price of their collateral declines, and Alice becomes liquidatable
        oracleA.updateAnswer(4.5e7);
        pool.forceUpdateReserves(); 

        // 3. Bob liquidates Alice, burning 1e18 debt tokens in the process
        vm.startPrank(bob);
        uint256 preLiquidationDebtShares = pool.getTotalSupplyRaw(address(tokenB)).debtShares;
        pool.liquidateSimple(address(tokenA), address(tokenB), pos, type(uint256).max); 
        uint256 postLiquidationDebtShares = pool.getTotalSupplyRaw(address(tokenB)).debtShares;
        vm.stopPrank();
        uint256 liquidatedDebtShares = preLiquidationDebtShares - postLiquidationDebtShares;
        assert(liquidatedDebtShares == 1e18);   // Alice's entire position was liquidated

        // 4. The pool's borrow rate is incorrectly calculated at 1.655% (noting the test interest rate settings here https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/test/forge/core/pool/CorePoolTests.sol#L65)
        uint256 poolRemainingTokenBLiquidity = tokenB.balanceOf(address(pool));

        uint256 expectedTotalRemainingDebt = postLiquidationDebtShares * pool.getReserveData(address(tokenB)).borrowIndex / 1e27;
        uint256 expectedBorrowUsageRatio = 1e27 * expectedTotalRemainingDebt / (expectedTotalRemainingDebt + poolRemainingTokenBLiquidity);

        uint256 actualTotalRemainingDebt = liquidatedDebtShares * pool.getReserveData(address(tokenB)).borrowIndex / 1e27;
        uint256 actualBorrowUsageRatio = 1e27 * actualTotalRemainingDebt / (actualTotalRemainingDebt + poolRemainingTokenBLiquidity);

        uint256 tokenBBorrowRate = pool.getReserveData(address(tokenB)).borrowRate;
        vm.assertApproxEqRel(tokenBBorrowRate, actualBorrowUsageRatio * 7 / 47, 0.01e18); // As a result of the bug, the tokenBBorrowRate is 11.111% * 7% / 47% ~ 1.655%
        vm.assertApproxEqRel(0.02979e27, expectedBorrowUsageRatio * 7 / 47, 0.01e18); // But the actual useage ratio is 20% so the tokenBBorrowRate should be 20% * 7% / 47% ~ 2.979%
    }

}
```

### Mitigation

- Assign the the remaining debt shares to `vars.debtReserveCache.nextDebtShares`.