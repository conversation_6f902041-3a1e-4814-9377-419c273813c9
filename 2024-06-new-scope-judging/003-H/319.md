Clever Ebony Halibut

High

# Incorrect Debt Share Update in `_repayDebtTokens` Leads to Significant Low Interest Rate

## Summary

The `_repayDebtTokens` function in `LiquidationLogic.sol` incorrectly updates debt shares, leading to underestimating of interest rate calculations in the `updateInterestRates` function.

## Vulnerability Detail

- During the liquidation process, the protocol executes a debt repayment by calling the `_repayDebtTokens` function in `LiquidationLogic.sol`. This function is responsible for updating the `nextDebtShares` to reflect the new state of total debt shares after the repayment.

- However, the current implementation of `_repayDebtTokens` contains an error in the assignment of `nextDebtShares`. Instead of updating `nextDebtShares` to the remaining debt shares (total debt shares minus the burnt shares), the function incorrectly assigns `nextDebtShares` the value of `burnt`, which represents only the recently burnt shares.

```js
  function _repayDebtTokens(
  DataTypes.ExecuteLiquidationCallParams memory params,
  LiquidationCallLocalVars memory vars,
  mapping(bytes32 => DataTypes.PositionBalance) storage balances,
  DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
  uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);

  >> vars.debtReserveCache.nextDebtShares = burnt;
}
```

- The incorrect assignment of `nextDebtShares` leads to a distorted view of the total debt within the reserve. The protocol relies on accurate debt share calculations to determine interest rates (both liquidity and borrow rates) through the `updateInterestRates` function. By assigning `nextDebtShares` the value of `burnt`, the function effectively replaces the whole total debt shares with just the recently burnt shares, resulting in significant inaccuracies.

#### POC :

Add this test function to `PoolLiquidationTests.t.sol`.

```js
function test_liquidateUndervalueInterestRate() external {
_generateLiquidationCondition();
(, uint256 totalDebtBase,,,,) = pool.getUserAccountData(alice, 0);

    // get the interest rate before liquidation :
    uint256 liquidityRate = pool.getReserveData(address(tokenB)).liquidityRate;
    uint256 borrowRate = pool.getReserveData(address(tokenB)).borrowRate;
    vm.startPrank(bob);
    vm.expectEmit(true, true, true, false);
    emit PoolEventsLib.LiquidationCall(address(tokenA), address(tokenB), pos, 0, 0, bob);
    // liquidate a small amount of debt (dust);
    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 3);

    vm.stopPrank();
    // get interest rate after liquidation :
    uint256 liquidityRateAfter = pool.getReserveData(address(tokenB)).liquidityRate;
    uint256 borrowRateAfter = pool.getReserveData(address(tokenB)).borrowRate;
    // log the interest rates before and after liquidation :
    console.log('liquidityRateBefore :', liquidityRate);
    console.log('borrowRateBefore  :', borrowRate);
    console.log('liquidityRateAfter  :', liquidityRateAfter);
    console.log('borrowRateAfter   :', borrowRateAfter);

}
```

- console :

```sh
[PASS] test_liquidateUndervalueInterestRate() (gas: 980350)

Logs:
liquidityRateBefore : 2647754137115839243498818
borrowRateBefore : 19858156028368794326241134
liquidityRateAfter : 0
borrowRateAfter : 687398
```

## Impact

The incorrect update of `nextDebtShares` in the `_repayDebtTokens` function leads to a significant undervaluation of interest rates. This results in substantially lower liquidity and borrow rates, causing a severe loss of yield for liquidity providers.

## Code Snippet
- https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246
## Tool used

Manual Review

## Recommendation

- To fix this issue, the `_repayDebtTokens` function should update `nextDebtShares` to the remaining debt shares by subtracting the `burnt` shares

```diff
function _repayDebtTokens(
DataTypes.ExecuteLiquidationCallParams memory params,
LiquidationCallLocalVars memory vars,
mapping(bytes32 => DataTypes.PositionBalance) storage balances,
DataTypes.ReserveSupplies storage totalSupplies
) internal {
uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
- vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares - burnt;
+ vars.debtReserveCache.nextDebtShares -= burnt;
  }
```