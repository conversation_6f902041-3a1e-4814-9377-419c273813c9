Loud Rosewood Oyster

Medium

# Erroneous Debt Share Handling in Liquidation Logic

## Summary
When repaying debt during liquidation, the total debt shares used in interest rate calculation is erroneously set to the burnt debt assets, thus resulting in faulty rates calculations.
## Vulnerability Detail
[Pool::liquidate](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L130-L132) and [liquidateSimple](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/Pool.sol#L135-L137) functionalities can be called by a liquidator to liquidate unhealthy positions. 

During computation in `LiquidationLogic`, [_repayDebtTokens](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247) is called to burn the debt tokens of the position, up to the amount being repaid by the liquidator. Due to the change in the reserve asset debt, its interest rates need to be recomputed/updated. 
One of the variants used for the calculation is the reserve asset's total debt. 

If before the liquidation, the debt reserve asset debt shares is 1,000, assuming 200 of this debt is repaid, the remaining total debt is expected to be:
1,000 - 200 => 800, but currently, the asset debt shares is set to the returned burned assets. 

If for example borrow index =1.2
shares burnt = 200 / 1.2 ==> 166.6

According to the current system, the new total debt shares is set to 166.6, instead of 800
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    vars.debtReserveCache.nextDebtShares = burnt;
  }
```
This will thus go on to calculate the new interest rates using the wrong total debt shares thus resulting in the computation of the wrong liquidation and borrow rates.

[ReserveLogic::updateInterestRates](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L145-L182)
```solidity
    vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);
```

## Impact
Wrong liquidity and borrow rates will further result in incorrect liquidity and borrow index, this could result in healthy positions appearing unhealthy and could also inflate borrower's interests.
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247


## Tool used

Manual Review

## Recommendation
update [_repayDebtTokens](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239-L247):
```solidity
  function _repayDebtTokens(
    DataTypes.ExecuteLiquidationCallParams memory params,
    LiquidationCallLocalVars memory vars,
    mapping(bytes32 => DataTypes.PositionBalance) storage balances,
    DataTypes.ReserveSupplies storage totalSupplies
  ) internal {
    ++ balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
    ++ vars.debtReserveCache.nextDebtShares = totalSupplies.debtShares;
  }
```
