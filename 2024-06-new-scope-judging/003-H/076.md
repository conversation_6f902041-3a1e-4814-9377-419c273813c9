Steep Aqua Yak

High

# Incorrect total debt amount to calculate interest rate

## Summary
In the liquidation flow, a wrong assignment causes the wrong value for total debt to calculate interest rate.

## Vulnerability Detail
The function [`PositionBalanceConfiguration#repayDebt` ](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L118) returns the shares amount to be burnt. That value is [then assigned to the cached variable `vars.debtReserveCache.nextDebtShare`](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L245-L247), which is then used [for `totalDebt` to calculate interest rates at this logic](https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L171)

#### PoC
Update this test to the test file `test/core/pool/PoolLiquidationTests.t.sol`
```solidity
...
    function test_wrong_interest_rates() external {
    _generateLiquidationCondition();

    vm.startPrank(bob);

    vm.expectCall(
      address(irStrategy),
      abi.encodeCall(
        IReserveInterestRateStrategy.calculateInterestRates,
        (
          bytes32(uint256(0)),
          new bytes(0),
          DataTypes.CalculateInterestRatesParams({
            reserve: address(tokenB),
            liquidityAdded: 1,
            liquidityTaken: 0,
            reserveFactor: 0,
            totalDebt: 1
          })
        )
      )
    );
    /// Call IRStrategy with totalDebt = 1

    pool.liquidateSimple(address(tokenA), address(tokenB), pos, 1);

    vm.stopPrank();
  }
```

Run the test and console shows:
```bash
Ran 1 test for test/forge/core/pool/PoolLiquidationTests.t.sol:PoolLiquidationTest
[PASS] test_wrong_interest_rates() (gas: 1252919)
```

It means that the `totalDebt` to be used as argument for fucntion `IReserveInterestRateStrategy.calculateInterestRates` is not the actual market's total debt


## Impact
Miscalculation for interest rates can break the whole lending protocol

## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/configuration/PositionBalanceConfiguration.sol#L107-L118

https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L245-L247


https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L158-L171
## Tool used

Manual Review

## Recommendation
Assign proper value for the variable `vars.debtReserveCache.nextDebtShare`