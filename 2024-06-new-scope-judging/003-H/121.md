Powerful Licorice Elephant

Medium

# vars.debtReserveCache.nextDebtShares is updated incorrectly in the  function _repayDebtTokens.

## Summary
vars.debtReserveCache.nextDebtShares is updated to burnt which is the debt repaid amount. As a result, when collateralReserve.updateInterestRates is called to update interest rate, _reserve.liquidityRate/_reserve.borrowRate value will become huge unexpected.vars.debtReserveCache.nextDebtShares should be updated to totalSupplies.debtShares.

## root cause
 vars.debtReserveCache.nextDebtShares is updated to burnt which is the debt repaid amount.   vars.debtReserveCache.nextDebtShares should be updated to totalSupplies.debtShares.

## Vulnerability Detail
 reserve.updateInterestRates is called to update the interest rate. Here reserve.updateInterestRates takes DataTypes.ReserveCache memory cache i.e vars.debtReserveCache as input. In vars.debtReserveCache,  vars.debtReserveCache.nextDebtShares is an important value because see   function updateInterestRates(library ReserveLogic) _reserve.liquidityRate/_reserve.borrowRate is updated based on   vars.totalDebt = _cache.nextDebtShares.rayMul(_cache.nextBorrowIndex);


When function executeLiquidationCall is called which calls the   function _repayDebtTokens where  vars.debtReserveCache.nextDebtShares is updated  to burnt which is the debt repaid amount for an position’s asset which is huge small than vars.debtReserveCache.nextDebtShares. After that,  reserve.updateInterestRates is called to update the interest rate. Here interest is updated based on  vars.debtReserveCache.nextDebtShares which is  the debt repaid amount for a position's asset(which is huge smaller than vars.debtReserveCache.nextDebtShares). As a result,_reserve.liquidityRate/_reserve.borrowRate value will become huge unexpected.


In   function _repayDebtTokens,  the debt repaid amount i.e burnt shares should be decreased from vars.debtReserveCache.nextDebtShares.but here vars.debtReserveCache.nextDebtShares is updated to burnt shares which is incorrect.



## Impact
_reserve.liquidityRate/_reserve.borrowRate value will become huge unexpected
## Code Snippet
https://github.com/sherlock-audit/2024-06-new-scope/blob/main/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L246
## Tool used

Manual Review

## Recommendation
vars.debtReserveCache.nextDebtShares should be updated to totalSupplies.debtShares