Careful Fleece Pike

High

# `borrowIndex` increases faster than `liquidityIndex` will cause the borrowers overpay their debt

### Summary

The usage of `MathUtils#calculateCompoundedInterest` for borrow interest will cause `borrowIndex` to increase faster than `liquidityIndex`, which leads to the borrowers overpay their debt.

### Root Cause

The borrow interest is computed using `MathUtils#calculateCompoundedInterest` will cause `borrowIndex` to increase faster than `liquidityIndex`

https://github.com/sherlock-audit/2024-06-new-scope/blob/c8300e73f4d751796daad3dadbae4d11072b3d79/zerolend-one/contracts/core/pool/logic/ReserveLogic.sol#L235

```solidity
  function _updateIndexes(DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    // Only cumulating on the supply side if there is any income being produced
    // The case of Reserve Factor 100% is not a problem (liquidityRate == 0),
    // as liquidity index should not be updated
    if (_cache.currLiquidityRate != 0) {
      uint256 cumulatedLiquidityInterest = MathUtils.calculateLinearInterest(_cache.currLiquidityRate, _cache.reserveLastUpdateTimestamp);
      _cache.nextLiquidityIndex = cumulatedLiquidityInterest.rayMul(_cache.currLiquidityIndex).toUint128();
      _reserve.liquidityIndex = _cache.nextLiquidityIndex;
    }

    // Variable borrow index only gets updated if there is any variable debt.
    // cache.currBorrowRate != 0 is not a correct validation,
    // because a positive base variable rate can be stored on
    // cache.currBorrowRate, but the index should not increase
    if (_cache.currDebtShares != 0) {
>>    uint256 cumulatedBorrowInterest = MathUtils.calculateCompoundedInterest(_cache.currBorrowRate, _cache.reserveLastUpdateTimestamp);
      _cache.nextBorrowIndex = cumulatedBorrowInterest.rayMul(_cache.currBorrowIndex).toUint128();
      _reserve.borrowIndex = _cache.nextBorrowIndex;
    }
  }
```

### Internal pre-conditions

_No response_

### External pre-conditions

_No response_

### Attack Path

_No response_

### Impact

The borrowers overpay their debt. The overpaid funds will be stuck in the `Pool` contract, and no one can claim the overpaid funds

### PoC

Run command: `forge test --match-path test/PoC/PoC.t.sol -vv`

```solidity
// SPDX-License-Identifier: MIT
pragma solidity 0.8.19;

import {PoolSetup} from 'test/forge/core/pool/PoolSetup.sol';
import {console} from 'lib/forge-std/src/Test.sol';

contract PoC is PoolSetup {

  address alice = makeAddr('alice');
  address bob = makeAddr('bob');

  function setUp() public {
    _setUpPool();
  }

  function testPoC() external {
    console.log("Pool's reserveFactor: %e", pool.getReserveFactor());
    console.log("Pool's balance at the start: %e", tokenB.balanceOf(address(pool)));
    
    uint256 mintAmount = 10 ether;
    uint256 borrowAmount = 1 ether;

    // tokenA acts as collateral to back a loan by tokenB
    _mintAndApprove(alice, tokenA, mintAmount, address(pool));
    _mintAndApprove(alice, tokenB, mintAmount, address(pool));
    _mintAndApprove(bob, tokenB, mintAmount, address(pool));

    vm.prank(bob);
    pool.supplySimple(address(tokenB), bob, mintAmount, 0);

    vm.startPrank(alice);

    pool.supplySimple(address(tokenA), alice, mintAmount, 0);
    // Alice borrows 1e18 tokenB from Bob
    pool.borrowSimple(address(tokenB), alice, borrowAmount, 0);

    vm.stopPrank();

    skip(365 days);

    pool.forceUpdateReserves();

    console.log("liquidityIndex: %e", pool.getReserveData(address(tokenB)).liquidityIndex);
    console.log("borrowIndex: %e", pool.getReserveData(address(tokenB)).borrowIndex);

    // Alice repays the loan
    vm.prank(alice);
    pool.repaySimple(address(tokenB), type(uint256).max, 0);

    // Bob withdraws ALL tokenB
    vm.prank(bob);
    pool.withdrawSimple(address(tokenB), bob, type(uint256).max, 0);

    console.log("Pool's balance at the end: %e", tokenB.balanceOf(address(pool)));
  }
}
```
Logs:

```bash
  Pool's reserveFactor: 0e0
  Pool's balance at the start: 0e0
  liquidityIndex: 1.001489361702127659574468085e27
  borrowIndex: 1.015004526931467351340072851e27
  Pool's balance at the end: 1.10909910190755e14
```

- The pool's `reserveFactor` is zero to ensure that the protocol has no shares in the pool, meaning that the funds in the contract is just belong to users.
- At the start, the pool's balance is zero.
- Alice borrows `1e18 tokenB` from Bob.
- After a year, `borrowIndex` is greater than `liquidityIndex`, because `borrowIndex` increases faster than `liquidityIndex`.
- Alice repays Bob, and Bob withdraws ALL `token B` from the contract.
- At the end, there is some leftover funds in the contract, which is the result of Alice overpay her loan. The leftover funds will be stuck in the contract, no one can claim it.





### Mitigation

Use `MathUtils#calculateLinearInterest` to compute the borrow interest

```diff
  function _updateIndexes(DataTypes.ReserveData storage _reserve, DataTypes.ReserveCache memory _cache) internal {
    // Only cumulating on the supply side if there is any income being produced
    // The case of Reserve Factor 100% is not a problem (liquidityRate == 0),
    // as liquidity index should not be updated
    if (_cache.currLiquidityRate != 0) {
      uint256 cumulatedLiquidityInterest = MathUtils.calculateLinearInterest(_cache.currLiquidityRate, _cache.reserveLastUpdateTimestamp);
      _cache.nextLiquidityIndex = cumulatedLiquidityInterest.rayMul(_cache.currLiquidityIndex).toUint128();
      _reserve.liquidityIndex = _cache.nextLiquidityIndex;
    }

    // Variable borrow index only gets updated if there is any variable debt.
    // cache.currBorrowRate != 0 is not a correct validation,
    // because a positive base variable rate can be stored on
    // cache.currBorrowRate, but the index should not increase
    if (_cache.currDebtShares != 0) {
-      uint256 cumulatedBorrowInterest = MathUtils.calculateCompoundedInterest(_cache.currBorrowRate, _cache.reserveLastUpdateTimestamp);
+      uint256 cumulatedBorrowInterest = MathUtils.calculateLinearInterest(_cache.currBorrowRate, _cache.reserveLastUpdateTimestamp);
      _cache.nextBorrowIndex = cumulatedBorrowInterest.rayMul(_cache.currBorrowIndex).toUint128();
      _reserve.borrowIndex = _cache.nextBorrowIndex;
    }
  }
```